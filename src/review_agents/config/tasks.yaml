# 复盘任务配置

data_analysis_task:
  description: |
    执行全面的交易数据分析任务：
    1. 从MySQL数据库提取完整交易链数据
    2. 计算关键绩效指标和统计数据
    3. 分析Agent行为模式和工具使用效率
    4. 识别数据异常和质量问题
    5. 生成数据洞察和发现
  expected_output: |
    标准JSON格式输出，包含：
    - 绩效指标（胜率、盈亏比、回撤等）
    - 行为洞察（Agent决策质量、工具效率）
    - 市场洞察（不同条件下的表现）
    - 数据质量评估
  agent: data_analyst
  async_execution: false

performance_evaluation_task:
  description: |
    执行综合绩效评估任务：
    1. 基于数据分析结果评估整体表现
    2. 与基准和目标进行对比分析
    3. 进行风险调整收益评估
    4. 识别系统优势和劣势
    5. 提供绩效改进方向
  expected_output: |
    标准JSON格式输出，包含：
    - 整体评级和绝对绩效指标
    - 相对绩效对比结果
    - 风险评估和控制效果
    - 优势劣势分析
  agent: performance_evaluator
  async_execution: false
  context: [data_analysis_task]

strategy_optimization_task:
  description: |
    执行策略优化分析任务：
    1. 基于历史表现分析策略有效性
    2. 识别参数优化机会
    3. 设计具体的改进方案
    4. 提出A/B测试建议
    5. 制定实施优先级
  expected_output: |
    标准JSON格式输出，包含：
    - 当前策略表现基线
    - 具体优化建议和预期效果
    - A/B测试方案设计
    - 实施优先级排序
  agent: strategy_optimizer
  async_execution: false
  context: [data_analysis_task, performance_evaluation_task]

report_generation_task:
  description: |
    生成综合复盘报告任务：
    1. 整合所有分析和评估结果
    2. 生成执行摘要和关键发现
    3. 制定详细的行动计划
    4. 设定下期目标和监控指标
    5. 输出最终复盘报告
  expected_output: |
    完整的复盘报告，包含：
    - 执行摘要（关键指标、主要发现、核心建议）
    - 详细分析结果
    - 可执行的行动计划
    - 下期目标和监控指标
  agent: report_generator
  async_execution: false
  context: [data_analysis_task, performance_evaluation_task, strategy_optimization_task]
