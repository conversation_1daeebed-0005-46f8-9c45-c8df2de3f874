#!/usr/bin/env python3
"""
复盘调度器 - 定时执行复盘任务
每天晚上12点自动执行复盘分析
"""

import schedule
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, Any
import json
import os
from pathlib import Path

from .crew import ReviewAgents
from ..gold_agents.database.trading_chain_manager import TradingChainManager


class ReviewScheduler:
    """复盘调度器"""
    
    def __init__(self, schedule_time: str = "00:00"):
        """初始化调度器
        
        Args:
            schedule_time: 执行时间，格式为 "HH:MM"，默认为午夜12点
        """
        self.schedule_time = schedule_time
        self.logger = self._setup_logger()
        self.review_agents = None
        self.trading_chain_manager = None
        self.reports_dir = Path("reports")
        self.reports_dir.mkdir(exist_ok=True)
        
        # 设置调度任务
        self._setup_schedule()
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(f"{self.__class__.__name__}")
        
        if not logger.handlers:
            # 创建logs目录
            log_dir = Path("logs")
            log_dir.mkdir(exist_ok=True)
            
            # 文件处理器
            file_handler = logging.FileHandler(
                log_dir / f"review_scheduler_{datetime.now().strftime('%Y%m%d')}.log",
                encoding='utf-8'
            )
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            ))
            
            # 控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s'
            ))
            
            logger.addHandler(file_handler)
            logger.addHandler(console_handler)
            logger.setLevel(logging.INFO)
            
        return logger
    
    def _setup_schedule(self):
        """设置调度任务"""
        try:
            # 每天指定时间执行复盘
            schedule.every().day.at(self.schedule_time).do(self._execute_daily_review)
            
            # 每周一执行周度复盘
            schedule.every().monday.at(self.schedule_time).do(self._execute_weekly_review)
            
            # 每月1号执行月度复盘
            schedule.every().day.at(self.schedule_time).do(self._check_monthly_review)
            
            self.logger.info(f"复盘调度器已设置 - 执行时间: {self.schedule_time}")
            
        except Exception as e:
            self.logger.error(f"设置调度任务失败: {str(e)}")
            raise
    
    def _init_components(self):
        """初始化组件"""
        try:
            if self.review_agents is None:
                self.review_agents = ReviewAgents()
                self.logger.info("复盘Agent团队已初始化")
            
            if self.trading_chain_manager is None:
                self.trading_chain_manager = TradingChainManager()
                self.logger.info("交易链管理器已初始化")
                
        except Exception as e:
            self.logger.error(f"初始化组件失败: {str(e)}")
            raise
    
    def _execute_daily_review(self):
        """执行日度复盘"""
        try:
            self.logger.info("开始执行日度复盘...")
            
            # 初始化组件
            self._init_components()
            
            # 获取昨天的日期
            yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
            
            # 检查是否有昨天的交易数据
            if not self._has_trading_data(yesterday, yesterday):
                self.logger.info(f"昨天({yesterday})没有交易数据，跳过复盘")
                return
            
            # 执行复盘分析
            review_result = self.review_agents.execute_review(f"{yesterday}_daily")
            
            if review_result["status"] == "success":
                # 保存复盘报告
                report_path = self._save_review_report(review_result, "daily", yesterday)
                self.logger.info(f"日度复盘完成，报告已保存: {report_path}")
                
                # 发送通知（如果需要）
                self._send_notification("日度复盘完成", review_result)
                
            else:
                self.logger.error(f"日度复盘失败: {review_result.get('message', '未知错误')}")
                
        except Exception as e:
            self.logger.error(f"执行日度复盘失败: {str(e)}")
    
    def _execute_weekly_review(self):
        """执行周度复盘"""
        try:
            self.logger.info("开始执行周度复盘...")
            
            # 初始化组件
            self._init_components()
            
            # 获取上周的日期范围
            today = datetime.now()
            last_monday = today - timedelta(days=today.weekday() + 7)
            last_sunday = last_monday + timedelta(days=6)
            
            start_date = last_monday.strftime('%Y-%m-%d')
            end_date = last_sunday.strftime('%Y-%m-%d')
            
            # 检查是否有上周的交易数据
            if not self._has_trading_data(start_date, end_date):
                self.logger.info(f"上周({start_date} to {end_date})没有交易数据，跳过复盘")
                return
            
            # 执行复盘分析
            review_result = self.review_agents.execute_review(f"{start_date}_to_{end_date}_weekly")
            
            if review_result["status"] == "success":
                # 保存复盘报告
                report_path = self._save_review_report(review_result, "weekly", f"{start_date}_to_{end_date}")
                self.logger.info(f"周度复盘完成，报告已保存: {report_path}")
                
                # 发送通知
                self._send_notification("周度复盘完成", review_result)
                
            else:
                self.logger.error(f"周度复盘失败: {review_result.get('message', '未知错误')}")
                
        except Exception as e:
            self.logger.error(f"执行周度复盘失败: {str(e)}")
    
    def _check_monthly_review(self):
        """检查是否需要执行月度复盘"""
        try:
            today = datetime.now()
            
            # 只在每月1号执行
            if today.day == 1:
                self._execute_monthly_review()
                
        except Exception as e:
            self.logger.error(f"检查月度复盘失败: {str(e)}")
    
    def _execute_monthly_review(self):
        """执行月度复盘"""
        try:
            self.logger.info("开始执行月度复盘...")
            
            # 初始化组件
            self._init_components()
            
            # 获取上个月的日期范围
            today = datetime.now()
            first_day_this_month = today.replace(day=1)
            last_day_last_month = first_day_this_month - timedelta(days=1)
            first_day_last_month = last_day_last_month.replace(day=1)
            
            start_date = first_day_last_month.strftime('%Y-%m-%d')
            end_date = last_day_last_month.strftime('%Y-%m-%d')
            
            # 检查是否有上个月的交易数据
            if not self._has_trading_data(start_date, end_date):
                self.logger.info(f"上个月({start_date} to {end_date})没有交易数据，跳过复盘")
                return
            
            # 执行复盘分析
            review_result = self.review_agents.execute_review(f"{start_date}_to_{end_date}_monthly")
            
            if review_result["status"] == "success":
                # 保存复盘报告
                report_path = self._save_review_report(review_result, "monthly", f"{start_date}_to_{end_date}")
                self.logger.info(f"月度复盘完成，报告已保存: {report_path}")
                
                # 发送通知
                self._send_notification("月度复盘完成", review_result)
                
            else:
                self.logger.error(f"月度复盘失败: {review_result.get('message', '未知错误')}")
                
        except Exception as e:
            self.logger.error(f"执行月度复盘失败: {str(e)}")
    
    def _has_trading_data(self, start_date: str, end_date: str) -> bool:
        """检查指定期间是否有交易数据"""
        try:
            with self.trading_chain_manager.session_scope() as session:
                from sqlalchemy import text
                
                query = text("""
                    SELECT COUNT(*) as count
                    FROM trading_sessions 
                    WHERE DATE(start_time) BETWEEN :start_date AND :end_date
                    AND status = 'completed'
                """)
                
                result = session.execute(query, {
                    'start_date': start_date,
                    'end_date': end_date
                }).fetchone()
                
                return result.count > 0 if result else False
                
        except Exception as e:
            self.logger.error(f"检查交易数据失败: {str(e)}")
            return False
    
    def _save_review_report(self, review_result: Dict[str, Any], report_type: str, period: str) -> str:
        """保存复盘报告"""
        try:
            # 创建报告文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{report_type}_review_{period}_{timestamp}.json"
            report_path = self.reports_dir / filename
            
            # 保存报告
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(review_result, f, indent=2, ensure_ascii=False, default=str)
            
            return str(report_path)
            
        except Exception as e:
            self.logger.error(f"保存复盘报告失败: {str(e)}")
            raise
    
    def _send_notification(self, title: str, content: Dict[str, Any]):
        """发送通知（可以扩展为邮件、微信等）"""
        try:
            # 这里可以集成邮件、微信、钉钉等通知方式
            self.logger.info(f"通知: {title}")
            
            # 简单的日志通知
            if content.get("status") == "success":
                self.logger.info("复盘分析成功完成")
            else:
                self.logger.warning(f"复盘分析失败: {content.get('message', '未知错误')}")
                
        except Exception as e:
            self.logger.error(f"发送通知失败: {str(e)}")
    
    def start(self):
        """启动调度器"""
        try:
            self.logger.info("复盘调度器已启动")
            self.logger.info(f"下次执行时间: {self.schedule_time}")
            
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
                
        except KeyboardInterrupt:
            self.logger.info("收到停止信号，正在关闭调度器...")
            self.stop()
        except Exception as e:
            self.logger.error(f"调度器运行失败: {str(e)}")
            raise
    
    def stop(self):
        """停止调度器"""
        try:
            # 清理资源
            if self.review_agents:
                self.review_agents.close()
            
            # 清除调度任务
            schedule.clear()
            
            self.logger.info("复盘调度器已停止")
            
        except Exception as e:
            self.logger.error(f"停止调度器失败: {str(e)}")
    
    def execute_manual_review(self, start_date: str, end_date: str, review_type: str = "manual") -> Dict[str, Any]:
        """手动执行复盘"""
        try:
            self.logger.info(f"开始手动执行复盘: {start_date} to {end_date}")
            
            # 初始化组件
            self._init_components()
            
            # 检查是否有交易数据
            if not self._has_trading_data(start_date, end_date):
                return {
                    "status": "no_data",
                    "message": f"指定期间({start_date} to {end_date})没有交易数据"
                }
            
            # 执行复盘分析
            review_result = self.review_agents.execute_review(f"{start_date}_to_{end_date}_{review_type}")
            
            if review_result["status"] == "success":
                # 保存复盘报告
                report_path = self._save_review_report(review_result, review_type, f"{start_date}_to_{end_date}")
                review_result["report_path"] = report_path
                self.logger.info(f"手动复盘完成，报告已保存: {report_path}")
            
            return review_result
            
        except Exception as e:
            self.logger.error(f"手动执行复盘失败: {str(e)}")
            return {
                "status": "error",
                "message": str(e)
            }


def main():
    """主函数 - 启动复盘调度器"""
    import argparse
    
    parser = argparse.ArgumentParser(description="复盘调度器")
    parser.add_argument("--time", default="00:00", help="执行时间 (HH:MM)")
    parser.add_argument("--manual", action="store_true", help="手动执行复盘")
    parser.add_argument("--start-date", help="开始日期 (YYYY-MM-DD)")
    parser.add_argument("--end-date", help="结束日期 (YYYY-MM-DD)")
    
    args = parser.parse_args()
    
    scheduler = ReviewScheduler(schedule_time=args.time)
    
    if args.manual:
        if not args.start_date or not args.end_date:
            print("手动执行需要指定开始和结束日期")
            return
        
        result = scheduler.execute_manual_review(args.start_date, args.end_date)
        print(f"手动复盘结果: {result}")
    else:
        # 启动定时调度器
        scheduler.start()


if __name__ == '__main__':
    main()
