#!/usr/bin/env python3
"""
复盘Agent团队 - 独立的复盘分析系统
"""

from crewai import Agent, Crew, Process, Task, LLM
from crewai.project import CrewBase, agent, crew, task
from .tools.trading_data_analyzer import TradingDataAnalyzer
from .tools.review_result_manager import ReviewResultManager
from datetime import datetime
import logging


@CrewBase
class ReviewAgents():
    """独立的复盘Agent团队"""
    agents_config = 'config/agents.yaml'
    tasks_config = 'config/tasks.yaml'

    def __init__(self):
        """初始化复盘团队"""
        self.logger = logging.getLogger(self.__class__.__name__)
        self._init_tools()
        self.logger.info("复盘Agent团队初始化完成")

    def _init_tools(self):
        """初始化复盘工具"""
        try:
            self.data_analyzer = TradingDataAnalyzer()
            self.result_manager = ReviewResultManager()
            self.result_manager.init_db()
            self.logger.info("复盘工具初始化完成")
        except Exception as e:
            self.logger.error(f"复盘工具初始化失败: {str(e)}")
            raise

    @agent
    def data_analyst(self) -> Agent:
        """数据分析师Agent - 负责交易数据分析"""
        return Agent(
            config=self.agents_config['data_analyst'],
            tools=[
                self.data_analyzer
            ],
            verbose=True,
            allow_delegation=False,
            max_iter=2,
            memory=True
        )

    @agent
    def performance_evaluator(self) -> Agent:
        """绩效评估师Agent - 负责绩效评估"""
        return Agent(
            config=self.agents_config['performance_evaluator'],
            tools=[
                self.data_analyzer
            ],
            verbose=True,
            allow_delegation=False,
            max_iter=2,
            memory=True
        )

    @agent
    def strategy_optimizer(self) -> Agent:
        """策略优化师Agent - 负责策略优化建议"""
        return Agent(
            config=self.agents_config['strategy_optimizer'],
            tools=[
                self.data_analyzer
            ],
            verbose=True,
            allow_delegation=False,
            max_iter=2,
            memory=True
        )

    @agent
    def report_generator(self) -> Agent:
        """报告生成器Agent - 负责生成最终报告"""
        return Agent(
            config=self.agents_config['report_generator'],
            tools=[],  # 主要整合其他Agent的结果
            verbose=True,
            allow_delegation=False,
            max_iter=1,
            memory=True
        )

    @task
    def data_analysis_task(self) -> Task:
        """数据分析任务"""
        return Task(
            config=self.tasks_config['data_analysis_task'],
            agent=self.data_analyst()
        )

    @task
    def performance_evaluation_task(self) -> Task:
        """绩效评估任务"""
        return Task(
            config=self.tasks_config['performance_evaluation_task'],
            agent=self.performance_evaluator()
        )

    @task
    def strategy_optimization_task(self) -> Task:
        """策略优化任务"""
        return Task(
            config=self.tasks_config['strategy_optimization_task'],
            agent=self.strategy_optimizer()
        )

    @task
    def report_generation_task(self) -> Task:
        """报告生成任务"""
        return Task(
            config=self.tasks_config['report_generation_task'],
            agent=self.report_generator()
        )

    @crew
    def crew(self) -> Crew:
        """创建复盘Agent团队"""
        return Crew(
            agents=self.agents,
            tasks=self.tasks,
            process=Process.sequential,
            verbose=True,
            max_rpm=3,
            memory=True,
            planning=True
        )

    def execute_review(self, review_period: str = None) -> dict:
        """执行复盘分析"""
        try:
            # 准备输入参数
            inputs = {
                "review_period": review_period or "last_30_days",
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            self.logger.info(f"开始执行复盘分析 - 期间: {inputs['review_period']}")

            # 执行复盘任务
            crew_result = self.crew().kickoff(inputs=inputs)

            # 构建完整的复盘结果
            review_result = {
                "status": "success",
                "review_period": inputs["review_period"],
                "review_result": crew_result,
                "timestamp": inputs["timestamp"]
            }

            # 保存复盘结果到数据库
            report_id = self.result_manager.save_review_report(review_result)
            review_result["report_id"] = report_id

            self.logger.info(f"复盘分析执行完成，报告ID: {report_id}")

            return review_result

        except Exception as e:
            self.logger.error(f"复盘分析执行失败: {str(e)}")
            return {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

    def get_review_metrics(self) -> dict:
        """获取复盘系统指标"""
        try:
            metrics = {
                "tools_metrics": {},
                "system_status": "healthy",
                "last_review": "unknown"
            }
            
            # 收集工具性能指标
            if hasattr(self.data_analyzer, 'get_metrics'):
                metrics["tools_metrics"]["data_analyzer"] = self.data_analyzer.get_metrics()
            
            return metrics
        except Exception as e:
            self.logger.error(f"获取复盘指标失败: {str(e)}")
            return {"error": str(e)}

    def close(self):
        """关闭资源"""
        try:
            if hasattr(self.data_analyzer, 'close'):
                self.data_analyzer.close()
            
            self.logger.info("复盘系统资源已关闭")
        except Exception as e:
            self.logger.error(f"关闭复盘资源失败: {str(e)}")


if __name__ == '__main__':
    # 测试复盘Agent团队
    review_agents = ReviewAgents()
    
    # 执行复盘分析
    result = review_agents.execute_review("2024-01-01_to_2024-01-31")
    print("复盘结果:", result)
    
    # 获取系统指标
    metrics = review_agents.get_review_metrics()
    print("复盘指标:", metrics)
    
    # 关闭资源
    review_agents.close()
