#!/usr/bin/env python3
"""
交易数据分析工具 - 从MySQL数据库分析完整交易链数据
"""

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import pandas as pd
import numpy as np
from pydantic import BaseModel, Field
from crewai_tools import BaseTool
import yaml
import os


class TradingDataAnalyzerSchema(BaseModel):
    """交易数据分析工具的输入参数模式"""
    action: str = Field(..., description="操作类型：analyze_performance, analyze_behavior, analyze_market")
    start_date: Optional[str] = Field(None, description="开始日期 YYYY-MM-DD")
    end_date: Optional[str] = Field(None, description="结束日期 YYYY-MM-DD")
    session_ids: Optional[List[str]] = Field(None, description="特定会话ID列表")


class TradingDataAnalyzer(BaseTool):
    """交易数据分析工具"""
    name: str = "TradingDataAnalyzer"
    description: str = """
    这是一个用于分析交易链数据的工具。
    主要功能：
    1. 从MySQL数据库提取交易数据
    2. 计算关键绩效指标
    3. 分析Agent行为模式
    4. 评估工具使用效率
    
    使用示例：
    1. 绩效分析：
       tool.run(action="analyze_performance", start_date="2024-01-01", end_date="2024-01-31")
    
    2. 行为分析：
       tool.run(action="analyze_behavior", session_ids=["session1", "session2"])
    """
    args_schema: type[BaseModel] = TradingDataAnalyzerSchema

    def __init__(self, **kwargs):
        """初始化交易数据分析工具"""
        super().__init__(**kwargs)
        self.engine = self._create_engine()
        self.Session = sessionmaker(bind=self.engine)

    def _create_engine(self):
        """创建数据库引擎"""
        try:
            # 加载配置
            config_path = os.path.join(os.path.dirname(__file__), '../../gold_agents/config/trader_config.yaml')
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            db_config = config['database']
            db_url = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['name']}"
            return create_engine(db_url, echo=False)
        except Exception as e:
            # 使用默认配置
            db_url = "mysql+pymysql://Mysql5.7:a78d04a8027589c3@*************:6221/Mysql5.7"
            return create_engine(db_url, echo=False)

    def _run(self, **kwargs: Any) -> Dict[str, Any]:
        """执行数据分析"""
        try:
            action = kwargs.get('action', 'analyze_performance')
            
            if action == 'analyze_performance':
                return self._analyze_performance(kwargs)
            elif action == 'analyze_behavior':
                return self._analyze_behavior(kwargs)
            elif action == 'analyze_market':
                return self._analyze_market(kwargs)
            else:
                return {
                    "status": "error",
                    "message": f"不支持的操作类型: {action}",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
        except Exception as e:
            return {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

    def _analyze_performance(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """分析交易绩效"""
        try:
            start_date = params.get('start_date', (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
            end_date = params.get('end_date', datetime.now().strftime('%Y-%m-%d'))
            
            with self.Session() as session:
                # 获取交易会话数据
                sessions_query = text("""
                    SELECT * FROM trading_sessions 
                    WHERE DATE(start_time) BETWEEN :start_date AND :end_date
                    AND status = 'completed'
                """)
                sessions_df = pd.read_sql(sessions_query, session.bind, params={
                    'start_date': start_date,
                    'end_date': end_date
                })
                
                if sessions_df.empty:
                    return {
                        "status": "success",
                        "message": "指定期间内没有完成的交易会话",
                        "performance_metrics": {},
                        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                
                # 获取交易决策数据
                decisions_query = text("""
                    SELECT td.*, ts.start_time as session_start
                    FROM trading_decisions td
                    JOIN trading_sessions ts ON td.session_id = ts.id
                    WHERE DATE(ts.start_time) BETWEEN :start_date AND :end_date
                    AND td.execution_status = 'executed'
                """)
                decisions_df = pd.read_sql(decisions_query, session.bind, params={
                    'start_date': start_date,
                    'end_date': end_date
                })
                
                # 计算绩效指标
                performance_metrics = self._calculate_performance_metrics(sessions_df, decisions_df)
                
                return {
                    "status": "success",
                    "analysis_period": f"{start_date} to {end_date}",
                    "performance_metrics": performance_metrics,
                    "sessions_analyzed": len(sessions_df),
                    "trades_analyzed": len(decisions_df),
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
        except Exception as e:
            raise Exception(f"绩效分析失败: {str(e)}")

    def _calculate_performance_metrics(self, sessions_df: pd.DataFrame, decisions_df: pd.DataFrame) -> Dict[str, Any]:
        """计算绩效指标"""
        metrics = {}
        
        if not sessions_df.empty:
            # 基础指标
            total_sessions = len(sessions_df)
            total_pnl = sessions_df['total_pnl'].sum()
            total_trades = sessions_df['total_trades'].sum()
            winning_trades = sessions_df['winning_trades'].sum()
            
            # 计算胜率
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            # 计算收益率
            initial_balance = sessions_df['initial_balance'].iloc[0] if len(sessions_df) > 0 else 10000
            total_return = total_pnl / initial_balance if initial_balance > 0 else 0
            
            # 计算最大回撤
            max_drawdown = sessions_df['max_drawdown'].max() if 'max_drawdown' in sessions_df.columns else 0
            
            # 计算盈利因子
            profits = sessions_df[sessions_df['total_pnl'] > 0]['total_pnl']
            losses = sessions_df[sessions_df['total_pnl'] < 0]['total_pnl']
            profit_factor = profits.sum() / abs(losses.sum()) if len(losses) > 0 and losses.sum() != 0 else 0
            
            # 计算夏普比率（简化版）
            returns = sessions_df['total_pnl'] / sessions_df['initial_balance']
            sharpe_ratio = returns.mean() / returns.std() if returns.std() > 0 else 0
            
            metrics = {
                "total_sessions": int(total_sessions),
                "total_trades": int(total_trades),
                "winning_trades": int(winning_trades),
                "win_rate": float(win_rate),
                "total_pnl": float(total_pnl),
                "total_return": float(total_return),
                "max_drawdown": float(max_drawdown),
                "profit_factor": float(profit_factor),
                "sharpe_ratio": float(sharpe_ratio),
                "avg_trade_pnl": float(total_pnl / total_trades) if total_trades > 0 else 0,
                "avg_session_duration": self._calculate_avg_duration(sessions_df)
            }
        
        return metrics

    def _calculate_avg_duration(self, sessions_df: pd.DataFrame) -> float:
        """计算平均会话持续时间（小时）"""
        try:
            sessions_df['start_time'] = pd.to_datetime(sessions_df['start_time'])
            sessions_df['end_time'] = pd.to_datetime(sessions_df['end_time'])
            durations = (sessions_df['end_time'] - sessions_df['start_time']).dt.total_seconds() / 3600
            return float(durations.mean()) if len(durations) > 0 else 0
        except:
            return 0.0

    def _analyze_behavior(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """分析Agent行为模式"""
        try:
            session_ids = params.get('session_ids', [])
            
            with self.Session() as session:
                # 构建查询条件
                if session_ids:
                    session_filter = "AND ae.session_id IN :session_ids"
                    query_params = {'session_ids': tuple(session_ids)}
                else:
                    # 默认分析最近30天的数据
                    session_filter = "AND ae.start_time >= :start_date"
                    query_params = {'start_date': datetime.now() - timedelta(days=30)}
                
                # 获取Agent执行数据
                agent_query = text(f"""
                    SELECT ae.*, ts.trading_pair
                    FROM agent_executions ae
                    JOIN trading_sessions ts ON ae.session_id = ts.id
                    WHERE ae.success = 1 {session_filter}
                    ORDER BY ae.start_time
                """)
                
                agent_df = pd.read_sql(agent_query, session.bind, params=query_params)
                
                if agent_df.empty:
                    return {
                        "status": "success",
                        "message": "没有找到Agent执行数据",
                        "behavioral_insights": [],
                        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                
                # 获取工具执行数据
                tool_query = text(f"""
                    SELECT te.*, ae.agent_name
                    FROM tool_executions te
                    JOIN agent_executions ae ON te.agent_execution_id = ae.id
                    WHERE te.success = 1 {session_filter.replace('ae.', 'ae.')}
                    ORDER BY te.start_time
                """)
                
                tool_df = pd.read_sql(tool_query, session.bind, params=query_params)
                
                # 分析Agent行为
                behavioral_insights = self._analyze_agent_behavior(agent_df, tool_df)
                
                return {
                    "status": "success",
                    "behavioral_insights": behavioral_insights,
                    "agents_analyzed": agent_df['agent_name'].nunique(),
                    "executions_analyzed": len(agent_df),
                    "tools_analyzed": len(tool_df),
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
        except Exception as e:
            raise Exception(f"行为分析失败: {str(e)}")

    def _analyze_agent_behavior(self, agent_df: pd.DataFrame, tool_df: pd.DataFrame) -> List[str]:
        """分析Agent行为模式"""
        insights = []
        
        # 分析Agent执行效率
        agent_stats = agent_df.groupby('agent_name').agg({
            'execution_time': ['mean', 'std', 'count'],
            'success': 'mean'
        }).round(3)
        
        for agent_name in agent_stats.index:
            avg_time = agent_stats.loc[agent_name, ('execution_time', 'mean')]
            success_rate = agent_stats.loc[agent_name, ('success', 'mean')]
            execution_count = agent_stats.loc[agent_name, ('execution_time', 'count')]
            
            insights.append(f"{agent_name}: 平均执行时间{avg_time:.2f}秒, 成功率{success_rate:.2%}, 执行次数{execution_count}")
            
            if avg_time > 5.0:
                insights.append(f"{agent_name}执行时间偏长，建议优化")
            if success_rate < 0.95:
                insights.append(f"{agent_name}成功率偏低，需要检查")
        
        # 分析工具使用模式
        if not tool_df.empty:
            tool_stats = tool_df.groupby(['agent_name', 'tool_name']).agg({
                'execution_time': 'mean',
                'success': 'mean'
            }).round(3)
            
            # 找出最常用的工具
            tool_usage = tool_df['tool_name'].value_counts()
            most_used_tool = tool_usage.index[0] if len(tool_usage) > 0 else "无"
            insights.append(f"最常用工具: {most_used_tool} (使用{tool_usage.iloc[0]}次)")
            
            # 找出效率最低的工具
            slow_tools = tool_df.groupby('tool_name')['execution_time'].mean().sort_values(ascending=False)
            if len(slow_tools) > 0:
                slowest_tool = slow_tools.index[0]
                insights.append(f"响应最慢工具: {slowest_tool} (平均{slow_tools.iloc[0]:.2f}秒)")
        
        return insights

    def _analyze_market(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """分析市场条件下的表现"""
        try:
            start_date = params.get('start_date', (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
            end_date = params.get('end_date', datetime.now().strftime('%Y-%m-%d'))
            
            with self.Session() as session:
                # 获取交易决策和市场数据
                market_query = text("""
                    SELECT td.*, ts.start_time, ts.total_pnl,
                           HOUR(ts.start_time) as trade_hour,
                           DAYOFWEEK(ts.start_time) as trade_day
                    FROM trading_decisions td
                    JOIN trading_sessions ts ON td.session_id = ts.id
                    WHERE DATE(ts.start_time) BETWEEN :start_date AND :end_date
                    AND td.execution_status = 'executed'
                """)
                
                market_df = pd.read_sql(market_query, session.bind, params={
                    'start_date': start_date,
                    'end_date': end_date
                })
                
                if market_df.empty:
                    return {
                        "status": "success",
                        "message": "没有找到市场数据",
                        "market_insights": [],
                        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                
                # 分析市场模式
                market_insights = self._analyze_market_patterns(market_df)
                
                return {
                    "status": "success",
                    "market_insights": market_insights,
                    "trades_analyzed": len(market_df),
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
        except Exception as e:
            raise Exception(f"市场分析失败: {str(e)}")

    def _analyze_market_patterns(self, market_df: pd.DataFrame) -> List[str]:
        """分析市场模式"""
        insights = []
        
        # 分析时间段表现
        if 'trade_hour' in market_df.columns:
            hourly_performance = market_df.groupby('trade_hour').agg({
                'signal_strength': 'mean',
                'confidence_level': 'mean'
            }).round(3)
            
            best_hour = hourly_performance['signal_strength'].idxmax()
            worst_hour = hourly_performance['signal_strength'].idxmin()
            
            insights.append(f"最佳交易时段: {best_hour}点 (平均信号强度{hourly_performance.loc[best_hour, 'signal_strength']:.3f})")
            insights.append(f"最差交易时段: {worst_hour}点 (平均信号强度{hourly_performance.loc[worst_hour, 'signal_strength']:.3f})")
        
        # 分析星期表现
        if 'trade_day' in market_df.columns:
            daily_performance = market_df.groupby('trade_day').agg({
                'signal_strength': 'mean',
                'confidence_level': 'mean'
            }).round(3)
            
            day_names = {1: '周日', 2: '周一', 3: '周二', 4: '周三', 5: '周四', 6: '周五', 7: '周六'}
            best_day = daily_performance['signal_strength'].idxmax()
            insights.append(f"最佳交易日: {day_names.get(best_day, best_day)} (平均信号强度{daily_performance.loc[best_day, 'signal_strength']:.3f})")
        
        # 分析信号强度分布
        if 'signal_strength' in market_df.columns:
            strong_signals = len(market_df[market_df['signal_strength'] >= 0.8])
            total_signals = len(market_df)
            strong_signal_ratio = strong_signals / total_signals if total_signals > 0 else 0
            
            insights.append(f"强信号比例: {strong_signal_ratio:.2%} ({strong_signals}/{total_signals})")
            
            if strong_signal_ratio < 0.3:
                insights.append("强信号比例偏低，建议优化策略参数")
        
        return insights


if __name__ == '__main__':
    # 测试交易数据分析工具
    analyzer = TradingDataAnalyzer()
    
    # 测试绩效分析
    result = analyzer.run(
        action="analyze_performance",
        start_date="2024-01-01",
        end_date="2024-01-31"
    )
    print("绩效分析结果:", result)
