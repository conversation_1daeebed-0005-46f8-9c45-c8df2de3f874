#!/usr/bin/env python3
"""
复盘结果管理器 - 保存复盘结果并提供给交易团队
"""

from sqlalchemy import create_engine, Column, String, Text, Float, Integer, DateTime, Boolean, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, scoped_session
from contextlib import contextmanager
from datetime import datetime
from typing import Dict, Any, Optional, List
import json
import uuid
import yaml
import os

Base = declarative_base()

class ReviewReport(Base):
    """复盘报告表"""
    __tablename__ = 'review_reports'
    
    id = Column(String(36), primary_key=True)  # UUID
    report_type = Column(String(20), nullable=False)  # daily, weekly, monthly, manual
    review_period_start = Column(DateTime, nullable=False)
    review_period_end = Column(DateTime, nullable=False)
    
    # 分析结果
    data_analysis_result = Column(JSON)  # 数据分析师结果
    performance_evaluation_result = Column(JSON)  # 绩效评估师结果
    strategy_optimization_result = Column(JSON)  # 策略优化师结果
    final_report = Column(JSON)  # 最终综合报告
    
    # 关键指标
    win_rate = Column(Float)
    profit_factor = Column(Float)
    max_drawdown = Column(Float)
    total_return = Column(Float)
    sharpe_ratio = Column(Float)
    
    # 改进建议
    key_insights = Column(JSON)  # 关键洞察
    improvement_suggestions = Column(JSON)  # 改进建议
    strategy_adjustments = Column(JSON)  # 策略调整建议
    
    # 状态管理
    status = Column(String(20), default='pending')  # pending, completed, applied, archived
    applied_to_trading = Column(Boolean, default=False)  # 是否已应用到交易系统
    application_time = Column(DateTime)  # 应用时间
    
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)


class StrategyAdjustment(Base):
    """策略调整记录表"""
    __tablename__ = 'strategy_adjustments'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    review_report_id = Column(String(36), nullable=False)
    
    adjustment_type = Column(String(50), nullable=False)  # parameter, rule, threshold
    component = Column(String(50), nullable=False)  # trader, risk_controller, etc.
    parameter_name = Column(String(100), nullable=False)
    
    old_value = Column(Text)  # 原值
    new_value = Column(Text)  # 新值
    reason = Column(Text)  # 调整原因
    expected_impact = Column(Text)  # 预期影响
    
    status = Column(String(20), default='pending')  # pending, applied, reverted
    applied_at = Column(DateTime)
    
    created_at = Column(DateTime, default=datetime.now)


class ReviewResultManager:
    """复盘结果管理器"""
    
    def __init__(self, config_path: str = None):
        """初始化复盘结果管理器"""
        self.config = self._load_config(config_path)
        self.engine = self._create_engine()
        self.Session = scoped_session(sessionmaker(
            bind=self.engine,
            expire_on_commit=False
        ))
        
    def _load_config(self, config_path: Optional[str] = None) -> Dict[str, Any]:
        """加载配置文件"""
        if config_path is None:
            config_path = os.path.join(os.path.dirname(__file__), '../../gold_agents/config/trader_config.yaml')
            
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            # 使用默认配置
            return {
                'database': {
                    'host': '*************',
                    'port': '6221',
                    'user': 'Mysql5.7',
                    'password': 'a78d04a8027589c3',
                    'name': 'Mysql5.7'
                }
            }
    
    def _create_engine(self):
        """创建数据库引擎"""
        db_config = self.config['database']
        db_url = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['name']}"
        return create_engine(db_url, echo=False)
    
    def init_db(self):
        """初始化数据库表"""
        Base.metadata.create_all(self.engine)
    
    @contextmanager
    def session_scope(self):
        """提供数据库会话的上下文管理器"""
        session = self.Session()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()
    
    def save_review_report(self, review_result: Dict[str, Any]) -> str:
        """保存复盘报告"""
        try:
            report_id = str(uuid.uuid4())
            
            with self.session_scope() as session:
                # 解析复盘结果
                final_report = review_result.get('review_result', {})
                
                # 提取关键指标
                performance_metrics = self._extract_performance_metrics(review_result)
                
                # 提取改进建议
                insights_and_suggestions = self._extract_insights_and_suggestions(review_result)
                
                review_report = ReviewReport(
                    id=report_id,
                    report_type=self._determine_report_type(review_result),
                    review_period_start=self._parse_period_start(review_result),
                    review_period_end=self._parse_period_end(review_result),
                    data_analysis_result=review_result.get('data_analysis', {}),
                    performance_evaluation_result=review_result.get('performance_evaluation', {}),
                    strategy_optimization_result=review_result.get('strategy_optimization', {}),
                    final_report=final_report,
                    win_rate=performance_metrics.get('win_rate'),
                    profit_factor=performance_metrics.get('profit_factor'),
                    max_drawdown=performance_metrics.get('max_drawdown'),
                    total_return=performance_metrics.get('total_return'),
                    sharpe_ratio=performance_metrics.get('sharpe_ratio'),
                    key_insights=insights_and_suggestions.get('key_insights', []),
                    improvement_suggestions=insights_and_suggestions.get('improvement_suggestions', []),
                    strategy_adjustments=insights_and_suggestions.get('strategy_adjustments', []),
                    status='completed'
                )
                
                session.add(review_report)
                session.flush()
                
                # 保存策略调整建议
                self._save_strategy_adjustments(session, report_id, insights_and_suggestions.get('strategy_adjustments', []))
            
            return report_id
            
        except Exception as e:
            raise Exception(f"保存复盘报告失败: {str(e)}")
    
    def _extract_performance_metrics(self, review_result: Dict[str, Any]) -> Dict[str, float]:
        """提取绩效指标"""
        try:
            # 从不同来源提取绩效指标
            performance_eval = review_result.get('performance_evaluation', {})
            data_analysis = review_result.get('data_analysis', {})
            
            metrics = {}
            
            # 尝试从绩效评估结果中提取
            if 'absolute_performance' in performance_eval:
                abs_perf = performance_eval['absolute_performance']
                metrics.update({
                    'total_return': abs_perf.get('total_return'),
                    'max_drawdown': abs_perf.get('max_drawdown'),
                    'sharpe_ratio': abs_perf.get('sharpe_ratio')
                })
            
            # 尝试从数据分析结果中提取
            if 'performance_metrics' in data_analysis:
                perf_metrics = data_analysis['performance_metrics']
                metrics.update({
                    'win_rate': perf_metrics.get('win_rate'),
                    'profit_factor': perf_metrics.get('profit_factor')
                })
            
            return metrics
            
        except Exception:
            return {}
    
    def _extract_insights_and_suggestions(self, review_result: Dict[str, Any]) -> Dict[str, Any]:
        """提取洞察和建议"""
        try:
            final_report = review_result.get('review_result', {})
            
            return {
                'key_insights': final_report.get('key_insights', []),
                'improvement_suggestions': final_report.get('improvement_suggestions', []),
                'strategy_adjustments': final_report.get('strategy_adjustments', [])
            }
            
        except Exception:
            return {'key_insights': [], 'improvement_suggestions': [], 'strategy_adjustments': []}
    
    def _save_strategy_adjustments(self, session, report_id: str, adjustments: List[Dict[str, Any]]):
        """保存策略调整建议"""
        try:
            for adjustment in adjustments:
                strategy_adjustment = StrategyAdjustment(
                    review_report_id=report_id,
                    adjustment_type=adjustment.get('type', 'parameter'),
                    component=adjustment.get('component', 'unknown'),
                    parameter_name=adjustment.get('parameter_name', 'unknown'),
                    old_value=str(adjustment.get('old_value', '')),
                    new_value=str(adjustment.get('new_value', '')),
                    reason=adjustment.get('reason', ''),
                    expected_impact=adjustment.get('expected_impact', ''),
                    status='pending'
                )
                session.add(strategy_adjustment)
                
        except Exception as e:
            print(f"保存策略调整失败: {str(e)}")
    
    def get_latest_review_report(self, report_type: str = None) -> Optional[Dict[str, Any]]:
        """获取最新的复盘报告"""
        try:
            with self.session_scope() as session:
                query = session.query(ReviewReport)
                
                if report_type:
                    query = query.filter(ReviewReport.report_type == report_type)
                
                latest_report = query.order_by(ReviewReport.created_at.desc()).first()
                
                if latest_report:
                    return {
                        'id': latest_report.id,
                        'report_type': latest_report.report_type,
                        'review_period': {
                            'start': latest_report.review_period_start,
                            'end': latest_report.review_period_end
                        },
                        'performance_metrics': {
                            'win_rate': latest_report.win_rate,
                            'profit_factor': latest_report.profit_factor,
                            'max_drawdown': latest_report.max_drawdown,
                            'total_return': latest_report.total_return,
                            'sharpe_ratio': latest_report.sharpe_ratio
                        },
                        'key_insights': latest_report.key_insights,
                        'improvement_suggestions': latest_report.improvement_suggestions,
                        'strategy_adjustments': latest_report.strategy_adjustments,
                        'status': latest_report.status,
                        'applied_to_trading': latest_report.applied_to_trading,
                        'created_at': latest_report.created_at
                    }
                
                return None
                
        except Exception as e:
            raise Exception(f"获取最新复盘报告失败: {str(e)}")
    
    def get_pending_strategy_adjustments(self) -> List[Dict[str, Any]]:
        """获取待应用的策略调整"""
        try:
            with self.session_scope() as session:
                adjustments = session.query(StrategyAdjustment).filter(
                    StrategyAdjustment.status == 'pending'
                ).order_by(StrategyAdjustment.created_at.desc()).all()
                
                return [
                    {
                        'id': adj.id,
                        'review_report_id': adj.review_report_id,
                        'adjustment_type': adj.adjustment_type,
                        'component': adj.component,
                        'parameter_name': adj.parameter_name,
                        'old_value': adj.old_value,
                        'new_value': adj.new_value,
                        'reason': adj.reason,
                        'expected_impact': adj.expected_impact,
                        'created_at': adj.created_at
                    }
                    for adj in adjustments
                ]
                
        except Exception as e:
            raise Exception(f"获取待应用策略调整失败: {str(e)}")
    
    def mark_adjustment_applied(self, adjustment_id: int):
        """标记策略调整已应用"""
        try:
            with self.session_scope() as session:
                adjustment = session.query(StrategyAdjustment).filter(
                    StrategyAdjustment.id == adjustment_id
                ).first()
                
                if adjustment:
                    adjustment.status = 'applied'
                    adjustment.applied_at = datetime.now()
                    
        except Exception as e:
            raise Exception(f"标记策略调整应用失败: {str(e)}")
    
    def mark_report_applied(self, report_id: str):
        """标记复盘报告已应用到交易系统"""
        try:
            with self.session_scope() as session:
                report = session.query(ReviewReport).filter(
                    ReviewReport.id == report_id
                ).first()
                
                if report:
                    report.applied_to_trading = True
                    report.application_time = datetime.now()
                    report.status = 'applied'
                    
        except Exception as e:
            raise Exception(f"标记复盘报告应用失败: {str(e)}")
    
    def _determine_report_type(self, review_result: Dict[str, Any]) -> str:
        """确定报告类型"""
        review_period = review_result.get('review_period', '')
        
        if 'daily' in review_period:
            return 'daily'
        elif 'weekly' in review_period:
            return 'weekly'
        elif 'monthly' in review_period:
            return 'monthly'
        else:
            return 'manual'
    
    def _parse_period_start(self, review_result: Dict[str, Any]) -> datetime:
        """解析复盘开始时间"""
        try:
            # 尝试从不同字段解析时间
            period_str = review_result.get('review_period', '')
            if '_to_' in period_str:
                start_str = period_str.split('_to_')[0].split('_')[-1]
                return datetime.strptime(start_str, '%Y-%m-%d')
            
            return datetime.now() - timedelta(days=1)
        except:
            return datetime.now() - timedelta(days=1)
    
    def _parse_period_end(self, review_result: Dict[str, Any]) -> datetime:
        """解析复盘结束时间"""
        try:
            period_str = review_result.get('review_period', '')
            if '_to_' in period_str:
                end_str = period_str.split('_to_')[1].split('_')[0]
                return datetime.strptime(end_str, '%Y-%m-%d')
            
            return datetime.now()
        except:
            return datetime.now()


if __name__ == '__main__':
    # 测试复盘结果管理器
    manager = ReviewResultManager()
    manager.init_db()
    
    # 模拟复盘结果
    test_review_result = {
        'status': 'success',
        'review_period': '2024-01-01_to_2024-01-31_daily',
        'data_analysis': {
            'performance_metrics': {
                'win_rate': 0.65,
                'profit_factor': 2.1
            }
        },
        'performance_evaluation': {
            'absolute_performance': {
                'total_return': 0.15,
                'max_drawdown': 0.08,
                'sharpe_ratio': 1.5
            }
        },
        'review_result': {
            'key_insights': ['胜率较高', '风险控制良好'],
            'improvement_suggestions': ['优化入场时机', '调整止损策略'],
            'strategy_adjustments': [
                {
                    'type': 'parameter',
                    'component': 'trader',
                    'parameter_name': 'signal_threshold',
                    'old_value': '0.7',
                    'new_value': '0.75',
                    'reason': '提高信号质量',
                    'expected_impact': '胜率提升3%'
                }
            ]
        }
    }
    
    # 保存复盘报告
    report_id = manager.save_review_report(test_review_result)
    print(f"复盘报告已保存: {report_id}")
    
    # 获取最新报告
    latest_report = manager.get_latest_review_report()
    print("最新复盘报告:", latest_report)
    
    # 获取待应用的策略调整
    pending_adjustments = manager.get_pending_strategy_adjustments()
    print("待应用策略调整:", pending_adjustments)
