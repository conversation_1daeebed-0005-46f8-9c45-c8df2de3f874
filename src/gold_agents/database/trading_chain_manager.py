#!/usr/bin/env python3
"""
完整交易链数据管理器 - 记录从分析到交易结束的完整过程
基于MySQL数据库，记录每个Agent的输入输出
"""

from sqlalchemy import create_engine, Column, String, Text, Float, Integer, DateTime, Boolean, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, scoped_session
from contextlib import contextmanager
from datetime import datetime
from typing import Dict, Any, Optional, List
import json
import uuid
import yaml
import os

Base = declarative_base()

# 数据库表定义
class TradingSession(Base):
    """交易会话表 - 记录完整的交易会话"""
    __tablename__ = 'trading_sessions'
    
    id = Column(String(36), primary_key=True)  # UUID
    trading_pair = Column(String(20), nullable=False)
    session_type = Column(String(20), default='live')  # live, test, backtest
    start_time = Column(DateTime, nullable=False)
    end_time = Column(DateTime)
    initial_balance = Column(Float)
    final_balance = Column(Float)
    total_pnl = Column(Float, default=0.0)
    total_trades = Column(Integer, default=0)
    winning_trades = Column(Integer, default=0)
    max_drawdown = Column(Float, default=0.0)
    status = Column(String(20), default='active')  # active, completed, failed, stopped
    metadata = Column(JSON)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)


class AgentExecution(Base):
    """Agent执行记录表 - 记录每个Agent的完整执行过程"""
    __tablename__ = 'agent_executions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(36), nullable=False)
    execution_order = Column(Integer, nullable=False)  # 执行顺序
    agent_name = Column(String(50), nullable=False)
    task_name = Column(String(100), nullable=False)
    
    # 输入数据
    input_data = Column(JSON)  # Agent接收的输入数据
    context_data = Column(JSON)  # 上下文数据（其他Agent的输出）
    
    # 执行过程
    start_time = Column(DateTime, nullable=False)
    end_time = Column(DateTime)
    execution_time = Column(Float)  # 执行耗时（秒）
    
    # 工具调用记录
    tools_called = Column(JSON)  # 调用的工具列表
    tool_results = Column(JSON)  # 工具调用结果
    
    # 输出数据
    output_data = Column(JSON)  # Agent的输出结果
    decision_reasoning = Column(Text)  # 决策推理过程
    
    # 执行状态
    success = Column(Boolean, default=True)
    error_message = Column(Text)
    retry_count = Column(Integer, default=0)
    
    # 性能指标
    memory_usage = Column(Float)  # 内存使用量（MB）
    cpu_usage = Column(Float)  # CPU使用率
    
    created_at = Column(DateTime, default=datetime.now)


class ToolExecution(Base):
    """工具执行记录表 - 记录每个工具的调用详情"""
    __tablename__ = 'tool_executions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    agent_execution_id = Column(Integer, nullable=False)
    session_id = Column(String(36), nullable=False)
    
    tool_name = Column(String(100), nullable=False)
    tool_action = Column(String(100))  # 工具的具体动作
    
    # 调用信息
    call_order = Column(Integer, nullable=False)  # 在Agent中的调用顺序
    start_time = Column(DateTime, nullable=False)
    end_time = Column(DateTime)
    execution_time = Column(Float)
    
    # 输入输出
    input_parameters = Column(JSON)
    output_result = Column(JSON)
    
    # 执行状态
    success = Column(Boolean, default=True)
    error_message = Column(Text)
    retry_count = Column(Integer, default=0)
    
    # API调用信息（如果适用）
    api_endpoint = Column(String(200))
    api_response_code = Column(Integer)
    api_response_time = Column(Float)
    
    created_at = Column(DateTime, default=datetime.now)


class TradingDecision(Base):
    """交易决策记录表 - 记录最终的交易决策"""
    __tablename__ = 'trading_decisions'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(36), nullable=False)
    decision_id = Column(String(36), nullable=False)  # 决策UUID
    
    # 决策信息
    decision_type = Column(String(20), nullable=False)  # open_long, open_short, close, hold
    trading_pair = Column(String(20), nullable=False)
    
    # 交易参数
    size = Column(Float)
    price = Column(Float)
    order_type = Column(String(20))  # market, limit
    leverage = Column(Integer)
    stop_loss = Column(Float)
    take_profit = Column(Float)
    
    # 决策依据
    strategy_id = Column(Integer)
    signal_strength = Column(Float)
    confidence_level = Column(Float)
    risk_score = Column(Float)
    
    # 团队决策过程
    trader_recommendation = Column(JSON)
    position_manager_advice = Column(JSON)
    risk_controller_assessment = Column(JSON)
    order_executor_plan = Column(JSON)
    
    # 合规检查
    rules_validation = Column(JSON)
    compliance_status = Column(String(20))  # approved, rejected, conditional
    
    # 执行结果
    execution_status = Column(String(20), default='pending')  # pending, executed, failed, cancelled
    actual_execution = Column(JSON)  # 实际执行结果
    
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)


class TradingChainManager:
    """完整交易链数据管理器"""
    
    def __init__(self, config_path: str = None):
        """初始化交易链管理器"""
        self.config = self._load_config(config_path)
        self.engine = self._create_engine()
        self.Session = scoped_session(sessionmaker(
            bind=self.engine,
            expire_on_commit=False
        ))
        self.current_session_id = None
        self.current_execution_order = 0
        
    def _load_config(self, config_path: Optional[str] = None) -> Dict[str, Any]:
        """加载配置文件"""
        if config_path is None:
            config_path = os.path.join(os.path.dirname(__file__), '../config/trader_config.yaml')
            
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            # 使用默认配置
            return {
                'database': {
                    'host': '*************',
                    'port': '6221',
                    'user': 'Mysql5.7',
                    'password': 'a78d04a8027589c3',
                    'name': 'Mysql5.7'
                }
            }
    
    def _create_engine(self):
        """创建数据库引擎"""
        db_config = self.config['database']
        db_url = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['name']}"
        return create_engine(db_url, echo=False)
    
    def init_db(self):
        """初始化数据库表"""
        Base.metadata.create_all(self.engine)
    
    @contextmanager
    def session_scope(self):
        """提供数据库会话的上下文管理器"""
        session = self.Session()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()
    
    def start_trading_session(self, trading_pair: str, initial_balance: float, 
                            session_type: str = 'live', metadata: Dict[str, Any] = None) -> str:
        """开始新的交易会话"""
        session_id = str(uuid.uuid4())
        self.current_session_id = session_id
        self.current_execution_order = 0
        
        with self.session_scope() as session:
            trading_session = TradingSession(
                id=session_id,
                trading_pair=trading_pair,
                session_type=session_type,
                start_time=datetime.now(),
                initial_balance=initial_balance,
                metadata=metadata or {}
            )
            session.add(trading_session)
            
        return session_id
    
    def record_agent_execution_start(self, agent_name: str, task_name: str, 
                                   input_data: Dict[str, Any], 
                                   context_data: Dict[str, Any] = None) -> int:
        """记录Agent执行开始"""
        self.current_execution_order += 1
        
        with self.session_scope() as session:
            agent_execution = AgentExecution(
                session_id=self.current_session_id,
                execution_order=self.current_execution_order,
                agent_name=agent_name,
                task_name=task_name,
                input_data=input_data,
                context_data=context_data or {},
                start_time=datetime.now(),
                tools_called=[],
                tool_results={}
            )
            session.add(agent_execution)
            session.flush()
            session.refresh(agent_execution)
            return agent_execution.id
    
    def record_tool_execution(self, agent_execution_id: int, tool_name: str, 
                            tool_action: str, input_parameters: Dict[str, Any],
                            call_order: int) -> int:
        """记录工具执行开始"""
        with self.session_scope() as session:
            tool_execution = ToolExecution(
                agent_execution_id=agent_execution_id,
                session_id=self.current_session_id,
                tool_name=tool_name,
                tool_action=tool_action,
                call_order=call_order,
                start_time=datetime.now(),
                input_parameters=input_parameters
            )
            session.add(tool_execution)
            session.flush()
            session.refresh(tool_execution)
            return tool_execution.id
    
    def complete_tool_execution(self, tool_execution_id: int, output_result: Dict[str, Any],
                              success: bool = True, error_message: str = None,
                              api_info: Dict[str, Any] = None):
        """完成工具执行记录"""
        with self.session_scope() as session:
            tool_execution = session.query(ToolExecution).filter_by(id=tool_execution_id).first()
            if tool_execution:
                tool_execution.end_time = datetime.now()
                tool_execution.execution_time = (tool_execution.end_time - tool_execution.start_time).total_seconds()
                tool_execution.output_result = output_result
                tool_execution.success = success
                tool_execution.error_message = error_message
                
                if api_info:
                    tool_execution.api_endpoint = api_info.get('endpoint')
                    tool_execution.api_response_code = api_info.get('response_code')
                    tool_execution.api_response_time = api_info.get('response_time')
    
    def complete_agent_execution(self, agent_execution_id: int, output_data: Dict[str, Any],
                                decision_reasoning: str = None, success: bool = True,
                                error_message: str = None, performance_metrics: Dict[str, Any] = None):
        """完成Agent执行记录"""
        with self.session_scope() as session:
            agent_execution = session.query(AgentExecution).filter_by(id=agent_execution_id).first()
            if agent_execution:
                agent_execution.end_time = datetime.now()
                agent_execution.execution_time = (agent_execution.end_time - agent_execution.start_time).total_seconds()
                agent_execution.output_data = output_data
                agent_execution.decision_reasoning = decision_reasoning
                agent_execution.success = success
                agent_execution.error_message = error_message
                
                if performance_metrics:
                    agent_execution.memory_usage = performance_metrics.get('memory_usage')
                    agent_execution.cpu_usage = performance_metrics.get('cpu_usage')
    
    def record_trading_decision(self, decision_data: Dict[str, Any]) -> str:
        """记录交易决策"""
        decision_id = str(uuid.uuid4())
        
        with self.session_scope() as session:
            trading_decision = TradingDecision(
                session_id=self.current_session_id,
                decision_id=decision_id,
                decision_type=decision_data.get('decision_type'),
                trading_pair=decision_data.get('trading_pair'),
                size=decision_data.get('size'),
                price=decision_data.get('price'),
                order_type=decision_data.get('order_type'),
                leverage=decision_data.get('leverage'),
                stop_loss=decision_data.get('stop_loss'),
                take_profit=decision_data.get('take_profit'),
                strategy_id=decision_data.get('strategy_id'),
                signal_strength=decision_data.get('signal_strength'),
                confidence_level=decision_data.get('confidence_level'),
                risk_score=decision_data.get('risk_score'),
                trader_recommendation=decision_data.get('trader_recommendation'),
                position_manager_advice=decision_data.get('position_manager_advice'),
                risk_controller_assessment=decision_data.get('risk_controller_assessment'),
                order_executor_plan=decision_data.get('order_executor_plan'),
                rules_validation=decision_data.get('rules_validation'),
                compliance_status=decision_data.get('compliance_status', 'pending')
            )
            session.add(trading_decision)
            
        return decision_id
    
    def update_trading_decision_execution(self, decision_id: str, execution_status: str,
                                        actual_execution: Dict[str, Any]):
        """更新交易决策执行结果"""
        with self.session_scope() as session:
            decision = session.query(TradingDecision).filter_by(decision_id=decision_id).first()
            if decision:
                decision.execution_status = execution_status
                decision.actual_execution = actual_execution
    
    def end_trading_session(self, final_balance: float, status: str = 'completed',
                          session_summary: Dict[str, Any] = None):
        """结束交易会话"""
        with self.session_scope() as session:
            trading_session = session.query(TradingSession).filter_by(id=self.current_session_id).first()
            if trading_session:
                trading_session.end_time = datetime.now()
                trading_session.final_balance = final_balance
                trading_session.total_pnl = final_balance - trading_session.initial_balance
                trading_session.status = status
                
                if session_summary:
                    trading_session.total_trades = session_summary.get('total_trades', 0)
                    trading_session.winning_trades = session_summary.get('winning_trades', 0)
                    trading_session.max_drawdown = session_summary.get('max_drawdown', 0.0)
                    
                    # 更新metadata
                    if trading_session.metadata:
                        trading_session.metadata.update(session_summary)
                    else:
                        trading_session.metadata = session_summary
        
        self.current_session_id = None
        self.current_execution_order = 0
    
    def get_session_data(self, session_id: str) -> Dict[str, Any]:
        """获取完整的会话数据"""
        with self.session_scope() as session:
            # 获取会话基本信息
            trading_session = session.query(TradingSession).filter_by(id=session_id).first()
            
            # 获取Agent执行记录
            agent_executions = session.query(AgentExecution).filter_by(session_id=session_id).order_by(AgentExecution.execution_order).all()
            
            # 获取工具执行记录
            tool_executions = session.query(ToolExecution).filter_by(session_id=session_id).order_by(ToolExecution.call_order).all()
            
            # 获取交易决策记录
            trading_decisions = session.query(TradingDecision).filter_by(session_id=session_id).order_by(TradingDecision.created_at).all()
            
            return {
                'session': trading_session,
                'agent_executions': agent_executions,
                'tool_executions': tool_executions,
                'trading_decisions': trading_decisions
            }


if __name__ == '__main__':
    # 测试交易链管理器
    manager = TradingChainManager()
    manager.init_db()
    
    # 开始交易会话
    session_id = manager.start_trading_session(
        trading_pair="BTCUSDT",
        initial_balance=10000.0,
        session_type="test",
        metadata={"test": "trading_chain"}
    )
    print(f"交易会话已开始: {session_id}")
    
    # 记录Agent执行
    agent_exec_id = manager.record_agent_execution_start(
        agent_name="trader",
        task_name="analyze_market",
        input_data={"market_condition": "bullish"},
        context_data={"previous_analysis": "positive"}
    )
    print(f"Agent执行已开始: {agent_exec_id}")
    
    # 记录工具执行
    tool_exec_id = manager.record_tool_execution(
        agent_execution_id=agent_exec_id,
        tool_name="StrategyTool",
        tool_action="get_latest",
        input_parameters={"trading_pair": "BTC"},
        call_order=1
    )
    
    # 完成工具执行
    manager.complete_tool_execution(
        tool_execution_id=tool_exec_id,
        output_result={"strategy": "bullish", "confidence": 0.8},
        success=True
    )
    
    # 完成Agent执行
    manager.complete_agent_execution(
        agent_execution_id=agent_exec_id,
        output_data={"recommendation": "open_long", "size": 0.1},
        decision_reasoning="基于策略信号和市场分析，建议开多仓",
        success=True
    )
    
    print("交易链数据记录测试完成")
