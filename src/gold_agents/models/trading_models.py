from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, JSON, Enum
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
import enum

Base = declarative_base()

class TradingPair(enum.Enum):
    """交易对枚举"""
    BTC = "BTC"
    ETH = "ETH"

class StrategyType(enum.Enum):
    """策略类型枚举"""
    TREND_FOLLOWING = "trend_following"
    MEAN_REVERSION = "mean_reversion"
    BREAKOUT = "breakout"
    GRID = "grid"

class Strategy(Base):
    """策略表"""
    __tablename__ = 'strategies'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    type = Column(Enum(StrategyType), nullable=False)
    description = Column(String(500))
    parameters = Column(JSON)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 关联
    trades = relationship("Trade", back_populates="strategy")
    signals = relationship("Signal", back_populates="strategy")

class Signal(Base):
    """交易信号表"""
    __tablename__ = 'signals'

    id = Column(Integer, primary_key=True)
    strategy_id = Column(Integer, ForeignKey('strategies.id'), nullable=False)
    trading_pair = Column(String(20), nullable=False)
    signal_type = Column(String(20), nullable=False)  # buy, sell, hold
    signal_strength = Column(Float)  # 信号强度 0-1
    price = Column(Float, nullable=False)
    timestamp = Column(DateTime, default=datetime.now)
    meta_data = Column(JSON)  # 存储额外的信号信息
    
    # 关联
    strategy = relationship("Strategy", back_populates="signals")
    trades = relationship("Trade", back_populates="signal")

class Trade(Base):
    """交易记录表"""
    __tablename__ = 'trades'

    id = Column(Integer, primary_key=True)
    strategy_id = Column(Integer, ForeignKey('strategies.id'), nullable=False)
    signal_id = Column(Integer, ForeignKey('signals.id'))
    trading_pair = Column(String(20), nullable=False)
    order_id = Column(String(50), nullable=False)
    side = Column(String(10), nullable=False)  # buy, sell
    order_type = Column(String(20), nullable=False)  # market, limit
    price = Column(Float, nullable=False)
    size = Column(Float, nullable=False)
    leverage = Column(Integer, nullable=False)
    status = Column(String(20), nullable=False)  # open, filled, cancelled
    open_time = Column(DateTime, nullable=False)
    close_time = Column(DateTime)
    pnl = Column(Float)  # 盈亏
    fee = Column(Float)  # 手续费
    meta_data = Column(JSON)  # 存储额外的交易信息
    
    # 关联
    strategy = relationship("Strategy", back_populates="trades")
    signal = relationship("Signal", back_populates="trades")

class Position(Base):
    """持仓表"""
    __tablename__ = 'positions'

    id = Column(Integer, primary_key=True)
    trading_pair = Column(String(20), nullable=False)
    side = Column(String(10), nullable=False)  # long, short
    size = Column(Float, nullable=False)
    entry_price = Column(Float, nullable=False)
    current_price = Column(Float, nullable=False)
    leverage = Column(Integer, nullable=False)
    margin = Column(Float, nullable=False)
    unrealized_pnl = Column(Float, nullable=False)
    timestamp = Column(DateTime, default=datetime.now)
    meta_data = Column(JSON)  # 存储额外的持仓信息

class AccountBalance(Base):
    """账户余额表"""
    __tablename__ = 'account_balances'

    id = Column(Integer, primary_key=True)
    trading_pair = Column(String(20), nullable=False)
    balance = Column(Float, nullable=False)
    available = Column(Float, nullable=False)
    margin = Column(Float, nullable=False)
    unrealized_pnl = Column(Float, nullable=False)
    timestamp = Column(DateTime, default=datetime.now)
    meta_data = Column(JSON)  # 存储额外的账户信息 