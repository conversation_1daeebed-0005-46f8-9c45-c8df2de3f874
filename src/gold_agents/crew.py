from crewai import Agent, Crew, Process, Task, LLM
from crewai.project import CrewBase, agent, crew, task
from .tools import (
    BitgetAccountTool, 
    BitgetMarketTool,
    BitgetOrderTool,
    BitgetRiskTool,
    BitgetTradeTool
)


@CrewBase
class GoldAgents():
    """现货黄金多智能体策略分析系统"""
    agents_config = 'config/agents.yaml'
    tasks_config = 'config/tasks.yaml'

    def __init__(self, db_config: dict):
        """初始化交易团队
        
        Args:
            db_config: 数据库配置信息
        """
        self.db_config = db_config


    @agent
    def trader(self) -> Agent:
        return Agent(
            config=self.agents_config['trader'],
            tools=[
                BitgetAccountTool(),
                BitgetMarketTool(),
                BitgetOrderTool(),
                BitgetRiskTool(),
                BitgetTradeTool(),
                self.strategy_tool
            ],
            verbose=True,
            allow_delegation=False
        )

    @agent
    def position_manager(self) -> Agent:
        return Agent(
            config=self.agents_config['position_manager'],
            tools=[
                BitgetAccountTool(),
                BitgetRiskTool(),
                self.strategy_tool
            ],
            verbose=True,
            allow_delegation=False
        )

    @agent
    def risk_controller(self) -> Agent:
        return Agent(
            config=self.agents_config['risk_controller'],
            tools=[
                BitgetMarketTool(),
                BitgetRiskTool(),
                self.strategy_tool
            ],
            verbose=True,
            allow_delegation=False
        )

    @agent
    def order_executor(self) -> Agent:
        return Agent(
            config=self.agents_config['order_executor'],
            tools=[
                BitgetOrderTool(),
                BitgetTradeTool(),
                self.strategy_tool
            ],
            verbose=True,
            allow_delegation=False
        )

    # Define Tasks using the updated YAML config keys

    @task
    def trader_task(self) -> Task:
        return Task(
            config=self.tasks_config['trader_task'],
            agent=self.trader(),
            output_file='output/11_trader_task.md',
            async_execution=True
        )

    @task
    def position_manager_task(self) -> Task:
        return Task(
            config=self.tasks_config['position_manager_task'],
            agent=self.position_manager(),
            context=[self.trader_task()],
            output_file='output/12_position_manager_task.md',
            async_execution=True
        )

    @task
    def risk_controller_task(self) -> Task:
        return Task(
            config=self.tasks_config['risk_controller_task'],
            agent=self.risk_controller(),
            context=[self.position_manager_task()],
            output_file='output/13_risk_controller_task.md',
            async_execution=True
        )

    @task
    def order_executor_task(self) -> Task:
        return Task(
            config=self.tasks_config['order_executor_task'],
            agent=self.order_executor(),
            context=[self.risk_controller_task()],
            output_file='output/14_order_executor_task.md',
            async_execution=True
        )

    @crew
    def crew(self) -> Crew:
        """创建虚拟货币多智能体策略分析系统"""
        return Crew(
            agents=self.agents,
            tasks=self.tasks,
            process=Process.sequential,
            verbose=True,
            max_rpm=3
        )

    def close(self):
        """关闭资源"""
        if hasattr(self, 'strategy_tool'):
            self.strategy_tool.close()
