from crewai import Agent, Crew, Process, Task, LLM
from crewai.project import CrewBase, agent, crew, task
from .tools import (
    BitgetAccountTool,
    BitgetMarketTool,
    BitgetOrderTool,
    BitgetRiskTool,
    BitgetTradeTool,
    StrategyTool,
    TradingRulesEngine,
    ReviewResultQueryTool,
    ProfessionalMarketAnalyzer
)
from .tools.tool_optimizer import ToolOptimizer
from .tools.strategy_adjustment_tool import StrategyAdjustmentTool
from .database.trading_chain_manager import TradingChainManager
from .config.config_manager import config_manager
import logging


@CrewBase
class GoldAgents():
    """优化的多智能体加密货币交易系统"""
    agents_config = 'config/agents.yaml'
    tasks_config = 'config/tasks.yaml'

    def __init__(self, db_config: dict = None):
        """初始化交易团队

        Args:
            db_config: 数据库配置信息
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config_manager = config_manager
        self.db_config = db_config or self.config_manager.get_database_config().__dict__

        # 初始化工具
        self._init_tools()

        self.logger.info("交易团队初始化完成")

    def _init_tools(self):
        """初始化所有工具"""
        try:
            # 核心交易工具
            self.strategy_tool = StrategyTool()
            self.account_tool = BitgetAccountTool()
            self.market_tool = BitgetMarketTool()
            self.order_tool = BitgetOrderTool()
            self.risk_tool = BitgetRiskTool()
            self.trade_tool = BitgetTradeTool()

            # 管理和优化工具
            self.rules_engine = TradingRulesEngine()
            self.tool_optimizer = ToolOptimizer()
            self.strategy_adjustment_tool = StrategyAdjustmentTool()
            self.review_query_tool = ReviewResultQueryTool()
            self.professional_analyzer = ProfessionalMarketAnalyzer()

            # 交易链数据管理器
            self.trading_chain_manager = TradingChainManager()
            self.trading_chain_manager.init_db()

            # 创建交易会话
            self.session_id = self.trading_chain_manager.start_trading_session(
                trading_pair="BTCUSDT",
                initial_balance=10000.0,
                session_type="live",
                metadata={"system": "gold_agents", "version": "3.0"}
            )

            self.logger.info(f"所有工具初始化完成，会话ID: {self.session_id}")
        except Exception as e:
            self.logger.error(f"工具初始化失败: {str(e)}")
            raise


    @agent
    def trader(self) -> Agent:
        """交易员Agent - 负责制定交易决策和策略分析"""
        return Agent(
            config=self.agents_config['trader'],
            tools=[
                self.professional_analyzer,     # 专业市场分析（新增）
                self.strategy_tool,              # 获取和验证策略信号
                self.market_tool,                # 分析市场数据和深度
                self.account_tool,               # 检查账户状态
                self.rules_engine,               # 验证交易决策合规性
                self.review_query_tool           # 查询复盘结果（只读）
            ],
            verbose=True,
            allow_delegation=False,
            max_iter=3,
            memory=True
        )

    @agent
    def position_manager(self) -> Agent:
        """仓位管理员Agent - 负责资金配置和仓位优化"""
        return Agent(
            config=self.agents_config['position_manager'],
            tools=[
                self.account_tool,       # 监控账户和持仓状态
                self.risk_tool,          # 评估仓位风险
                self.strategy_tool       # 获取策略风险参数
            ],
            verbose=True,
            allow_delegation=False,
            max_iter=2,
            memory=True
        )

    @agent
    def risk_controller(self) -> Agent:
        """风险控制员Agent - 负责风险监控和应急处理"""
        return Agent(
            config=self.agents_config['risk_controller'],
            tools=[
                self.rules_engine,       # 执行风险合规检查
                self.risk_tool,          # 执行风险控制措施
                self.market_tool,        # 监控市场风险
                self.account_tool        # 检查账户风险指标
            ],
            verbose=True,
            allow_delegation=False,
            max_iter=2,
            memory=True
        )

    @agent
    def order_executor(self) -> Agent:
        """订单执行员Agent - 负责订单执行和交易记录"""
        return Agent(
            config=self.agents_config['order_executor'],
            tools=[
                self.trade_tool,         # 执行实际交易
                self.order_tool,         # 管理订单状态
                self.market_tool,        # 优化执行价格
                self.account_tool        # 验证执行结果
            ],
            verbose=True,
            allow_delegation=False,
            max_iter=2,
            memory=True
        )

    # Define Tasks using the updated YAML config keys

    @task
    def trader_task(self) -> Task:
        return Task(
            config=self.tasks_config['trader_task'],
            agent=self.trader(),
            output_file='output/11_trader_task.md',
            async_execution=True
        )

    @task
    def position_manager_task(self) -> Task:
        return Task(
            config=self.tasks_config['position_manager_task'],
            agent=self.position_manager(),
            context=[self.trader_task()],
            output_file='output/12_position_manager_task.md',
            async_execution=True
        )

    @task
    def risk_controller_task(self) -> Task:
        return Task(
            config=self.tasks_config['risk_controller_task'],
            agent=self.risk_controller(),
            context=[self.position_manager_task()],
            output_file='output/13_risk_controller_task.md',
            async_execution=True
        )

    @task
    def order_executor_task(self) -> Task:
        return Task(
            config=self.tasks_config['order_executor_task'],
            agent=self.order_executor(),
            context=[self.risk_controller_task()],
            output_file='output/14_order_executor_task.md',
            async_execution=True
        )

    @crew
    def crew(self) -> Crew:
        """创建优化的多智能体加密货币交易系统"""
        return Crew(
            agents=self.agents,
            tasks=self.tasks,
            process=Process.sequential,
            verbose=True,
            max_rpm=5,  # 增加请求频率限制
            memory=True,  # 启用记忆功能
            planning=True,  # 启用规划功能
            embedder={
                "provider": "openai",
                "config": {
                    "model": "text-embedding-3-small"
                }
            }
        )

    def get_crew_metrics(self) -> dict:
        """获取团队性能指标"""
        try:
            metrics = {
                "tools_metrics": {},
                "config_status": {
                    "api_config": bool(self.config_manager.get_api_config().api_key),
                    "risk_config": bool(self.config_manager.get_risk_config()),
                    "database_config": bool(self.db_config)
                },
                "timestamp": self.config_manager.get_config('timestamp', 'unknown')
            }

            # 收集工具性能指标
            tools = [self.strategy_tool, self.account_tool, self.market_tool,
                    self.order_tool, self.risk_tool, self.trade_tool]

            for tool in tools:
                if hasattr(tool, 'get_metrics'):
                    metrics["tools_metrics"][tool.name] = tool.get_metrics()

            return metrics
        except Exception as e:
            self.logger.error(f"获取团队指标失败: {str(e)}")
            return {"error": str(e)}

    def health_check(self) -> dict:
        """团队健康检查"""
        try:
            health_status = {
                "overall_status": "healthy",
                "components": {},
                "timestamp": self.config_manager.get_config('timestamp', 'unknown')
            }

            # 检查工具健康状态
            tools = [self.strategy_tool, self.account_tool, self.market_tool,
                    self.order_tool, self.risk_tool, self.trade_tool]

            for tool in tools:
                if hasattr(tool, 'health_check'):
                    health_status["components"][tool.name] = tool.health_check()
                else:
                    health_status["components"][tool.name] = {"status": "unknown"}

            # 检查配置状态
            health_status["components"]["config"] = {
                "status": "healthy" if self.config_manager.get_api_config().api_key else "unhealthy",
                "details": "配置管理器状态"
            }

            # 判断整体状态
            unhealthy_components = [
                name for name, status in health_status["components"].items()
                if status.get("status") != "healthy"
            ]

            if unhealthy_components:
                health_status["overall_status"] = "unhealthy"
                health_status["unhealthy_components"] = unhealthy_components

            return health_status
        except Exception as e:
            return {
                "overall_status": "error",
                "error": str(e),
                "timestamp": self.config_manager.get_config('timestamp', 'unknown')
            }

    def close(self):
        """关闭资源"""
        try:
            tools = [self.strategy_tool, self.account_tool, self.market_tool,
                    self.order_tool, self.risk_tool, self.trade_tool]

            for tool in tools:
                if hasattr(tool, 'close'):
                    tool.close()

            self.logger.info("所有资源已关闭")
        except Exception as e:
            self.logger.error(f"关闭资源失败: {str(e)}")
