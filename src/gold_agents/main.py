#!/usr/bin/env python3
"""
优化的主程序入口 - 支持多种运行模式和完善的错误处理
"""

import sys
import warnings
import os
import logging
import argparse
import json
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

from .crew import GoldAgents
from .config.config_manager import config_manager

# 配置警告过滤
warnings.filterwarnings("ignore", category=SyntaxWarning, module="pysbd")
warnings.filterwarnings("ignore", category=UserWarning, module="pydantic")

# 加载环境变量
load_dotenv()

# 配置日志
def setup_logging(level: str = "INFO"):
    """设置日志配置"""
    log_level = getattr(logging, level.upper(), logging.INFO)

    # 创建logs目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    # 配置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 文件处理器
    file_handler = logging.FileHandler(
        log_dir / f"gold_agents_{datetime.now().strftime('%Y%m%d')}.log",
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)

    # 根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)

    return root_logger


def run():
    """CrewAI run命令兼容性函数"""
    return main_trading_system()


def main_trading_system(mode: str = "normal", inputs: dict = None):
    """
    主要的交易系统运行函数

    Args:
        mode: 运行模式 (normal, test, health_check, metrics)
        inputs: 输入参数
    """
    logger = logging.getLogger(__name__)
    gold_agents = None

    try:
        logger.info(f"启动交易系统 - 模式: {mode}")

        # 创建交易团队实例
        gold_agents = GoldAgents()

        # 根据模式执行不同操作
        if mode == "health_check":
            return perform_health_check(gold_agents)
        elif mode == "metrics":
            return get_system_metrics(gold_agents)
        elif mode == "test":
            return run_test_mode(gold_agents, inputs)
        else:
            return run_normal_mode(gold_agents, inputs)

    except Exception as e:
        logger.error(f"交易系统运行失败: {str(e)}")
        logger.exception("详细错误信息:")
        return {"status": "error", "message": str(e)}
    finally:
        if gold_agents:
            gold_agents.close()


def run_normal_mode(gold_agents: GoldAgents, inputs: dict = None) -> dict:
    """正常运行模式"""
    logger = logging.getLogger(__name__)

    try:
        # 获取crew实例
        crew = gold_agents.crew()

        # 准备输入参数
        default_inputs = {
            "trading_pair": "BTCUSDT",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "mode": "live_trading"
        }

        if inputs:
            default_inputs.update(inputs)

        logger.info(f"开始执行交易任务 - 输入参数: {default_inputs}")

        # 执行crew任务
        result = crew.kickoff(inputs=default_inputs)

        logger.info("交易任务执行完成")

        return {
            "status": "success",
            "result": result,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

    except Exception as e:
        logger.error(f"正常模式执行失败: {str(e)}")
        raise


def run_test_mode(gold_agents: GoldAgents, inputs: dict = None) -> dict:
    """测试运行模式"""
    logger = logging.getLogger(__name__)

    try:
        logger.info("运行测试模式")

        # 执行健康检查
        health_status = gold_agents.health_check()

        # 获取系统指标
        metrics = gold_agents.get_crew_metrics()

        # 模拟交易执行（不实际下单）
        test_inputs = {
            "trading_pair": "BTCUSDT",
            "mode": "test",
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        if inputs:
            test_inputs.update(inputs)

        return {
            "status": "success",
            "mode": "test",
            "health_check": health_status,
            "metrics": metrics,
            "test_inputs": test_inputs,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

    except Exception as e:
        logger.error(f"测试模式执行失败: {str(e)}")
        raise


def perform_health_check(gold_agents: GoldAgents) -> dict:
    """执行健康检查"""
    logger = logging.getLogger(__name__)

    try:
        logger.info("执行系统健康检查")

        health_status = gold_agents.health_check()

        # 添加配置检查
        config_status = {
            "api_config": bool(config_manager.get_api_config().api_key),
            "risk_config": bool(config_manager.get_risk_config()),
            "database_config": bool(config_manager.get_database_config())
        }

        health_status["config_status"] = config_status

        logger.info(f"健康检查完成 - 状态: {health_status['overall_status']}")

        return health_status

    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return {
            "overall_status": "error",
            "error": str(e),
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }


def get_system_metrics(gold_agents: GoldAgents) -> dict:
    """获取系统性能指标"""
    logger = logging.getLogger(__name__)

    try:
        logger.info("获取系统性能指标")

        metrics = gold_agents.get_crew_metrics()

        # 添加系统信息
        metrics["system_info"] = {
            "python_version": sys.version,
            "platform": sys.platform,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        return metrics

    except Exception as e:
        logger.error(f"获取系统指标失败: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }


def train():
    """训练crew模型"""
    logger = logging.getLogger(__name__)

    try:
        if len(sys.argv) < 3:
            raise ValueError("训练需要指定迭代次数和文件名")

        n_iterations = int(sys.argv[1])
        filename = sys.argv[2]

        inputs = {
            "trading_pair": "BTCUSDT",
            "mode": "training"
        }

        logger.info(f"开始训练 - 迭代次数: {n_iterations}, 文件: {filename}")

        gold_agents = GoldAgents()
        gold_agents.crew().train(n_iterations=n_iterations, filename=filename, inputs=inputs)

        logger.info("训练完成")

    except Exception as e:
        logger.error(f"训练失败: {str(e)}")
        raise Exception(f"An error occurred while training the crew: {e}")


def replay():
    """重放crew执行"""
    logger = logging.getLogger(__name__)

    try:
        if len(sys.argv) < 2:
            raise ValueError("重放需要指定任务ID")

        task_id = sys.argv[1]

        logger.info(f"开始重放任务: {task_id}")

        gold_agents = GoldAgents()
        gold_agents.crew().replay(task_id=task_id)

        logger.info("重放完成")

    except Exception as e:
        logger.error(f"重放失败: {str(e)}")
        raise Exception(f"An error occurred while replaying the crew: {e}")


def test():
    """测试crew执行"""
    logger = logging.getLogger(__name__)

    try:
        if len(sys.argv) < 3:
            raise ValueError("测试需要指定迭代次数和模型名称")

        n_iterations = int(sys.argv[1])
        model_name = sys.argv[2]

        inputs = {
            "trading_pair": "BTCUSDT",
            "mode": "testing"
        }

        logger.info(f"开始测试 - 迭代次数: {n_iterations}, 模型: {model_name}")

        gold_agents = GoldAgents()
        gold_agents.crew().test(n_iterations=n_iterations, openai_model_name=model_name, inputs=inputs)

        logger.info("测试完成")

    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        raise Exception(f"An error occurred while testing the crew: {e}")


def main():
    """主函数 - 支持命令行参数"""
    parser = argparse.ArgumentParser(description="Gold Agents Trading System")
    parser.add_argument("--mode", choices=["normal", "test", "health_check", "metrics"],
                       default="normal", help="运行模式")
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       default="INFO", help="日志级别")
    parser.add_argument("--config", help="配置文件路径")
    parser.add_argument("--trading-pair", default="BTCUSDT", help="交易对")

    args = parser.parse_args()

    # 设置日志
    logger = setup_logging(args.log_level)

    try:
        logger.info(f"启动Gold Agents交易系统 - 模式: {args.mode}")

        # 准备输入参数
        inputs = {
            "trading_pair": args.trading_pair,
            "config_file": args.config
        }

        # 运行系统
        result = main_trading_system(mode=args.mode, inputs=inputs)

        # 输出结果
        print(json.dumps(result, indent=2, ensure_ascii=False))

        return result

    except Exception as e:
        logger.error(f"系统运行失败: {str(e)}")
        print(f"错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
