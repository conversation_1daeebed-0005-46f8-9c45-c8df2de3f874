#!/usr/bin/env python
import sys
import warnings
import os
from datetime import datetime
from dotenv import load_dotenv

from .crew import GoldAgents

warnings.filterwarnings("ignore", category=SyntaxWarning, module="pysbd")

# Load environment variables
load_dotenv()

# This main file is intended to be a way for you to run your
# crew locally, so refrain from adding unnecessary logic into this file.
# Replace with inputs you want to test with, it will automatically
# interpolate any tasks and agents information

def run():
    """
    Run function for crewai run command compatibility
    """
    return main_gold_trading()


def main():
    """
    Main function to run the GoldAgents crew
    """
    try:
        # Create an instance of GoldAgents
        gold_agents = GoldAgents()
        
        # Get the crew
        crew = gold_agents.crew()
        
        # Get the current year for the task context
        current_year = datetime.now().year
        
        # Kick off the crew with the topic context
        result = crew.kickoff(
            inputs={
                "topic": "Gold Trading Analysis",
                "current_year": current_year
            }
        )
        
        print("Crew execution completed successfully!")
        print(f"Result: {result}")
        
        return result
    
    except Exception as e:
        print(f"An error occurred: {e}")
        raise


def main_gold_trading():
    """
    Main function to run the Gold Trading crew
    """
    try:
        # Create an instance of GoldAgents
        gold_agents = GoldAgents()
        
        # Get the crew
        crew = gold_agents.crew()
        
        # Kick off the crew
        result = crew.kickoff()
        
        print("Gold Trading crew execution completed successfully!")
        
        return result
    
    except Exception as e:
        print(f"An error occurred: {e}")
        raise


def train():
    """
    Train the crew for a given number of iterations.
    """
    inputs = {
        "topic": "AI LLMs"
    }
    try:
        GoldAgents().crew().train(n_iterations=int(sys.argv[1]), filename=sys.argv[2], inputs=inputs)

    except Exception as e:
        raise Exception(f"An error occurred while training the crew: {e}")


def replay():
    """
    Replay the crew execution from a specific task.
    """
    try:
        GoldAgents().crew().replay(task_id=sys.argv[1])

    except Exception as e:
        raise Exception(f"An error occurred while replaying the crew: {e}")


def test():
    """
    Test the crew execution and returns the results.
    """
    inputs = {
        "topic": "AI LLMs"
    }
    try:
        GoldAgents().crew().test(n_iterations=int(sys.argv[1]), openai_model_name=sys.argv[2], inputs=inputs)

    except Exception as e:
        raise Exception(f"An error occurred while testing the crew: {e}")


if __name__ == "__main__":
    main_gold_trading()
