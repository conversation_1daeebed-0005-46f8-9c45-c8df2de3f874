# 交易准则配置 - 确保盈利和风险控制的核心规则

# 核心交易目标
trading_objectives:
  primary_goal: "确保长期稳定盈利"
  secondary_goals:
    - "维持高胜率（>60%）"
    - "控制最大回撤（<10%）"
    - "优化风险收益比（>2:1）"
    - "保持资金安全"

# 盈利策略框架
profit_strategy:
  # 胜率要求
  win_rate_targets:
    minimum: 0.55      # 最低胜率55%
    target: 0.65       # 目标胜率65%
    excellent: 0.75    # 优秀胜率75%
  
  # 盈亏比要求
  risk_reward_ratios:
    minimum: 1.5       # 最低盈亏比1.5:1
    target: 2.0        # 目标盈亏比2:1
    excellent: 3.0     # 优秀盈亏比3:1
  
  # 盈利目标
  profit_targets:
    daily: 0.02        # 日盈利目标2%
    weekly: 0.10       # 周盈利目标10%
    monthly: 0.30      # 月盈利目标30%
    annual: 2.0        # 年盈利目标200%

# 强制交易规则
mandatory_rules:
  # 入场规则
  entry_rules:
    - "必须有明确的策略信号支持"
    - "信号强度必须≥0.7"
    - "市场流动性必须充足"
    - "账户风险敞口不得超过2%"
    - "必须设置止损和止盈"
    - "禁止在重大新闻发布前后30分钟交易"
  
  # 出场规则
  exit_rules:
    - "达到止盈目标立即平仓"
    - "触及止损位立即平仓"
    - "信号反转时考虑平仓"
    - "持仓时间超过24小时重新评估"
    - "市场异常波动时优先保本"
  
  # 仓位管理规则
  position_rules:
    - "单笔交易风险不超过账户的2%"
    - "总持仓不超过账户的20%"
    - "同方向持仓不超过账户的15%"
    - "杠杆倍数不超过5倍"
    - "保证金率不低于50%"
  
  # 风险控制规则
  risk_rules:
    - "日亏损超过5%停止交易"
    - "连续3笔亏损后暂停交易"
    - "最大回撤超过10%启动应急措施"
    - "保证金率低于30%强制减仓"
    - "系统故障时立即平仓"

# 交易决策框架
decision_framework:
  # 策略信号评估
  signal_evaluation:
    strength_thresholds:
      weak: 0.5          # 弱信号
      medium: 0.7        # 中等信号
      strong: 0.8        # 强信号
      very_strong: 0.9   # 极强信号
    
    confidence_requirements:
      minimum: 0.6       # 最低置信度
      preferred: 0.75    # 偏好置信度
      excellent: 0.9     # 优秀置信度
  
  # 市场条件评估
  market_conditions:
    volatility_limits:
      low: 0.01          # 低波动率
      normal: 0.03       # 正常波动率
      high: 0.05         # 高波动率
      extreme: 0.08      # 极端波动率
    
    liquidity_requirements:
      min_depth: 100000  # 最小市场深度（USDT）
      max_spread: 0.001  # 最大买卖价差
      min_volume: 50000  # 最小成交量
  
  # 时间窗口限制
  time_constraints:
    trading_hours:
      start: "00:00"     # 交易开始时间（UTC）
      end: "23:59"       # 交易结束时间（UTC）
    
    forbidden_periods:
      - "重大经济数据发布前后30分钟"
      - "周末市场流动性不足时段"
      - "节假日期间"

# 绩效评估标准
performance_standards:
  # 日度评估
  daily_metrics:
    min_win_rate: 0.6
    max_drawdown: 0.03
    min_profit_factor: 1.5
    max_trades: 10
  
  # 周度评估
  weekly_metrics:
    min_win_rate: 0.65
    max_drawdown: 0.05
    min_profit_factor: 2.0
    target_return: 0.10
  
  # 月度评估
  monthly_metrics:
    min_win_rate: 0.65
    max_drawdown: 0.08
    min_profit_factor: 2.5
    target_return: 0.30
    min_sharpe_ratio: 1.5

# 应急处理机制
emergency_procedures:
  # 触发条件
  triggers:
    high_loss: 0.05      # 日亏损超过5%
    low_margin: 0.3      # 保证金率低于30%
    system_error: true   # 系统错误
    market_crash: 0.1    # 市场暴跌超过10%
  
  # 应急措施
  actions:
    immediate:
      - "停止所有新开仓"
      - "评估现有持仓风险"
      - "准备紧急平仓"
    
    escalated:
      - "强制平仓所有持仓"
      - "暂停交易系统"
      - "发送风险警报"
      - "启动人工干预"

# 复盘和改进机制
review_improvement:
  # 复盘频率
  review_frequency:
    daily: true          # 每日复盘
    weekly: true         # 每周复盘
    monthly: true        # 每月复盘
    quarterly: true      # 季度复盘
  
  # 关键指标监控
  key_metrics:
    - "胜率变化趋势"
    - "盈亏比变化"
    - "最大回撤控制"
    - "资金使用效率"
    - "策略信号质量"
    - "执行效率"
  
  # 改进触发条件
  improvement_triggers:
    win_rate_decline: 0.05    # 胜率下降5%
    drawdown_increase: 0.03   # 回撤增加3%
    profit_factor_drop: 0.5   # 盈利因子下降0.5
  
  # 策略调整机制
  strategy_adjustment:
    signal_threshold: "根据历史表现动态调整"
    position_sizing: "基于波动率和胜率调整"
    stop_loss_levels: "根据市场条件优化"
    take_profit_targets: "基于风险收益比调整"

# 团队协作规则
team_collaboration:
  # 决策优先级
  decision_priority:
    1: "风险控制员"      # 最高优先级
    2: "仓位管理员"      # 第二优先级
    3: "交易员"          # 第三优先级
    4: "订单执行员"      # 执行优先级
  
  # 冲突解决机制
  conflict_resolution:
    risk_vs_profit: "优先风险控制"
    speed_vs_accuracy: "优先准确性"
    short_vs_long: "基于策略信号强度"
  
  # 信息共享要求
  information_sharing:
    - "所有决策必须记录原因"
    - "风险事件必须立即通报"
    - "策略变更必须团队确认"
    - "异常情况必须集体决策"

# 合规和监管要求
compliance:
  # 交易限制
  trading_limits:
    max_daily_volume: 1000000    # 日最大交易量（USDT）
    max_position_size: 100000    # 最大持仓规模（USDT）
    max_leverage: 5              # 最大杠杆倍数
  
  # 记录保存要求
  record_keeping:
    trade_records: "永久保存"
    decision_logs: "保存1年"
    risk_events: "永久保存"
    performance_reports: "保存3年"
  
  # 审计要求
  audit_requirements:
    - "所有交易必须可追溯"
    - "决策过程必须透明"
    - "风险控制措施必须有效"
    - "绩效报告必须准确"
