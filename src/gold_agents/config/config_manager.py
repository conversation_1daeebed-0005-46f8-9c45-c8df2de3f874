#!/usr/bin/env python3
"""
统一配置管理器 - 集中管理所有配置文件和环境变量
"""

import os
import yaml
import json
from typing import Dict, Any, Optional, Union
from pathlib import Path
from dataclasses import dataclass
from datetime import datetime


@dataclass
class APIConfig:
    """API配置"""
    api_key: str
    api_secret: str
    passphrase: str
    environment: str = "test"
    
    @property
    def is_production(self) -> bool:
        return self.environment == "prod"


@dataclass
class RiskConfig:
    """风险控制配置"""
    max_position_size: float = 1.0
    max_account_risk: float = 0.1
    max_leverage: int = 5
    min_margin_ratio: float = 0.5
    max_daily_loss: float = 0.05
    max_single_loss: float = 0.02
    max_price_deviation: float = 0.01
    default_take_profit_ratio: float = 0.02
    default_stop_loss_ratio: float = 0.01


@dataclass
class TradingConfig:
    """交易配置"""
    trading_pairs: Dict[str, Dict[str, Any]]
    price_monitor: Dict[str, Any]
    position_adjustment: Dict[str, float]


@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = "localhost"
    port: str = "3306"
    user: str = "root"
    password: str = "root"
    name: str = "test"


class ConfigManager:
    """统一配置管理器"""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self.config_dir = Path(__file__).parent
            self._configs = {}
            self._load_all_configs()
            self._initialized = True
    
    def _load_all_configs(self):
        """加载所有配置文件"""
        try:
            # 加载主要配置文件
            config_files = {
                'agents': 'agents.yaml',
                'tasks': 'tasks.yaml',
                'trader': 'trader_config.yaml',
                'risk': 'risk_config.yaml'
            }
            
            for config_name, filename in config_files.items():
                config_path = self.config_dir / filename
                if config_path.exists():
                    self._configs[config_name] = self._load_yaml_file(config_path)
                else:
                    print(f"警告: 配置文件 {filename} 不存在")
            
            # 处理环境变量覆盖
            self._apply_env_overrides()
            
            # 验证配置
            self._validate_configs()
            
        except Exception as e:
            print(f"加载配置失败: {str(e)}")
            raise
    
    def _load_yaml_file(self, file_path: Path) -> Dict[str, Any]:
        """加载YAML文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        except Exception as e:
            print(f"加载YAML文件失败 {file_path}: {str(e)}")
            return {}
    
    def _apply_env_overrides(self):
        """应用环境变量覆盖"""
        # API配置环境变量覆盖
        if 'trader' in self._configs and 'api' in self._configs['trader']:
            api_config = self._configs['trader']['api']
            api_config['api_key'] = os.getenv('BITGET_API_KEY', api_config.get('api_key', ''))
            api_config['api_secret'] = os.getenv('BITGET_API_SECRET', api_config.get('api_secret', ''))
            api_config['passphrase'] = os.getenv('BITGET_PASSPHRASE', api_config.get('passphrase', ''))
        
        # 环境配置覆盖
        if 'trader' in self._configs:
            self._configs['trader']['environment'] = os.getenv('TRADING_ENV', 
                                                              self._configs['trader'].get('environment', 'test'))
        
        # 数据库配置环境变量覆盖
        if 'trader' in self._configs and 'database' in self._configs['trader']:
            db_config = self._configs['trader']['database']
            db_config['host'] = os.getenv('DB_HOST', db_config.get('host', 'localhost'))
            db_config['port'] = os.getenv('DB_PORT', db_config.get('port', '3306'))
            db_config['user'] = os.getenv('DB_USER', db_config.get('user', 'root'))
            db_config['password'] = os.getenv('DB_PASSWORD', db_config.get('password', 'root'))
            db_config['name'] = os.getenv('DB_NAME', db_config.get('name', 'test'))
    
    def _validate_configs(self):
        """验证配置的完整性和正确性"""
        errors = []
        
        # 验证API配置
        if 'trader' in self._configs and 'api' in self._configs['trader']:
            api_config = self._configs['trader']['api']
            required_api_fields = ['api_key', 'api_secret', 'passphrase']
            for field in required_api_fields:
                if not api_config.get(field):
                    errors.append(f"API配置缺少必需字段: {field}")
        
        # 验证风险配置
        if 'risk' in self._configs:
            risk_config = self._configs['risk']
            if 'risk_control' in risk_config:
                rc = risk_config['risk_control']
                if rc.get('max_account_risk', 0) > 1.0:
                    errors.append("最大账户风险比例不能超过100%")
                if rc.get('max_leverage', 0) > 100:
                    errors.append("最大杠杆倍数过高")
        
        if errors:
            raise ValueError(f"配置验证失败: {'; '.join(errors)}")
    
    def get_api_config(self) -> APIConfig:
        """获取API配置"""
        trader_config = self._configs.get('trader', {})
        api_config = trader_config.get('api', {})
        
        return APIConfig(
            api_key=api_config.get('api_key', ''),
            api_secret=api_config.get('api_secret', ''),
            passphrase=api_config.get('passphrase', ''),
            environment=trader_config.get('environment', 'test')
        )
    
    def get_risk_config(self) -> RiskConfig:
        """获取风险控制配置"""
        risk_config = self._configs.get('risk', {}).get('risk_control', {})
        trader_config = self._configs.get('trader', {}).get('risk_control', {})
        
        # 合并配置，trader_config优先
        merged_config = {**risk_config, **trader_config}
        
        return RiskConfig(
            max_position_size=merged_config.get('max_position_size', 1.0),
            max_account_risk=merged_config.get('max_account_risk', 0.1),
            max_leverage=merged_config.get('max_leverage', 5),
            min_margin_ratio=merged_config.get('min_margin_ratio', 0.5),
            max_daily_loss=merged_config.get('max_daily_loss', 0.05),
            max_single_loss=merged_config.get('max_single_loss', 0.02),
            max_price_deviation=merged_config.get('max_price_deviation', 0.01),
            default_take_profit_ratio=merged_config.get('default_take_profit_ratio', 0.02),
            default_stop_loss_ratio=merged_config.get('default_stop_loss_ratio', 0.01)
        )
    
    def get_trading_config(self) -> TradingConfig:
        """获取交易配置"""
        trader_config = self._configs.get('trader', {})
        
        return TradingConfig(
            trading_pairs=trader_config.get('trading_pairs', {}),
            price_monitor=trader_config.get('price_monitor', {}),
            position_adjustment=trader_config.get('risk_control', {}).get('position_adjustment', {})
        )
    
    def get_database_config(self) -> DatabaseConfig:
        """获取数据库配置"""
        db_config = self._configs.get('trader', {}).get('database', {})
        
        return DatabaseConfig(
            host=db_config.get('host', 'localhost'),
            port=db_config.get('port', '3306'),
            user=db_config.get('user', 'root'),
            password=db_config.get('password', 'root'),
            name=db_config.get('name', 'test')
        )
    
    def get_agent_config(self, agent_name: str) -> Dict[str, Any]:
        """获取特定Agent的配置"""
        agents_config = self._configs.get('agents', {})
        return agents_config.get(agent_name, {})
    
    def get_task_config(self, task_name: str) -> Dict[str, Any]:
        """获取特定任务的配置"""
        tasks_config = self._configs.get('tasks', {})
        return tasks_config.get(task_name, {})
    
    def get_config(self, config_path: str, default: Any = None) -> Any:
        """通过路径获取配置值"""
        try:
            keys = config_path.split('.')
            value = self._configs
            
            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return default
            
            return value
        except Exception:
            return default
    
    def update_config(self, config_path: str, value: Any):
        """更新配置值"""
        try:
            keys = config_path.split('.')
            config = self._configs
            
            # 导航到目标位置
            for key in keys[:-1]:
                if key not in config:
                    config[key] = {}
                config = config[key]
            
            # 设置值
            config[keys[-1]] = value
            
        except Exception as e:
            print(f"更新配置失败: {str(e)}")
            raise
    
    def save_config(self, config_name: str):
        """保存配置到文件"""
        try:
            if config_name in self._configs:
                config_file = self.config_dir / f"{config_name}.yaml"
                with open(config_file, 'w', encoding='utf-8') as f:
                    yaml.dump(self._configs[config_name], f, 
                             default_flow_style=False, allow_unicode=True)
        except Exception as e:
            print(f"保存配置失败: {str(e)}")
            raise
    
    def reload_configs(self):
        """重新加载所有配置"""
        self._configs.clear()
        self._load_all_configs()
    
    def get_all_configs(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self._configs.copy()
    
    def export_config(self, format: str = 'yaml') -> str:
        """导出配置"""
        if format.lower() == 'json':
            return json.dumps(self._configs, indent=2, ensure_ascii=False)
        else:
            return yaml.dump(self._configs, default_flow_style=False, allow_unicode=True)


# 全局配置管理器实例
config_manager = ConfigManager()


if __name__ == '__main__':
    # 测试配置管理器
    cm = ConfigManager()
    
    print("API配置:", cm.get_api_config())
    print("风险配置:", cm.get_risk_config())
    print("数据库配置:", cm.get_database_config())
    
    # 测试配置路径访问
    print("环境:", cm.get_config('trader.environment', 'test'))
    print("最大杠杆:", cm.get_config('trader.risk_control.max_leverage', 5))
