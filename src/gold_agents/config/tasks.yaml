# 交易任务配置 - 优化版本
trader_task:
  description: |
    执行核心交易决策任务：
    1. 获取并验证最新策略信号
    2. 分析当前账户状态和市场条件
    3. 计算最优仓位大小和风险参数
    4. 执行交易决策并设置止损止盈
  expected_output: |
    标准JSON格式输出，包含交易决策的所有关键信息：
    - 交易动作和参数（action, symbol, order_type, price, size）
    - 风险控制参数（stop_loss, take_profit）
    - 账户状态信息（balance, position, unrealized_pnl）
    - 风险度量指标（account_risk, position_size, margin_ratio）
  agent: trader
  async_execution: false
  context: []

position_manager_task:
  description: |
    执行仓位管理和资金配置任务：
    1. 监控当前持仓状态和保证金使用
    2. 评估投资组合风险和相关性
    3. 优化资金分配和仓位大小
    4. 提供仓位调整建议
  expected_output: |
    标准JSON格式输出，包含仓位管理的关键信息：
    - 当前仓位状态（total_position, unrealized_pnl, margin_usage）
    - 资金分配计划（recommended_size, max_position, risk_budget）
    - 调整建议（action, reason, target_size）
  agent: position_manager
  async_execution: false
  context: [trader_task]

risk_controller_task:
  description: |
    执行风险监控和控制任务：
    1. 实时评估市场和交易风险
    2. 监控风险指标和预警信号
    3. 执行风险控制措施
    4. 提供风险管理建议
  expected_output: |
    标准JSON格式输出，包含风险评估的关键信息：
    - 风险评估（overall_risk, market_volatility, liquidity_risk）
    - 预警信息（active_warnings, risk_triggers, emergency_actions）
    - 风险建议（position_adjustment, risk_measures, urgency）
  agent: risk_controller
  async_execution: false
  context: [trader_task, position_manager_task]

order_executor_task:
  description: |
    执行订单管理和执行优化任务：
    1. 制定最优订单执行策略
    2. 监控订单执行质量和成本
    3. 处理执行异常和错误
    4. 评估执行效果
  expected_output: |
    标准JSON格式输出，包含订单执行的关键信息：
    - 执行计划（order_type, execution_strategy, estimated_cost）
    - 执行结果（order_id, status, fill_price, fill_quantity）
    - 性能指标（slippage, execution_time, market_impact）
  agent: order_executor
  async_execution: false
  context: [trader_task, position_manager_task, risk_controller_task]


