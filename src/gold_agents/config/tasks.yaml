trader_task:
  description: '作为Bitget平台的交易执行者，根据策略团队的分析结果，结合账户状态和市场深度，执行最优的交易决策。需要监控账户状态、分析挂单情况、执行交易、管理风险敞口，并优化交易执行。首先从数据库获取最新的有效策略，验证策略的有效性，然后根据策略执行交易决策。'
  expected_output: '必须严格按照JSON格式输出结果，包含role、action、symbol、order_type、price、size、stop_loss、take_profit、reason、account_status、strategy_info和timestamp字段。action必须是"open_long"、"open_short"、"close_long"、"close_short"、"modify_order"、"cancel_order"或"no_action"之一，order_type必须是"market"或"limit"，所有价格和数量必须是精确的数值。account_status必须包含balance、position和unrealized_pnl字段。strategy_info必须包含strategy_id、name、type、signal_strength和risk_tip字段。不得包含任何解释或分析过程，只返回JSON格式的结果。'
  agent: trader
  async_execution: true

position_manager_task:
  description: '作为仓位管理员，全面管理交易账户的仓位和资金分配。监控当前持仓状态，计算可用保证金，评估仓位风险，优化资金使用效率。根据策略的风险提示和信号强度，调整仓位大小和资金分配。'
  expected_output: '必须严格按照JSON格式输出结果，包含role、position_status、allocation、risk_metrics、strategy_impact和timestamp字段。position_status必须包含current_position、unrealized_pnl、margin_usage和available_margin字段。allocation必须包含single_trade_limit、total_position_limit和risk_exposure字段。risk_metrics必须包含max_drawdown、volatility_threshold和position_limit字段。strategy_impact必须包含strategy_id、position_adjustment和risk_adjustment字段。不得包含任何解释或分析过程，只返回JSON格式的结果。'
  agent: position_manager
  async_execution: true

risk_controller_task:
  description: '作为风险控制员，实时监控市场风险并执行风险控制措施。监控市场波动率，检查止损止盈条件，评估突发事件风险，执行风险预警和应急措施。根据策略的风险提示和关键价格水平，调整风险控制参数。'
  expected_output: '必须严格按照JSON格式输出结果，包含role、market_risk、trading_risk、emergency_status、strategy_risk和timestamp字段。market_risk必须包含volatility、liquidity、price_anomaly和sentiment字段。trading_risk必须包含stop_loss_triggered、position_risk、fund_risk和order_risk字段。emergency_status必须包含warning_triggered、emergency_action和risk_level字段。strategy_risk必须包含strategy_id、risk_level、key_levels和risk_measures字段。不得包含任何解释或分析过程，只返回JSON格式的结果。'
  agent: risk_controller
  async_execution: true

order_executor_task:
  description: '作为订单执行员，执行具体的交易订单并优化执行效果。执行开仓/平仓订单，管理订单状态，处理订单异常，优化订单执行价格。根据策略的入场价格、止盈止损价格，精确执行交易订单。'
  expected_output: '必须严格按照JSON格式输出结果，包含role、order_status、execution_metrics、error_handling、strategy_execution和timestamp字段。order_status必须包含order_id、status、type和execution_price字段。execution_metrics必须包含slippage、fill_ratio和execution_time字段。error_handling必须包含has_error、error_type和recovery_status字段。strategy_execution必须包含strategy_id、entry_execution、tp_execution和sl_execution字段。不得包含任何解释或分析过程，只返回JSON格式的结果。'
  agent: order_executor
  async_execution: true


