#!/usr/bin/env python3
"""
复盘结果查询工具 - 专为交易团队提供的独立查询接口
与复盘团队分离，只负责查询复盘结果，不执行复盘分析
"""

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
from crewai_tools import BaseTool
import yaml
import os
import json


class ReviewResultQueryToolSchema(BaseModel):
    """复盘结果查询工具的输入参数模式"""
    action: str = Field(..., description="操作类型：get_latest_report, get_pending_adjustments, get_performance_summary")
    report_type: Optional[str] = Field(None, description="报告类型：daily, weekly, monthly")
    days_back: Optional[int] = Field(7, description="查询最近几天的报告")
    include_applied: Optional[bool] = Field(False, description="是否包含已应用的调整")


class ReviewResultQueryTool(BaseTool):
    """复盘结果查询工具 - 交易团队专用"""
    name: str = "ReviewResultQueryTool"
    description: str = """
    这是一个专为交易团队提供的复盘结果查询工具。
    主要功能：
    1. 查询最新的复盘报告
    2. 获取待应用的策略调整建议
    3. 查看绩效趋势和关键指标
    4. 获取改进建议和洞察
    
    注意：此工具只负责查询，不执行复盘分析。
    
    使用示例：
    1. 获取最新日度报告：
       tool.run(action="get_latest_report", report_type="daily")
    
    2. 获取待应用调整：
       tool.run(action="get_pending_adjustments")
    
    3. 获取绩效摘要：
       tool.run(action="get_performance_summary", days_back=30)
    """
    args_schema: type[BaseModel] = ReviewResultQueryToolSchema

    def __init__(self, **kwargs):
        """初始化复盘结果查询工具"""
        super().__init__(**kwargs)
        self.engine = self._create_engine()
        self.Session = sessionmaker(bind=self.engine)

    def _create_engine(self):
        """创建数据库引擎"""
        try:
            # 加载配置
            config_path = os.path.join(os.path.dirname(__file__), '../config/trader_config.yaml')
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            db_config = config['database']
            db_url = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['name']}"
            return create_engine(db_url, echo=False)
        except Exception as e:
            # 使用默认配置
            db_url = "mysql+pymysql://Mysql5.7:a78d04a8027589c3@*************:6221/Mysql5.7"
            return create_engine(db_url, echo=False)

    def _run(self, **kwargs: Any) -> Dict[str, Any]:
        """执行查询操作"""
        try:
            action = kwargs.get('action', 'get_latest_report')
            
            if action == 'get_latest_report':
                return self._get_latest_report(kwargs)
            elif action == 'get_pending_adjustments':
                return self._get_pending_adjustments(kwargs)
            elif action == 'get_performance_summary':
                return self._get_performance_summary(kwargs)
            elif action == 'get_insights_summary':
                return self._get_insights_summary(kwargs)
            else:
                return {
                    "status": "error",
                    "message": f"不支持的操作类型: {action}",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
        except Exception as e:
            return {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

    def _get_latest_report(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """获取最新的复盘报告"""
        try:
            report_type = params.get('report_type')
            
            with self.Session() as session:
                # 构建查询
                query = """
                    SELECT * FROM review_reports 
                    WHERE 1=1
                """
                query_params = {}
                
                if report_type:
                    query += " AND report_type = :report_type"
                    query_params['report_type'] = report_type
                
                query += " ORDER BY created_at DESC LIMIT 1"
                
                result = session.execute(text(query), query_params).fetchone()
                
                if not result:
                    return {
                        "status": "no_data",
                        "message": f"没有找到{report_type or '任何'}类型的复盘报告",
                        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                
                # 转换为字典
                columns = result._fields
                report_data = dict(zip(columns, result))
                
                # 解析JSON字段
                json_fields = ['data_analysis_result', 'performance_evaluation_result', 
                              'strategy_optimization_result', 'final_report', 'key_insights', 
                              'improvement_suggestions', 'strategy_adjustments']
                
                for field in json_fields:
                    if report_data.get(field):
                        try:
                            if isinstance(report_data[field], str):
                                report_data[field] = json.loads(report_data[field])
                        except:
                            pass
                
                return {
                    "status": "success",
                    "report": report_data,
                    "report_id": report_data['id'],
                    "report_type": report_data['report_type'],
                    "created_at": report_data['created_at'],
                    "applied_to_trading": report_data.get('applied_to_trading', False),
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
        except Exception as e:
            raise Exception(f"获取最新复盘报告失败: {str(e)}")

    def _get_pending_adjustments(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """获取待应用的策略调整"""
        try:
            include_applied = params.get('include_applied', False)
            
            with self.Session() as session:
                query = """
                    SELECT sa.*, rr.report_type, rr.created_at as report_created_at
                    FROM strategy_adjustments sa
                    JOIN review_reports rr ON sa.review_report_id = rr.id
                    WHERE 1=1
                """
                
                if not include_applied:
                    query += " AND sa.status = 'pending'"
                
                query += " ORDER BY sa.created_at DESC"
                
                results = session.execute(text(query)).fetchall()
                
                if not results:
                    return {
                        "status": "no_data",
                        "message": "没有找到策略调整记录",
                        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                
                # 转换为字典列表
                columns = results[0]._fields
                adjustments = [dict(zip(columns, row)) for row in results]
                
                # 按组件分组
                grouped_adjustments = {}
                for adj in adjustments:
                    component = adj['component']
                    if component not in grouped_adjustments:
                        grouped_adjustments[component] = []
                    grouped_adjustments[component].append(adj)
                
                return {
                    "status": "success",
                    "total_adjustments": len(adjustments),
                    "pending_count": len([adj for adj in adjustments if adj['status'] == 'pending']),
                    "applied_count": len([adj for adj in adjustments if adj['status'] == 'applied']),
                    "adjustments_by_component": grouped_adjustments,
                    "all_adjustments": adjustments,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
        except Exception as e:
            raise Exception(f"获取策略调整失败: {str(e)}")

    def _get_performance_summary(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """获取绩效摘要"""
        try:
            days_back = params.get('days_back', 7)
            start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
            
            with self.Session() as session:
                # 获取指定时间范围内的报告
                query = """
                    SELECT report_type, win_rate, profit_factor, max_drawdown, 
                           total_return, sharpe_ratio, created_at
                    FROM review_reports 
                    WHERE DATE(created_at) >= :start_date
                    ORDER BY created_at DESC
                """
                
                results = session.execute(text(query), {'start_date': start_date}).fetchall()
                
                if not results:
                    return {
                        "status": "no_data",
                        "message": f"最近{days_back}天没有复盘报告",
                        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                
                # 转换为字典列表
                columns = results[0]._fields
                reports = [dict(zip(columns, row)) for row in results]
                
                # 计算趋势和统计
                performance_summary = self._calculate_performance_trends(reports)
                
                return {
                    "status": "success",
                    "period": f"最近{days_back}天",
                    "reports_count": len(reports),
                    "performance_summary": performance_summary,
                    "latest_metrics": reports[0] if reports else None,
                    "trend_analysis": self._analyze_performance_trends(reports),
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
        except Exception as e:
            raise Exception(f"获取绩效摘要失败: {str(e)}")

    def _calculate_performance_trends(self, reports: List[Dict]) -> Dict[str, Any]:
        """计算绩效趋势"""
        if not reports:
            return {}
        
        # 提取数值指标
        metrics = ['win_rate', 'profit_factor', 'max_drawdown', 'total_return', 'sharpe_ratio']
        trends = {}
        
        for metric in metrics:
            values = [r.get(metric) for r in reports if r.get(metric) is not None]
            if values:
                trends[metric] = {
                    'current': values[0] if values else None,
                    'average': sum(values) / len(values),
                    'best': max(values),
                    'worst': min(values),
                    'trend': 'improving' if len(values) > 1 and values[0] > values[-1] else 'declining' if len(values) > 1 else 'stable'
                }
        
        return trends

    def _analyze_performance_trends(self, reports: List[Dict]) -> List[str]:
        """分析绩效趋势"""
        if len(reports) < 2:
            return ["数据不足，无法分析趋势"]
        
        insights = []
        
        # 分析胜率趋势
        win_rates = [r.get('win_rate') for r in reports if r.get('win_rate') is not None]
        if len(win_rates) >= 2:
            if win_rates[0] > win_rates[-1]:
                insights.append(f"胜率呈上升趋势：从{win_rates[-1]:.2%}提升到{win_rates[0]:.2%}")
            elif win_rates[0] < win_rates[-1]:
                insights.append(f"胜率呈下降趋势：从{win_rates[-1]:.2%}下降到{win_rates[0]:.2%}")
        
        # 分析盈利因子趋势
        profit_factors = [r.get('profit_factor') for r in reports if r.get('profit_factor') is not None]
        if len(profit_factors) >= 2:
            if profit_factors[0] > profit_factors[-1]:
                insights.append(f"盈利因子改善：从{profit_factors[-1]:.2f}提升到{profit_factors[0]:.2f}")
            elif profit_factors[0] < profit_factors[-1]:
                insights.append(f"盈利因子下降：从{profit_factors[-1]:.2f}下降到{profit_factors[0]:.2f}")
        
        # 分析回撤控制
        drawdowns = [r.get('max_drawdown') for r in reports if r.get('max_drawdown') is not None]
        if len(drawdowns) >= 2:
            if drawdowns[0] < drawdowns[-1]:
                insights.append(f"回撤控制改善：从{drawdowns[-1]:.2%}降低到{drawdowns[0]:.2%}")
            elif drawdowns[0] > drawdowns[-1]:
                insights.append(f"回撤控制恶化：从{drawdowns[-1]:.2%}增加到{drawdowns[0]:.2%}")
        
        return insights if insights else ["绩效指标相对稳定"]

    def _get_insights_summary(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """获取洞察摘要"""
        try:
            days_back = params.get('days_back', 7)
            start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
            
            with self.Session() as session:
                query = """
                    SELECT key_insights, improvement_suggestions, strategy_adjustments, 
                           report_type, created_at
                    FROM review_reports 
                    WHERE DATE(created_at) >= :start_date
                    AND (key_insights IS NOT NULL OR improvement_suggestions IS NOT NULL)
                    ORDER BY created_at DESC
                """
                
                results = session.execute(text(query), {'start_date': start_date}).fetchall()
                
                if not results:
                    return {
                        "status": "no_data",
                        "message": f"最近{days_back}天没有洞察数据",
                        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                
                # 汇总所有洞察和建议
                all_insights = []
                all_suggestions = []
                all_adjustments = []
                
                for row in results:
                    # 解析JSON字段
                    try:
                        if row.key_insights:
                            insights = json.loads(row.key_insights) if isinstance(row.key_insights, str) else row.key_insights
                            all_insights.extend(insights if isinstance(insights, list) else [insights])
                    except:
                        pass
                    
                    try:
                        if row.improvement_suggestions:
                            suggestions = json.loads(row.improvement_suggestions) if isinstance(row.improvement_suggestions, str) else row.improvement_suggestions
                            all_suggestions.extend(suggestions if isinstance(suggestions, list) else [suggestions])
                    except:
                        pass
                    
                    try:
                        if row.strategy_adjustments:
                            adjustments = json.loads(row.strategy_adjustments) if isinstance(row.strategy_adjustments, str) else row.strategy_adjustments
                            all_adjustments.extend(adjustments if isinstance(adjustments, list) else [adjustments])
                    except:
                        pass
                
                return {
                    "status": "success",
                    "period": f"最近{days_back}天",
                    "insights_summary": {
                        "key_insights": list(set(all_insights)),  # 去重
                        "improvement_suggestions": list(set(all_suggestions)),  # 去重
                        "strategy_adjustments_count": len(all_adjustments),
                        "total_insights": len(all_insights),
                        "total_suggestions": len(all_suggestions)
                    },
                    "recent_insights": all_insights[:5],  # 最近5条洞察
                    "recent_suggestions": all_suggestions[:5],  # 最近5条建议
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
        except Exception as e:
            raise Exception(f"获取洞察摘要失败: {str(e)}")


if __name__ == '__main__':
    # 测试复盘结果查询工具
    tool = ReviewResultQueryTool()
    
    # 测试获取最新报告
    print("=== 测试获取最新报告 ===")
    result = tool.run(action="get_latest_report", report_type="daily")
    print(json.dumps(result, indent=2, ensure_ascii=False, default=str))
    
    # 测试获取待应用调整
    print("\n=== 测试获取待应用调整 ===")
    result = tool.run(action="get_pending_adjustments")
    print(json.dumps(result, indent=2, ensure_ascii=False, default=str))
    
    # 测试获取绩效摘要
    print("\n=== 测试获取绩效摘要 ===")
    result = tool.run(action="get_performance_summary", days_back=30)
    print(json.dumps(result, indent=2, ensure_ascii=False, default=str))
