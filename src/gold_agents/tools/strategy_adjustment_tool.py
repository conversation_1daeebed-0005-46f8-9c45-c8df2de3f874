#!/usr/bin/env python3
"""
策略自动调整工具 - 根据复盘结果自动调整交易策略
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
from pydantic import BaseModel, Field
from crewai_tools import BaseTool
import json
import yaml
import os
from pathlib import Path

# 导入复盘结果管理器
import sys
sys.path.append(str(Path(__file__).parent.parent.parent))
from review_agents.tools.review_result_manager import ReviewResultManager


class StrategyAdjustmentToolSchema(BaseModel):
    """策略调整工具的输入参数模式"""
    action: str = Field(..., description="操作类型：get_latest_review, apply_adjustments, get_recommendations")
    report_type: Optional[str] = Field(None, description="复盘报告类型：daily, weekly, monthly")
    auto_apply: Optional[bool] = Field(False, description="是否自动应用调整")


class StrategyAdjustmentTool(BaseTool):
    """策略自动调整工具"""
    name: str = "StrategyAdjustmentTool"
    description: str = """
    这是一个用于根据复盘结果自动调整交易策略的工具。
    主要功能：
    1. 获取最新的复盘结果
    2. 解析策略调整建议
    3. 自动应用策略参数调整
    4. 更新交易规则和配置
    
    使用示例：
    1. 获取最新复盘结果：
       tool.run(action="get_latest_review", report_type="daily")
    
    2. 应用策略调整：
       tool.run(action="apply_adjustments", auto_apply=True)
    
    3. 获取调整建议：
       tool.run(action="get_recommendations")
    """
    args_schema: type[BaseModel] = StrategyAdjustmentToolSchema

    def __init__(self, **kwargs):
        """初始化策略调整工具"""
        super().__init__(**kwargs)
        self.result_manager = ReviewResultManager()
        self.config_dir = Path(__file__).parent.parent / "config"
        self.adjustment_history = []

    def _run(self, **kwargs: Any) -> Dict[str, Any]:
        """执行策略调整操作"""
        try:
            action = kwargs.get('action', 'get_latest_review')
            
            if action == 'get_latest_review':
                return self._get_latest_review(kwargs)
            elif action == 'apply_adjustments':
                return self._apply_strategy_adjustments(kwargs)
            elif action == 'get_recommendations':
                return self._get_adjustment_recommendations(kwargs)
            else:
                return {
                    "status": "error",
                    "message": f"不支持的操作类型: {action}",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
        except Exception as e:
            return {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

    def _get_latest_review(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """获取最新的复盘结果"""
        try:
            report_type = params.get('report_type')
            
            # 获取最新复盘报告
            latest_report = self.result_manager.get_latest_review_report(report_type)
            
            if not latest_report:
                return {
                    "status": "no_data",
                    "message": f"没有找到{report_type or '任何'}类型的复盘报告",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            
            # 获取待应用的策略调整
            pending_adjustments = self.result_manager.get_pending_strategy_adjustments()
            
            return {
                "status": "success",
                "latest_report": latest_report,
                "pending_adjustments": pending_adjustments,
                "adjustment_count": len(pending_adjustments),
                "report_applied": latest_report.get('applied_to_trading', False),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            raise Exception(f"获取最新复盘结果失败: {str(e)}")

    def _apply_strategy_adjustments(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """应用策略调整"""
        try:
            auto_apply = params.get('auto_apply', False)
            
            # 获取待应用的策略调整
            pending_adjustments = self.result_manager.get_pending_strategy_adjustments()
            
            if not pending_adjustments:
                return {
                    "status": "no_adjustments",
                    "message": "没有待应用的策略调整",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            
            applied_adjustments = []
            failed_adjustments = []
            
            for adjustment in pending_adjustments:
                try:
                    if auto_apply or self._should_apply_adjustment(adjustment):
                        success = self._apply_single_adjustment(adjustment)
                        
                        if success:
                            # 标记为已应用
                            self.result_manager.mark_adjustment_applied(adjustment['id'])
                            applied_adjustments.append(adjustment)
                        else:
                            failed_adjustments.append(adjustment)
                    
                except Exception as e:
                    print(f"应用调整失败: {adjustment['parameter_name']} - {str(e)}")
                    failed_adjustments.append(adjustment)
            
            # 如果有成功应用的调整，标记报告为已应用
            if applied_adjustments:
                report_ids = list(set([adj['review_report_id'] for adj in applied_adjustments]))
                for report_id in report_ids:
                    self.result_manager.mark_report_applied(report_id)
            
            return {
                "status": "success",
                "applied_count": len(applied_adjustments),
                "failed_count": len(failed_adjustments),
                "applied_adjustments": applied_adjustments,
                "failed_adjustments": failed_adjustments,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            raise Exception(f"应用策略调整失败: {str(e)}")

    def _should_apply_adjustment(self, adjustment: Dict[str, Any]) -> bool:
        """判断是否应该应用调整"""
        # 这里可以添加更复杂的逻辑来判断是否应该应用调整
        # 例如：检查调整的影响程度、风险等级等
        
        adjustment_type = adjustment.get('adjustment_type', '')
        component = adjustment.get('component', '')
        
        # 自动应用低风险的参数调整
        if adjustment_type == 'parameter' and component in ['trader', 'risk_controller']:
            return True
        
        # 其他类型的调整需要人工确认
        return False

    def _apply_single_adjustment(self, adjustment: Dict[str, Any]) -> bool:
        """应用单个策略调整"""
        try:
            adjustment_type = adjustment.get('adjustment_type')
            component = adjustment.get('component')
            parameter_name = adjustment.get('parameter_name')
            new_value = adjustment.get('new_value')
            
            if adjustment_type == 'parameter':
                return self._update_config_parameter(component, parameter_name, new_value)
            elif adjustment_type == 'rule':
                return self._update_trading_rule(component, parameter_name, new_value)
            elif adjustment_type == 'threshold':
                return self._update_threshold_value(component, parameter_name, new_value)
            else:
                print(f"不支持的调整类型: {adjustment_type}")
                return False
                
        except Exception as e:
            print(f"应用单个调整失败: {str(e)}")
            return False

    def _update_config_parameter(self, component: str, parameter_name: str, new_value: str) -> bool:
        """更新配置参数"""
        try:
            # 根据组件确定配置文件
            config_files = {
                'trader': 'trader_config.yaml',
                'risk_controller': 'risk_config.yaml',
                'position_manager': 'trader_config.yaml',
                'order_executor': 'trader_config.yaml'
            }
            
            config_file = config_files.get(component)
            if not config_file:
                print(f"未知的组件: {component}")
                return False
            
            config_path = self.config_dir / config_file
            
            # 读取配置文件
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 更新参数值
            if self._update_nested_config(config, parameter_name, new_value):
                # 保存配置文件
                with open(config_path, 'w', encoding='utf-8') as f:
                    yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
                
                # 记录调整历史
                self.adjustment_history.append({
                    'timestamp': datetime.now(),
                    'component': component,
                    'parameter': parameter_name,
                    'new_value': new_value,
                    'config_file': config_file
                })
                
                print(f"成功更新 {component}.{parameter_name} = {new_value}")
                return True
            
            return False
            
        except Exception as e:
            print(f"更新配置参数失败: {str(e)}")
            return False

    def _update_nested_config(self, config: Dict[str, Any], parameter_path: str, new_value: str) -> bool:
        """更新嵌套配置参数"""
        try:
            # 解析参数路径，例如 "risk_control.max_leverage"
            keys = parameter_path.split('.')
            
            # 导航到目标位置
            current = config
            for key in keys[:-1]:
                if key not in current:
                    current[key] = {}
                current = current[key]
            
            # 转换新值的类型
            final_key = keys[-1]
            old_value = current.get(final_key)
            
            # 根据原值类型转换新值
            if isinstance(old_value, bool):
                new_value = new_value.lower() in ['true', '1', 'yes']
            elif isinstance(old_value, int):
                new_value = int(float(new_value))
            elif isinstance(old_value, float):
                new_value = float(new_value)
            # 字符串类型保持不变
            
            # 设置新值
            current[final_key] = new_value
            
            return True
            
        except Exception as e:
            print(f"更新嵌套配置失败: {str(e)}")
            return False

    def _update_trading_rule(self, component: str, rule_name: str, new_value: str) -> bool:
        """更新交易规则"""
        try:
            # 更新交易规则配置
            rules_config_path = self.config_dir / "trading_rules.yaml"
            
            if not rules_config_path.exists():
                print("交易规则配置文件不存在")
                return False
            
            with open(rules_config_path, 'r', encoding='utf-8') as f:
                rules_config = yaml.safe_load(f)
            
            # 更新规则
            if self._update_nested_config(rules_config, rule_name, new_value):
                with open(rules_config_path, 'w', encoding='utf-8') as f:
                    yaml.dump(rules_config, f, default_flow_style=False, allow_unicode=True)
                
                print(f"成功更新交易规则 {rule_name} = {new_value}")
                return True
            
            return False
            
        except Exception as e:
            print(f"更新交易规则失败: {str(e)}")
            return False

    def _update_threshold_value(self, component: str, threshold_name: str, new_value: str) -> bool:
        """更新阈值设置"""
        try:
            # 阈值通常在策略配置或风险配置中
            config_path = self.config_dir / "trader_config.yaml"
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 更新阈值
            if self._update_nested_config(config, threshold_name, new_value):
                with open(config_path, 'w', encoding='utf-8') as f:
                    yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
                
                print(f"成功更新阈值 {threshold_name} = {new_value}")
                return True
            
            return False
            
        except Exception as e:
            print(f"更新阈值失败: {str(e)}")
            return False

    def _get_adjustment_recommendations(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """获取调整建议"""
        try:
            # 获取最新复盘报告
            latest_report = self.result_manager.get_latest_review_report()
            
            if not latest_report:
                return {
                    "status": "no_data",
                    "message": "没有可用的复盘报告",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            
            # 分析调整建议
            recommendations = self._analyze_adjustment_recommendations(latest_report)
            
            return {
                "status": "success",
                "recommendations": recommendations,
                "report_id": latest_report['id'],
                "report_date": latest_report['created_at'],
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            raise Exception(f"获取调整建议失败: {str(e)}")

    def _analyze_adjustment_recommendations(self, report: Dict[str, Any]) -> List[Dict[str, Any]]:
        """分析调整建议"""
        recommendations = []
        
        # 基于绩效指标生成建议
        performance_metrics = report.get('performance_metrics', {})
        
        win_rate = performance_metrics.get('win_rate', 0)
        profit_factor = performance_metrics.get('profit_factor', 0)
        max_drawdown = performance_metrics.get('max_drawdown', 0)
        
        # 胜率相关建议
        if win_rate < 0.6:
            recommendations.append({
                'priority': 'high',
                'category': 'signal_quality',
                'suggestion': '提高信号阈值以改善胜率',
                'target_parameter': 'signal_threshold',
                'current_impact': f'当前胜率: {win_rate:.2%}',
                'expected_improvement': '胜率提升5-10%'
            })
        
        # 盈利因子相关建议
        if profit_factor < 1.5:
            recommendations.append({
                'priority': 'high',
                'category': 'risk_reward',
                'suggestion': '优化止盈止损比例',
                'target_parameter': 'take_profit_ratio',
                'current_impact': f'当前盈利因子: {profit_factor:.2f}',
                'expected_improvement': '盈利因子提升0.3-0.5'
            })
        
        # 回撤相关建议
        if max_drawdown > 0.1:
            recommendations.append({
                'priority': 'critical',
                'category': 'risk_control',
                'suggestion': '加强风险控制措施',
                'target_parameter': 'max_position_size',
                'current_impact': f'当前最大回撤: {max_drawdown:.2%}',
                'expected_improvement': '回撤减少2-5%'
            })
        
        # 添加来自复盘报告的具体建议
        improvement_suggestions = report.get('improvement_suggestions', [])
        for suggestion in improvement_suggestions:
            recommendations.append({
                'priority': 'medium',
                'category': 'review_suggestion',
                'suggestion': suggestion,
                'source': 'review_report'
            })
        
        return recommendations

    def get_adjustment_history(self) -> List[Dict[str, Any]]:
        """获取调整历史"""
        return self.adjustment_history.copy()


if __name__ == '__main__':
    # 测试策略调整工具
    tool = StrategyAdjustmentTool()
    
    # 获取最新复盘结果
    result = tool.run(action="get_latest_review", report_type="daily")
    print("最新复盘结果:", json.dumps(result, indent=2, ensure_ascii=False, default=str))
    
    # 获取调整建议
    recommendations = tool.run(action="get_recommendations")
    print("调整建议:", json.dumps(recommendations, indent=2, ensure_ascii=False, default=str))
