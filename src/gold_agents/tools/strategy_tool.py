#!/usr/bin/env python3
"""
策略工具 - 用于获取和管理交易策略信号
"""

import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
from crewai_tools import BaseTool


class StrategyToolSchema(BaseModel):
    """策略工具的输入参数模式"""
    action: str = Field(..., description="操作类型：get_latest, get_by_id, validate")
    strategy_id: Optional[int] = Field(None, description="策略ID，获取特定策略时使用")
    trading_pair: str = Field(default="BTC", description="交易对")
    timeframe: str = Field(default="1h", description="时间周期：1m, 5m, 15m, 1h, 4h, 1d")


class StrategyTool(BaseTool):
    """策略管理工具类"""
    name: str = "StrategyTool"
    description: str = """
    这是一个用于获取和管理交易策略信号的工具。
    主要功能：
    1. 获取最新的有效策略信号
    2. 验证策略信号的时效性
    3. 获取特定策略的详细信息
    4. 管理策略信号的生命周期
    
    使用示例：
    1. 获取最新策略：
       tool.run(action="get_latest", trading_pair="BTC", timeframe="1h")
    
    2. 获取特定策略：
       tool.run(action="get_by_id", strategy_id=123)
    
    3. 验证策略有效性：
       tool.run(action="validate", strategy_id=123)
    """
    args_schema: type[BaseModel] = StrategyToolSchema

    def __init__(self, db_path: str = "trading_strategies.db", **kwargs):
        """初始化策略工具"""
        super().__init__(**kwargs)
        self.db_path = db_path
        self._init_database()

    def _init_database(self):
        """初始化策略数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建策略表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS strategies (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        trading_pair TEXT NOT NULL,
                        timeframe TEXT NOT NULL,
                        signal_type TEXT NOT NULL,  -- long, short, close
                        signal_strength REAL NOT NULL,  -- 0.0-1.0
                        entry_price REAL,
                        stop_loss REAL,
                        take_profit REAL,
                        risk_level TEXT NOT NULL,  -- low, medium, high
                        confidence REAL NOT NULL,  -- 0.0-1.0
                        created_at DATETIME NOT NULL,
                        expires_at DATETIME NOT NULL,
                        is_active BOOLEAN DEFAULT 1,
                        metadata TEXT  -- JSON格式的额外信息
                    )
                ''')
                
                # 创建策略执行记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS strategy_executions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        strategy_id INTEGER NOT NULL,
                        execution_time DATETIME NOT NULL,
                        execution_price REAL,
                        execution_size REAL,
                        result TEXT,  -- success, failed, partial
                        pnl REAL,
                        notes TEXT,
                        FOREIGN KEY (strategy_id) REFERENCES strategies (id)
                    )
                ''')
                
                conn.commit()
                
                # 插入一些示例策略数据（如果表为空）
                cursor.execute("SELECT COUNT(*) FROM strategies")
                if cursor.fetchone()[0] == 0:
                    self._insert_sample_strategies(cursor)
                    conn.commit()
                    
        except Exception as e:
            print(f"初始化策略数据库失败: {str(e)}")
            raise

    def _insert_sample_strategies(self, cursor):
        """插入示例策略数据"""
        sample_strategies = [
            {
                'trading_pair': 'BTC',
                'timeframe': '1h',
                'signal_type': 'long',
                'signal_strength': 0.8,
                'entry_price': 105000.0,
                'stop_loss': 103000.0,
                'take_profit': 108000.0,
                'risk_level': 'medium',
                'confidence': 0.75,
                'created_at': datetime.now(),
                'expires_at': datetime.now() + timedelta(hours=4),
                'metadata': json.dumps({
                    'indicators': ['RSI_oversold', 'MACD_bullish', 'MA_crossover'],
                    'market_conditions': 'trending_up',
                    'volume_profile': 'increasing'
                })
            },
            {
                'trading_pair': 'BTC',
                'timeframe': '4h',
                'signal_type': 'short',
                'signal_strength': 0.6,
                'entry_price': 104500.0,
                'stop_loss': 106000.0,
                'take_profit': 102000.0,
                'risk_level': 'high',
                'confidence': 0.65,
                'created_at': datetime.now() - timedelta(hours=1),
                'expires_at': datetime.now() + timedelta(hours=8),
                'metadata': json.dumps({
                    'indicators': ['RSI_overbought', 'Resistance_level'],
                    'market_conditions': 'ranging',
                    'volume_profile': 'decreasing'
                })
            }
        ]
        
        for strategy in sample_strategies:
            cursor.execute('''
                INSERT INTO strategies 
                (trading_pair, timeframe, signal_type, signal_strength, entry_price, 
                 stop_loss, take_profit, risk_level, confidence, created_at, expires_at, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                strategy['trading_pair'], strategy['timeframe'], strategy['signal_type'],
                strategy['signal_strength'], strategy['entry_price'], strategy['stop_loss'],
                strategy['take_profit'], strategy['risk_level'], strategy['confidence'],
                strategy['created_at'], strategy['expires_at'], strategy['metadata']
            ))

    def _run(self, **kwargs: Any) -> Dict[str, Any]:
        """执行策略工具的主要逻辑"""
        try:
            action = kwargs.get('action', 'get_latest')
            
            if action == 'get_latest':
                return self._get_latest_strategy(kwargs)
            elif action == 'get_by_id':
                return self._get_strategy_by_id(kwargs)
            elif action == 'validate':
                return self._validate_strategy(kwargs)
            else:
                return {
                    "status": "error",
                    "message": f"不支持的操作类型: {action}",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
        except Exception as e:
            return {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

    def _get_latest_strategy(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """获取最新的有效策略"""
        try:
            trading_pair = params.get('trading_pair', 'BTC')
            timeframe = params.get('timeframe', '1h')
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 查询最新的有效策略
                cursor.execute('''
                    SELECT * FROM strategies 
                    WHERE trading_pair = ? AND timeframe = ? 
                    AND is_active = 1 AND expires_at > datetime('now')
                    ORDER BY created_at DESC, signal_strength DESC
                    LIMIT 1
                ''', (trading_pair, timeframe))
                
                row = cursor.fetchone()
                if row:
                    strategy = self._row_to_strategy(row)
                    return {
                        "status": "success",
                        "strategy": strategy,
                        "message": f"找到最新策略: {strategy['signal_type']}",
                        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                else:
                    return {
                        "status": "no_data",
                        "strategy": None,
                        "message": f"未找到 {trading_pair} {timeframe} 的有效策略",
                        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                    
        except Exception as e:
            raise Exception(f"获取最新策略失败: {str(e)}")

    def _get_strategy_by_id(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """根据ID获取特定策略"""
        try:
            strategy_id = params.get('strategy_id')
            if not strategy_id:
                raise ValueError("strategy_id 参数是必需的")
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('SELECT * FROM strategies WHERE id = ?', (strategy_id,))
                row = cursor.fetchone()
                
                if row:
                    strategy = self._row_to_strategy(row)
                    return {
                        "status": "success",
                        "strategy": strategy,
                        "message": f"找到策略 ID: {strategy_id}",
                        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                else:
                    return {
                        "status": "not_found",
                        "strategy": None,
                        "message": f"未找到策略 ID: {strategy_id}",
                        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                    
        except Exception as e:
            raise Exception(f"获取策略失败: {str(e)}")

    def _validate_strategy(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """验证策略的有效性"""
        try:
            strategy_id = params.get('strategy_id')
            if not strategy_id:
                raise ValueError("strategy_id 参数是必需的")
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT id, expires_at, is_active, signal_strength, confidence 
                    FROM strategies WHERE id = ?
                ''', (strategy_id,))
                
                row = cursor.fetchone()
                if not row:
                    return {
                        "status": "not_found",
                        "is_valid": False,
                        "message": f"策略 ID {strategy_id} 不存在",
                        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                
                strategy_id, expires_at, is_active, signal_strength, confidence = row
                expires_at = datetime.fromisoformat(expires_at)
                
                # 验证策略有效性
                is_valid = (
                    is_active and 
                    expires_at > datetime.now() and
                    signal_strength >= 0.5 and
                    confidence >= 0.6
                )
                
                validation_details = {
                    "is_active": bool(is_active),
                    "is_expired": expires_at <= datetime.now(),
                    "signal_strength": signal_strength,
                    "confidence": confidence,
                    "expires_at": expires_at.strftime("%Y-%m-%d %H:%M:%S")
                }
                
                return {
                    "status": "success",
                    "is_valid": is_valid,
                    "validation_details": validation_details,
                    "message": "策略验证完成",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
        except Exception as e:
            raise Exception(f"验证策略失败: {str(e)}")

    def _row_to_strategy(self, row) -> Dict[str, Any]:
        """将数据库行转换为策略字典"""
        columns = [
            'id', 'trading_pair', 'timeframe', 'signal_type', 'signal_strength',
            'entry_price', 'stop_loss', 'take_profit', 'risk_level', 'confidence',
            'created_at', 'expires_at', 'is_active', 'metadata'
        ]
        
        strategy = dict(zip(columns, row))
        
        # 解析metadata
        if strategy['metadata']:
            try:
                strategy['metadata'] = json.loads(strategy['metadata'])
            except:
                strategy['metadata'] = {}
        
        return strategy

    def close(self):
        """关闭资源"""
        pass


if __name__ == '__main__':
    # 测试策略工具
    tool = StrategyTool()
    
    # 测试获取最新策略
    result = tool.run(action="get_latest", trading_pair="BTC", timeframe="1h")
    print("获取最新策略结果:", json.dumps(result, indent=2, ensure_ascii=False))
    
    # 测试验证策略
    if result['status'] == 'success' and result['strategy']:
        strategy_id = result['strategy']['id']
        validation_result = tool.run(action="validate", strategy_id=strategy_id)
        print("策略验证结果:", json.dumps(validation_result, indent=2, ensure_ascii=False))
