from sqlalchemy import create_engine, Column, String, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from typing import List, Dict

# 数据库连接信息
DATABASE_URI = 'mysql+pymysql://Mysql5.7:a78d04a8027589c3@43.156.238.66:6221/Mysql5.7'  # 请根据实际情况修改

engine = create_engine(DATABASE_URI)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

class JINSHINews(Base):
    __tablename__ = 'jinshi_lives_news'

    id = Column(String(255), primary_key=True)
    content = Column(Text)
    timestamp = Column(String(255))
    upvotes = Column(String(255))

def init_db():
    """
    创建数据库表
    """
    Base.metadata.create_all(bind=engine)

def news_exists(session, news_id: str) -> bool:
    """
    检查指定ID的新闻是否已存在于数据库中

    参数:
        session: SQLAlchemy会话对象
        news_id (str): 要检查的新闻ID

    返回:
        bool: 如果新闻存在则返回True，否则返回False
    """
    return session.query(JINSHINews).filter(JINSHINews.id == news_id).first() is not None

def batch_insert_news(news_list: List[Dict]):
    """
    将新闻列表批量存储到数据库中

    参数:
        news_list (List[Dict]): 包含新闻字典的列表
    """
    db = SessionLocal()
    try:
        for news in news_list:
            # 如果新闻已经存在，则跳过
            if news_exists(db, news["id"]):
                print(f"新闻 {news['id']} 已存在，跳过插入。")
                continue
            
            # 创建一个新的新闻对象
            db_news = JINSHINews(
                id=news["id"],
                content=news["content"],
                timestamp=news["timestamp"],
                upvotes=news["upvotes"]
            )
            db.add(db_news)
        
        db.commit()
    except Exception as e:
        db.rollback()
        print(f"Error storing news in database: {e}")
    finally:
        db.close()

def get_latest_news(limit=20):
    """
    查询最新的新闻记录，按id倒序排列，并限制返回的记录数量

    参数:
        limit (int): 返回的记录数量，默认为20

    返回:
        List[JINSHINews]: 最新的新闻记录列表
    """
    db = SessionLocal()
    try:
        # 查询并按id倒序排列，限制返回的记录数量
        results = db.query(JINSHINews).order_by(JINSHINews.id.desc()).limit(limit).all()
        return [dict(id=row.id, content=row.content, timestamp=row.timestamp, upvotes=row.upvotes) for row in results]
    finally:
        db.close()



if __name__ == '__main__':
    news = get_latest_news()
    print(len(news))
    for  n in news:
        print(n.content)