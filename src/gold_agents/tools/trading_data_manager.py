#!/usr/bin/env python3
"""
交易数据管理器 - 负责保存和管理所有交易相关数据
"""

import json
import sqlite3
import uuid
from datetime import datetime
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field
from crewai_tools import BaseTool


class TradingDataManagerSchema(BaseModel):
    """交易数据管理器的输入参数模式"""
    action: str = Field(..., description="操作类型：save_session, save_trade, save_agent_output, get_session_data")
    session_id: Optional[str] = Field(None, description="会话ID")
    data: Optional[dict] = Field(None, description="要保存的数据")


class TradingDataManager(BaseTool):
    """交易数据管理器"""
    name: str = "TradingDataManager"
    description: str = """
    这是一个用于管理交易数据的工具。
    主要功能：
    1. 保存交易会话数据
    2. 记录每个Agent的输入输出
    3. 保存交易执行结果
    4. 提供数据查询功能
    
    使用示例：
    1. 开始新会话：
       tool.run(action="save_session", data={"trading_pair": "BTC"})
    
    2. 保存Agent输出：
       tool.run(action="save_agent_output", session_id="xxx", data={...})
    
    3. 保存交易记录：
       tool.run(action="save_trade", session_id="xxx", data={...})
    """
    args_schema: type[BaseModel] = TradingDataManagerSchema

    def __init__(self, db_path: str = "trading_data.db", **kwargs):
        """初始化交易数据管理器"""
        super().__init__(**kwargs)
        self.db_path = db_path
        self.current_session_id = None
        self._init_database()

    def _init_database(self):
        """初始化数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建交易会话表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS trading_sessions (
                        id TEXT PRIMARY KEY,
                        trading_pair TEXT NOT NULL,
                        start_time DATETIME NOT NULL,
                        end_time DATETIME,
                        initial_balance REAL,
                        final_balance REAL,
                        total_pnl REAL,
                        total_trades INTEGER DEFAULT 0,
                        winning_trades INTEGER DEFAULT 0,
                        status TEXT DEFAULT 'active', -- active, completed, failed
                        metadata TEXT, -- JSON格式的额外信息
                        created_at DATETIME NOT NULL
                    )
                ''')
                
                # 创建Agent输出记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS agent_outputs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT NOT NULL,
                        agent_name TEXT NOT NULL,
                        task_name TEXT NOT NULL,
                        input_data TEXT NOT NULL, -- JSON格式
                        output_data TEXT NOT NULL, -- JSON格式
                        execution_time REAL NOT NULL,
                        success BOOLEAN NOT NULL,
                        error_message TEXT,
                        timestamp DATETIME NOT NULL,
                        FOREIGN KEY (session_id) REFERENCES trading_sessions (id)
                    )
                ''')
                
                # 创建交易执行记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS trade_executions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT NOT NULL,
                        strategy_id INTEGER,
                        order_id TEXT,
                        trading_pair TEXT NOT NULL,
                        action TEXT NOT NULL, -- open_long, open_short, close_long, close_short
                        order_type TEXT NOT NULL, -- market, limit
                        entry_price REAL,
                        exit_price REAL,
                        size REAL NOT NULL,
                        leverage INTEGER,
                        stop_loss REAL,
                        take_profit REAL,
                        actual_pnl REAL,
                        commission REAL,
                        slippage REAL,
                        entry_time DATETIME NOT NULL,
                        exit_time DATETIME,
                        duration_minutes INTEGER,
                        status TEXT NOT NULL, -- pending, filled, partial, cancelled, closed
                        notes TEXT,
                        agent_decisions TEXT, -- JSON格式，记录相关Agent决策
                        FOREIGN KEY (session_id) REFERENCES trading_sessions (id)
                    )
                ''')
                
                # 创建风险事件记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS risk_events (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT NOT NULL,
                        event_type TEXT NOT NULL, -- stop_loss, margin_call, max_drawdown, etc.
                        severity TEXT NOT NULL, -- low, medium, high, critical
                        description TEXT NOT NULL,
                        triggered_by TEXT, -- 触发的Agent或条件
                        action_taken TEXT, -- 采取的行动
                        impact REAL, -- 影响金额
                        timestamp DATETIME NOT NULL,
                        FOREIGN KEY (session_id) REFERENCES trading_sessions (id)
                    )
                ''')
                
                # 创建性能指标表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS performance_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT NOT NULL,
                        metric_name TEXT NOT NULL,
                        metric_value REAL NOT NULL,
                        calculation_time DATETIME NOT NULL,
                        FOREIGN KEY (session_id) REFERENCES trading_sessions (id)
                    )
                ''')
                
                conn.commit()
                
        except Exception as e:
            print(f"初始化交易数据库失败: {str(e)}")
            raise

    def _run(self, **kwargs: Any) -> Dict[str, Any]:
        """执行数据管理操作"""
        try:
            action = kwargs.get('action')
            
            if action == 'start_session':
                return self._start_trading_session(kwargs.get('data', {}))
            elif action == 'save_agent_output':
                return self._save_agent_output(kwargs.get('session_id'), kwargs.get('data', {}))
            elif action == 'save_trade':
                return self._save_trade_execution(kwargs.get('session_id'), kwargs.get('data', {}))
            elif action == 'save_risk_event':
                return self._save_risk_event(kwargs.get('session_id'), kwargs.get('data', {}))
            elif action == 'end_session':
                return self._end_trading_session(kwargs.get('session_id'), kwargs.get('data', {}))
            elif action == 'get_session_data':
                return self._get_session_data(kwargs.get('session_id'))
            else:
                return {
                    "status": "error",
                    "message": f"不支持的操作类型: {action}",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
        except Exception as e:
            return {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

    def _start_trading_session(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """开始新的交易会话"""
        try:
            session_id = str(uuid.uuid4())
            self.current_session_id = session_id
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO trading_sessions 
                    (id, trading_pair, start_time, initial_balance, metadata, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (
                    session_id,
                    data.get('trading_pair', 'BTC'),
                    datetime.now(),
                    data.get('initial_balance', 0.0),
                    json.dumps(data.get('metadata', {}), ensure_ascii=False),
                    datetime.now()
                ))
                conn.commit()
            
            return {
                "status": "success",
                "session_id": session_id,
                "message": "交易会话已开始",
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            raise Exception(f"开始交易会话失败: {str(e)}")

    def _save_agent_output(self, session_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """保存Agent输出数据"""
        try:
            if not session_id:
                session_id = self.current_session_id
            
            if not session_id:
                raise ValueError("未指定会话ID")
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO agent_outputs 
                    (session_id, agent_name, task_name, input_data, output_data, 
                     execution_time, success, error_message, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    session_id,
                    data.get('agent_name', ''),
                    data.get('task_name', ''),
                    json.dumps(data.get('input_data', {}), ensure_ascii=False),
                    json.dumps(data.get('output_data', {}), ensure_ascii=False),
                    data.get('execution_time', 0.0),
                    data.get('success', True),
                    data.get('error_message'),
                    datetime.now()
                ))
                conn.commit()
            
            return {
                "status": "success",
                "message": "Agent输出已保存",
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            raise Exception(f"保存Agent输出失败: {str(e)}")

    def _save_trade_execution(self, session_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """保存交易执行记录"""
        try:
            if not session_id:
                session_id = self.current_session_id
            
            if not session_id:
                raise ValueError("未指定会话ID")
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 保存交易记录
                cursor.execute('''
                    INSERT INTO trade_executions 
                    (session_id, strategy_id, order_id, trading_pair, action, order_type,
                     entry_price, exit_price, size, leverage, stop_loss, take_profit,
                     actual_pnl, commission, slippage, entry_time, exit_time, 
                     duration_minutes, status, notes, agent_decisions)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    session_id,
                    data.get('strategy_id'),
                    data.get('order_id'),
                    data.get('trading_pair', 'BTC'),
                    data.get('action'),
                    data.get('order_type'),
                    data.get('entry_price'),
                    data.get('exit_price'),
                    data.get('size'),
                    data.get('leverage'),
                    data.get('stop_loss'),
                    data.get('take_profit'),
                    data.get('actual_pnl'),
                    data.get('commission'),
                    data.get('slippage'),
                    data.get('entry_time', datetime.now()),
                    data.get('exit_time'),
                    data.get('duration_minutes'),
                    data.get('status', 'pending'),
                    data.get('notes'),
                    json.dumps(data.get('agent_decisions', {}), ensure_ascii=False)
                ))
                
                # 更新会话统计
                if data.get('status') == 'closed' and data.get('actual_pnl') is not None:
                    cursor.execute('''
                        UPDATE trading_sessions 
                        SET total_trades = total_trades + 1,
                            winning_trades = winning_trades + ?,
                            total_pnl = COALESCE(total_pnl, 0) + ?
                        WHERE id = ?
                    ''', (
                        1 if data.get('actual_pnl', 0) > 0 else 0,
                        data.get('actual_pnl', 0),
                        session_id
                    ))
                
                conn.commit()
            
            return {
                "status": "success",
                "message": "交易记录已保存",
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            raise Exception(f"保存交易记录失败: {str(e)}")

    def _save_risk_event(self, session_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """保存风险事件"""
        try:
            if not session_id:
                session_id = self.current_session_id
            
            if not session_id:
                raise ValueError("未指定会话ID")
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO risk_events 
                    (session_id, event_type, severity, description, triggered_by, 
                     action_taken, impact, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    session_id,
                    data.get('event_type'),
                    data.get('severity', 'medium'),
                    data.get('description'),
                    data.get('triggered_by'),
                    data.get('action_taken'),
                    data.get('impact', 0.0),
                    datetime.now()
                ))
                conn.commit()
            
            return {
                "status": "success",
                "message": "风险事件已记录",
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            raise Exception(f"保存风险事件失败: {str(e)}")

    def _end_trading_session(self, session_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """结束交易会话"""
        try:
            if not session_id:
                session_id = self.current_session_id
            
            if not session_id:
                raise ValueError("未指定会话ID")
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE trading_sessions 
                    SET end_time = ?, final_balance = ?, status = ?
                    WHERE id = ?
                ''', (
                    datetime.now(),
                    data.get('final_balance'),
                    data.get('status', 'completed'),
                    session_id
                ))
                conn.commit()
            
            # 清除当前会话ID
            if session_id == self.current_session_id:
                self.current_session_id = None
            
            return {
                "status": "success",
                "message": "交易会话已结束",
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            raise Exception(f"结束交易会话失败: {str(e)}")

    def _get_session_data(self, session_id: str) -> Dict[str, Any]:
        """获取会话数据"""
        try:
            if not session_id:
                session_id = self.current_session_id
            
            if not session_id:
                raise ValueError("未指定会话ID")
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 获取会话基本信息
                cursor.execute('SELECT * FROM trading_sessions WHERE id = ?', (session_id,))
                session_row = cursor.fetchone()
                
                if not session_row:
                    raise ValueError(f"会话 {session_id} 不存在")
                
                # 获取Agent输出记录
                cursor.execute('SELECT * FROM agent_outputs WHERE session_id = ? ORDER BY timestamp', (session_id,))
                agent_outputs = cursor.fetchall()
                
                # 获取交易记录
                cursor.execute('SELECT * FROM trade_executions WHERE session_id = ? ORDER BY entry_time', (session_id,))
                trades = cursor.fetchall()
                
                # 获取风险事件
                cursor.execute('SELECT * FROM risk_events WHERE session_id = ? ORDER BY timestamp', (session_id,))
                risk_events = cursor.fetchall()
                
                return {
                    "status": "success",
                    "session_data": {
                        "session_info": dict(zip([col[0] for col in cursor.description], session_row)) if session_row else None,
                        "agent_outputs": [dict(zip([col[0] for col in cursor.description], row)) for row in agent_outputs],
                        "trades": [dict(zip([col[0] for col in cursor.description], row)) for row in trades],
                        "risk_events": [dict(zip([col[0] for col in cursor.description], row)) for row in risk_events]
                    },
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
        except Exception as e:
            raise Exception(f"获取会话数据失败: {str(e)}")

    def get_current_session_id(self) -> Optional[str]:
        """获取当前会话ID"""
        return self.current_session_id

    def create_session_context(self, trading_pair: str = "BTC", initial_balance: float = 10000.0) -> str:
        """创建会话上下文（便捷方法）"""
        result = self._start_trading_session({
            "trading_pair": trading_pair,
            "initial_balance": initial_balance,
            "metadata": {
                "created_by": "trading_system",
                "purpose": "live_trading"
            }
        })
        
        if result["status"] == "success":
            return result["session_id"]
        else:
            raise Exception(f"创建会话失败: {result['message']}")


if __name__ == '__main__':
    # 测试交易数据管理器
    manager = TradingDataManager()
    
    # 创建新会话
    session_result = manager.run(action="start_session", data={
        "trading_pair": "BTC",
        "initial_balance": 10000.0
    })
    print("会话创建结果:", session_result)
    
    if session_result["status"] == "success":
        session_id = session_result["session_id"]
        
        # 保存Agent输出
        agent_result = manager.run(action="save_agent_output", session_id=session_id, data={
            "agent_name": "trader",
            "task_name": "execute_trade",
            "input_data": {"trading_pair": "BTC", "action": "open_long"},
            "output_data": {"status": "success", "order_id": "12345"},
            "execution_time": 1.5,
            "success": True
        })
        print("Agent输出保存结果:", agent_result)
