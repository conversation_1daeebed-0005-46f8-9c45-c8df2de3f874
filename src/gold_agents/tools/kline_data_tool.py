
from typing import Any, Optional, Type

from crewai.tools import BaseTool
from pydantic import BaseModel, Field
from .k_line_util import get_k_line_data


class Kline15minDataToolSchema(BaseModel):
    k_line_cycle: int = Field(..., description="K线周期类型 ")


class Kline15minKlineDataTool(BaseTool):
    name: str = "现货黄金K线数据获取工具"
    description: str = ("用于获取合约BTCUSDT的历史K线数据。"
                        "目前主要支持获取15minK线数据。"
                        "默认获取最近交易日的15min K线数据。")
    args_schema: Type[BaseModel] = Kline15minDataToolSchema
    k_line_cycle: Optional[int] = 3

    def __init__(self, k_line_cycle: Optional[int] = 3, **kwargs: Any) -> None:
        super().__init__(**kwargs)
        self.k_line_cycle = k_line_cycle

    def _run(self, k_line_cycle: int = 3) -> str:
        try:
            return get_k_line_data(3)
        except ModuleNotFoundError:
            return "获取K线数据失败"
        except Exception as e:
            return f"获取现货黄金 XAU/USD K线数据时出错: {e}"


class Kline1hKlineDataToolSchema(BaseModel):
    k_line_cycle: int = Field(..., description="K线周期类型 ")


class Kline1hKlineDataTool(BaseTool):
    name: str = "现货黄金K线数据获取工具"
    description: str = ("用于获取合约BTCUSDT的历史K线数据。"
                        "目前主要支持获取 1h K线数据。" )
    args_schema: Type[BaseModel] = Kline1hKlineDataToolSchema
    k_line_cycle: Optional[int] = 5

    def __init__(self, k_line_cycle: Optional[int] = 5, **kwargs: Any) -> None:
        super().__init__(**kwargs)
        self.k_line_cycle = k_line_cycle

    def _run(self, k_line_cycle: int = 5) -> str:
        try:
            return get_k_line_data(5)
        except ModuleNotFoundError:
            return "获取K线数据失败"
        except Exception as e:
            return f"获取现货黄金 XAU/USD K线数据时出错: {e}"



class Kline4hKlineDataToolSchema(BaseModel):
    k_line_cycle: int = Field(..., description="K线周期类型 ")


class Kline4hKlineDataTool(BaseTool):
    name: str = "现货黄金K线数据获取工具"
    description: str = ("用于获取合约BTCUSDT的历史K线数据。"
                        "目前主要支持获取4h K线数据。")
    args_schema: Type[BaseModel] = Kline4hKlineDataToolSchema
    k_line_cycle: Optional[int] = 7

    def __init__(self, k_line_cycle: Optional[int] = 7, **kwargs: Any) -> None:
        super().__init__(**kwargs)
        self.k_line_cycle = k_line_cycle

    def _run(self, k_line_cycle: int = 7) -> str:
        try:
            return get_k_line_data(7)
        except ModuleNotFoundError:
            return "获取K线数据失败"
        except Exception as e:
            return f"获取现货黄金 XAU/USD K线数据时出错: {e}"


class Kline1dKlineDataToolSchema(BaseModel):
    k_line_cycle: int = Field(..., description="K线周期类型 ")


class Kline1dKlineDataTool(BaseTool):
    name: str = "现货黄金K线数据获取工具"
    description: str = ("用于获取合约BTCUSDT的历史K线数据。"
                        "目前主要支持获取1d K线数据。" )
    args_schema: Type[BaseModel] = Kline1dKlineDataToolSchema
    k_line_cycle: Optional[int] = 8

    def __init__(self, k_line_cycle: Optional[int] = 8, **kwargs: Any) -> None:
        super().__init__(**kwargs)
        self.k_line_cycle = k_line_cycle

    def _run(self, k_line_cycle: int = 8) -> str:
        try:
            return get_k_line_data(8)
        except ModuleNotFoundError:
            return "获取K线数据失败"
        except Exception as e:
            return f"获取现货黄金 XAU/USD K线数据时出错: {e}"

if __name__ == '__main__':
    tool = Kline1dKlineDataTool()
    data_daily = tool.run(k_line_cycle=3)
    print(data_daily)

