from sqlalchemy import create_engine, Column, String, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from typing import List, Dict

# 数据库连接信息
DATABASE_URI = 'mysql+pymysql://Mysql5.7:a78d04a8027589c3@43.156.238.66:6221/Mysql5.7'  # 请根据实际情况修改

engine = create_engine(DATABASE_URI)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()


class JINSHIHotNews(Base):
    __tablename__ = 'jinshi_hot_news'

    id = Column(String(255), primary_key=True)
    title = Column(String(255))
    content_type = Column(String(255))
    jump_url = Column(String(255))
    jump_type = Column(String(255))
    is_hot = Column(String(255))
    cover = Column(String(255))
    refreshed_at = Column(String(255))
    published_at = Column(String(255))
    short_title = Column(String(255))
    content = Column(Text)


def init_db():
    """
    创建数据库表
    """
    Base.metadata.create_all(bind=engine)


def hot_news_exists(session, news_id: str) -> bool:
    """
    检查指定ID的热门新闻是否已存在于数据库中

    参数:
        session: SQLAlchemy会话对象
        news_id (str): 要检查的新闻ID

    返回:
        bool: 如果新闻存在则返回True，否则返回False
    """
    return session.query(JINSHIHotNews).filter(JINSHIHotNews.id == news_id).first() is not None


def batch_insert_hot_news(hot_news_list: List[Dict]):
    """
    将热门新闻列表批量存储到数据库中

    参数:
        hot_news_list (List[Dict]): 包含热门新闻字典的列表
    """
    db = SessionLocal()
    try:
        for news in hot_news_list:
            # 如果新闻已经存在，则跳过
            if hot_news_exists(db, news["id"]):
                print(f"热门新闻 {news['id']} 已存在，跳过插入。")
                continue

            # 创建一个新的热门新闻对象
            db_hot_news = JINSHIHotNews(
                id=news["id"],
                title=news["title"],
                content_type=news["content_type"],
                jump_url=news["jump_url"],
                jump_type=news["jump_type"],
                is_hot=news["is_hot"],
                cover=news["cover"],
                refreshed_at=news["refreshed_at"],
                published_at=news["published_at"],
                short_title=news["short_title"],
                content=news["content"]
            )
            db.add(db_hot_news)

        db.commit()
    except Exception as e:
        db.rollback()
        print(f"Error storing hot news in database: {e}")
    finally:
        db.close()

def get_latest_hot_news(limit=20):
    """
    查询最新的新闻记录，按id倒序排列，并限制返回的记录数量

    参数:
        limit (int): 返回的记录数量，默认为20

    返回:
        List[JINSHINews]: 最新的新闻记录列表
    """
    db = SessionLocal()
    try:
        # 查询并按id倒序排列，限制返回的记录数量
        result = db.query(JINSHIHotNews).order_by(JINSHIHotNews.id.desc()).limit(limit).all()
        return [dict(id=row.id, title=row.title, content_type=row.content_type,
                     refreshed_at=row.refreshed_at, published_at=row.published_at,
                     short_title=row.short_title, content=row.content) for row in result]
    finally:
        db.close()
if __name__ == '__main__':
    news = get_latest_hot_news()
    print(len(news))
    for  n in news:
        print(n.content)