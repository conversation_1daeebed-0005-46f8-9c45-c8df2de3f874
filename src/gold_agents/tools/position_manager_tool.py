import logging
import yaml
from typing import Dict, List, Optional
import numpy as np
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class PositionManagerTool:
    """仓位管理工具类，用于管理交易仓位"""
    
    def __init__(self, config_path: str):
        """初始化仓位管理工具
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self._init_position_metrics()
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            配置信息
        """
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
            raise
            
    def _init_position_metrics(self):
        """初始化仓位指标"""
        self.position_history = []
        self.correlation_matrix = {}
        self.last_rebalance_time = datetime.now()
        
    def calculate_dynamic_position(self, base_position: float, market_conditions: Dict) -> float:
        """计算动态仓位
        
        Args:
            base_position: 基础仓位
            market_conditions: 市场条件
            
        Returns:
            调整后的仓位
        """
        try:
            if not self.config['position_management']['dynamic']['enable_dynamic']:
                return base_position
                
            # 获取配置
            max_adjustment = self.config['position_management']['dynamic']['max_adjustment']
            
            # 计算调整因子
            adjustment_factor = 1.0
            
            # 根据波动率调整
            if 'volatility' in market_conditions:
                volatility = market_conditions['volatility']
                max_volatility = self.config['risk_control']['volatility']['max_volatility']
                if volatility > max_volatility:
                    adjustment_factor *= (1 - (volatility - max_volatility) / max_volatility)
                    
            # 根据流动性调整
            if 'liquidity' in market_conditions:
                liquidity = market_conditions['liquidity']
                min_depth = self.config['risk_control']['liquidity']['min_depth']
                if liquidity < min_depth:
                    adjustment_factor *= (liquidity / min_depth)
                    
            # 根据情绪调整
            if 'sentiment' in market_conditions:
                sentiment = market_conditions['sentiment']
                threshold = self.config['risk_control']['sentiment']['sentiment_threshold']
                if abs(sentiment) > threshold:
                    adjustment_factor *= (1 - abs(sentiment - threshold) / threshold)
                    
            # 限制调整范围
            adjustment_factor = max(1 - max_adjustment, min(1 + max_adjustment, adjustment_factor))
            
            # 计算最终仓位
            final_position = base_position * adjustment_factor
            
            # 确保在限制范围内
            max_position = self.config['risk_control']['position']['max_position_ratio']
            min_position = self.config['risk_control']['position']['min_position_ratio']
            
            return max(min_position, min(max_position, final_position))
            
        except Exception as e:
            logger.error(f"计算动态仓位失败: {str(e)}")
            return base_position
            
    def check_correlation(self, symbol: str, positions: List[Dict]) -> Dict:
        """检查相关性
        
        Args:
            symbol: 交易对
            positions: 当前持仓列表
            
        Returns:
            相关性信息
        """
        try:
            if not self.config['position_management']['correlation']['enable_correlation']:
                return {'is_correlated': False, 'reason': '相关性分析未启用'}
                
            # 获取配置
            threshold = self.config['position_management']['correlation']['correlation_threshold']
            max_positions = self.config['position_management']['correlation']['max_correlated_positions']
            
            # 更新相关性矩阵
            if symbol not in self.correlation_matrix:
                self.correlation_matrix[symbol] = {}
                
            # 检查相关性
            correlated_positions = []
            for position in positions:
                if position['symbol'] in self.correlation_matrix[symbol]:
                    correlation = self.correlation_matrix[symbol][position['symbol']]
                    if correlation > threshold:
                        correlated_positions.append(position['symbol'])
                        
            is_correlated = len(correlated_positions) >= max_positions
            
            return {
                'is_correlated': is_correlated,
                'correlated_positions': correlated_positions,
                'threshold': threshold,
                'max_positions': max_positions,
                'reason': '相关性过高' if is_correlated else None
            }
            
        except Exception as e:
            logger.error(f"检查相关性失败: {str(e)}")
            return {'is_correlated': False, 'reason': f'检查失败: {str(e)}'}
            
    def optimize_allocation(self, positions: List[Dict], account_balance: float) -> Dict:
        """优化资金分配
        
        Args:
            positions: 当前持仓列表
            account_balance: 账户余额
            
        Returns:
            优化后的资金分配
        """
        try:
            if not self.config['position_management']['allocation']['enable_optimization']:
                return {'positions': positions, 'reason': '资金优化未启用'}
                
            # 检查是否需要再平衡
            now = datetime.now()
            if now - self.last_rebalance_time < timedelta(hours=self.config['position_management']['allocation']['rebalance_interval']):
                return {'positions': positions, 'reason': '未达到再平衡间隔'}
                
            # 获取配置
            min_allocation = self.config['position_management']['allocation']['min_allocation']
            
            # 计算目标分配
            total_value = sum(position['value'] for position in positions)
            if total_value == 0:
                return {'positions': positions, 'reason': '无持仓'}
                
            # 计算每个持仓的目标价值
            target_allocations = {}
            for position in positions:
                # 根据风险调整目标分配
                risk_factor = 1.0
                if 'volatility' in position:
                    risk_factor *= (1 - position['volatility'])
                if 'correlation' in position:
                    risk_factor *= (1 - position['correlation'])
                    
                target_allocations[position['symbol']] = {
                    'target_value': account_balance * min_allocation * risk_factor,
                    'current_value': position['value']
                }
                
            # 计算调整
            adjustments = {}
            for symbol, allocation in target_allocations.items():
                adjustments[symbol] = allocation['target_value'] - allocation['current_value']
                
            # 更新再平衡时间
            self.last_rebalance_time = now
            
            return {
                'positions': positions,
                'adjustments': adjustments,
                'reason': '资金分配已优化'
            }
            
        except Exception as e:
            logger.error(f"优化资金分配失败: {str(e)}")
            return {'positions': positions, 'reason': f'优化失败: {str(e)}'}
            
    def calculate_order_splits(self, order_size: float, market_depth: Dict) -> List[Dict]:
        """计算订单拆分
        
        Args:
            order_size: 订单规模
            market_depth: 市场深度数据
            
        Returns:
            拆分后的订单列表
        """
        try:
            if not self.config['execution']['order_splitting']['enable_splitting']:
                return [{'size': order_size, 'price': None}]
                
            # 获取配置
            max_split = self.config['execution']['order_splitting']['max_split']
            min_split_size = self.config['execution']['order_splitting']['min_split_size']
            
            # 计算市场深度
            depth_levels = []
            for side in ['bids', 'asks']:
                for level in market_depth[side]:
                    depth_levels.append({
                        'price': level['price'],
                        'size': level['size'],
                        'side': side
                    })
                    
            # 按价格排序
            depth_levels.sort(key=lambda x: x['price'])
            
            # 计算拆分
            splits = []
            remaining_size = order_size
            
            for level in depth_levels:
                if remaining_size <= 0:
                    break
                    
                split_size = min(remaining_size, level['size'])
                if split_size >= min_split_size:
                    splits.append({
                        'size': split_size,
                        'price': level['price']
                    })
                    remaining_size -= split_size
                    
            # 如果还有剩余，添加到最后一个拆分
            if remaining_size > 0 and splits:
                splits[-1]['size'] += remaining_size
                
            # 限制拆分数量
            if len(splits) > max_split:
                # 合并较小的拆分
                splits.sort(key=lambda x: x['size'])
                while len(splits) > max_split:
                    smallest = splits.pop(0)
                    splits[0]['size'] += smallest['size']
                    
            return splits
            
        except Exception as e:
            logger.error(f"计算订单拆分失败: {str(e)}")
            return [{'size': order_size, 'price': None}] 