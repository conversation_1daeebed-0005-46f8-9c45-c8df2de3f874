from datetime import datetime, timezone, timedelta  # 直接导入datetime类和时区组件
from sqlalchemy import create_engine, Column, String, Text, BigInteger
from sqlalchemy.orm import declarative_base, sessionmaker
from typing import List, Dict
import json
import pandas as pd
import numpy as np
# import pandas_ta as ta
from datetime import datetime

DATABASE_URI = 'mysql+pymysql://Mysql5.7:a78d04a8027589c3@43.156.238.66:6221/Mysql5.7'

engine = create_engine(DATABASE_URI)
session_factory = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

class MarketData(Base):
    __tablename__ = 'market_data'

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    symbol = Column(String(255))
    type = Column(String(255))
    open_time = Column(String(255))
    open = Column(String(255))
    high = Column(String(255))
    low = Column(String(255))
    close = Column(String(255))
    volume = Column(String(255))
    quote_asset_volume = Column(String(255))

def init_db():
    """
    创建数据库表
    """
    Base.metadata.create_all(bind=engine)

def data_exists(session, data_type: str, open_time: str) -> bool:
    # print(f"data_type {data_type}  open_time {open_time}")
    data = session.query(MarketData).filter(
        MarketData.type == data_type,
        MarketData.open_time == open_time
    ).first()
    return data  is not None

def batch_insert_market_data(data_list: List[Dict]):
    """
    将市场数据列表批量存储到数据库中

    参数:
        data_list (List[Dict]): 包含市场数据字典的列表
    """
    db = session_factory()
    try:
        for data in data_list:
            # // 如果数据已经存在，则跳过
            if data_exists(db, data["type"], data["open_time"]):
                # print(f"数据 {data['open_time']} 已存在，跳过插入。")
                continue
            # // 创建一个新的数据对象
            print(f"正在插入数据 {data['open_time']}")
            db_data = MarketData(
                # id=data["id"],
                symbol=data["symbol"],
                type=data["type"],
                open_time=data["open_time"],
                open=data["open"],
                high=data["high"],
                low=data["low"],
                close=data["close"],
                volume=data["volume"],
                quote_asset_volume=data["quote_asset_volume"]
            )
            db.add(db_data)
        
        db.commit()
    except Exception as e:
        db.rollback()
        print(f"Error storing market data in database: {e}")
    finally:
        db.close()

def get_market_data_by_symbol(symbol: str ="BTCUSDT",type :str ="15m", limit=100):
    """
    根据symbol查询市场数据，按id倒序排列，并限制返回的记录数量

    参数:
        symbol (str): 要查询的symbol值
        limit (int): 返回的记录数量，默认为100

    返回:
        List[MarketData]: 市场数据记录列表
    """
    db = session_factory()
    try:
        # 查询指定symbol的数据，并按id倒序排列，限制返回的记录数量
        return db.query(MarketData).filter(MarketData.symbol == symbol ,MarketData.type == type).order_by(MarketData.id.desc()).limit(limit).all()
    finally:
        db.close()

#
# def calculate_technical_indicators(klines):
#     # 修改DataFrame初始化方式，使用对象属性访问
#     df = pd.DataFrame([{
#         'open_time': k.open_time,
#         'open': float(k.open),
#         'high': float(k.high),
#         'low': float(k.low),
#         'close': float(k.close),
#         'volume': float(k.volume),
#         'quote_asset_volume': float(k.quote_asset_volume)
#     } for k in klines])
#
#     numeric_cols = ['open', 'high', 'low', 'close', 'volume']
#     df[numeric_cols] = df[numeric_cols].apply(pd.to_numeric, axis=1)
#
#     close_prices = df['close'].values
#     volume_values = df['volume'].values
#
#     # 使用pandas_ta计算指标
#     # 布林带
#     bbands = ta.bbands(df['close'], length=20)
#     df['BB_UpperBand'] = bbands['BBU_20_2.0']
#     df['BB_MiddleBand'] = bbands['BBM_20_2.0']
#     df['BB_LowerBand'] = bbands['BBL_20_2.0']
#     upper = bbands['BBU_20_2.0'].values
#     lower = bbands['BBL_20_2.0'].values
#
#     # MACD
#     macd = ta.macd(df['close'], fast=12, slow=26, signal=9)
#     df['MACD'] = macd['MACD_12_26_9']
#     df['MACD_Signal'] = macd['MACDs_12_26_9']
#     df['MACD_Histogram'] = macd['MACDh_12_26_9']
#
#     # RSI
#     df['RSI'] = ta.rsi(df['close'], length=14)
#
#     # KDJ (STOCH)
#     stoch = ta.stoch(df['high'], df['low'], df['close'], k=9, d=3)
#     df['KDJ_K'] = stoch['STOCHk_9_3_3']
#     df['KDJ_D'] = stoch['STOCHd_9_3_3']
#     df['KDJ_J'] = 3 * stoch['STOCHk_9_3_3'] - 2 * stoch['STOCHd_9_3_3']
#
#     # ADX & DMI
#     adx = ta.adx(df['high'], df['low'], df['close'], length=14)
#     df['ADX'] = adx['ADX_14']
#     df['DMI +DI'] = adx['DMP_14']
#     df['DMI -DI'] = adx['DMN_14']
#     df['DMI_DI_Diff'] = adx['DMP_14'] - adx['DMN_14']
#
#     # CCI
#     df['CCI'] = ta.cci(df['high'], df['low'], df['close'], length=14)
#
#     # ATR
#     df['ATR'] = ta.atr(df['high'], df['low'], df['close'], length=14)
#
#     # OBV
#     df['OBV'] = ta.obv(df['close'], df['volume'])
#
#     # SAR
#     psar = ta.psar(df['high'], df['low'], df['close'], step=0.02, max=0.2)
#     df['SAR'] = psar['PSARl_0.02_0.2']
#
#     # ROC
#     df['ROC'] = ta.roc(df['close'], length=12)
#
#     # 威廉指标
#     df['Williams %R'] = ta.willr(df['high'], df['low'], df['close'], length=14)
#
#     # MA
#     df['MA5'] = ta.sma(df['close'], length=5)
#     df['MA10'] = ta.sma(df['close'], length=10)
#     df['MA20'] = ta.sma(df['close'], length=20)
#     df['MA30'] = ta.sma(df['close'], length=30)
#     df['MA60'] = ta.sma(df['close'], length=60)
#
#     # EMA
#     df['EMA5'] = ta.ema(df['close'], length=5)
#     df['EMA10'] = ta.ema(df['close'], length=10)
#     df['EMA20'] = ta.ema(df['close'], length=20)
#     df['EMA30'] = ta.ema(df['close'], length=30)
#     df['EMA12'] = ta.ema(df['close'], length=12)
#     df['EMA26'] = ta.ema(df['close'], length=26)
#
#     # TRIX
#     trix = ta.trix(df['close'], length=12)
#     trix_col = [col for col in trix.columns if "TRIX" in col]
#     if trix_col:
#         df['TRIX'] = trix[trix_col[0]]
#     else:
#         df['TRIX'] = np.nan
#
#     # VR（手动实现）
#     period = 26
#     up_volume = df['volume'].where(df['close'] > df['open'], 0)
#     down_volume = df['volume'].where(df['close'] < df['open'], 0)
#     same_volume = df['volume'].where(df['close'] == df['open'], 0)
#     df['VR'] = ((up_volume.rolling(window=period).sum() + 0.5 * same_volume.rolling(window=period).sum()) /
#                 (down_volume.rolling(window=period).sum() + 0.5 * same_volume.rolling(window=period).sum())) * 100
#
#     # MFI
#     df['MFI'] = ta.mfi(df['high'], df['low'], df['close'], df['volume'], length=14)
#
#     # TEMA
#     df['TEMA'] = ta.tema(df['close'], length=30)
#     # 线性回归斜率
#     df['LINEARREG_SLOPE'] = ta.linreg(df['close'], length=14)
#     # StochRSI
#     stochrsi = ta.stochrsi(df['close'], length=14, rsi_length=14, k=5, d=3)
#     df['Stochastic_RSI_FastK'] = stochrsi['STOCHRSIk_14_14_5_3']
#     df['Stochastic_RSI_FastD'] = stochrsi['STOCHRSId_14_14_5_3']
#     # MOM
#     df['MOM'] = ta.mom(df['close'], length=10)
#     # VWAP
#     df['open_time_dt'] = pd.to_datetime(pd.to_numeric(df['open_time']), unit='ms')
#     df.set_index('open_time_dt', inplace=True)
#     df['VWAP'] = ta.vwap(df['high'], df['low'], df['close'], df['volume'])
#     df.reset_index(drop=True, inplace=True)
#     # CMF
#     df['CMF'] = ta.cmf(df['high'], df['low'], df['close'], df['volume'], length=14)
#
#     # 原有特征保留
#     df['SMA50'] = ta.sma(df['close'], length=50)
#     df['SMA200'] = ta.sma(df['close'], length=200)
#     df['EMA12'] = ta.ema(df['close'], length=12)
#     df['EMA26'] = ta.ema(df['close'], length=26)
#
#     # 交叉信号前，所有涉及的列都填充0，彻底避免None/NaN
#     cross_cols = ['SMA30', 'SMA50', 'SMA200', 'EMA12', 'EMA26', 'EMA50', 'KAMA14', 'KAMA50']
#     for col in cross_cols:
#         if col in df.columns:
#             df[col] = df[col].fillna(0)
#
#     # 新增策略实现
#     # ================== 趋势策略 ==================
#     # SMA交叉策略增强
#     df['SMA_Cross'] = np.where(df['SMA50'] > df['SMA200'], 1, -1)  # 添加SMA原始交叉信号
#     df['EMA_Cross'] = np.where(df['EMA12'] > df['EMA26'], 1, -1)  # 补充EMA基础交叉信号
#     df['SMA30'] = ta.sma(df['close'], length=30)
#     df['SMA_Cross_30_50'] = np.where(df['SMA30'] > df['SMA50'], 1, -1)
#     df['SMA30_Exit'] = np.where(df['close'] < df['SMA30'], 1, 0)
#
#     # EMA交叉策略增强
#     df['EMA50'] = ta.ema(df['close'], length=50)
#     df['EMA_Cross_12_50'] = np.where(df['EMA12'] > df['EMA50'], 1, -1)
#
#     # KAMA交叉策略
#     df['KAMA14'] = ta.kama(df['close'], length=14)
#     df['KAMA50'] = ta.kama(df['close'], length=50)
#     df['KAMA_Cross'] = np.where(df['KAMA14'] > df['KAMA50'], 1, -1)
#     df['KAMA_Exit'] = np.where(
#         (df['close'] < df['KAMA14']) & (df['close'] < df['KAMA50']), 1, 0)
#
#     # ================== 动量策略 ==================
#     # StochRSI增强
#     stochrsi2 = ta.stochrsi(df['close'], length=14)
#     df['StochRSI_Fastk'] = stochrsi2['STOCHRSIk_14_14_3_3']
#     df['StochRSI_Fastd'] = stochrsi2['STOCHRSId_14_14_3_3']
#
#     # ConnorsRSI增强
#     df['ConnorsRSI'] = (ta.rsi(df['close'], length=3) +
#                         ta.roc(df['close'], length=2) / 100 * 0.5 +
#                         df['close'].pct_change().abs().rolling(3).mean() * 0.5)
#
#     # ================== 波动策略 ==================
#     # 布林带增强
#     bbands2 = ta.bbands(df['close'], length=20, std=2)
#     df['BB_Upper'] = bbands2['BBU_20_2.0']
#     df['BB_Mid'] = bbands2['BBM_20_2.0']
#     df['BB_Lower'] = bbands2['BBL_20_2.0']
#     df['BB_Width'] = df['BB_Upper'] - df['BB_Lower']  # 布林带宽度指标
#
#     # ================== 成交量策略 ==================
#     # OBV增强（OVUN策略）
#     df['OBV'] = ta.obv(df['close'], df['volume'])
#     df['OBV_EMA20'] = ta.ema(df['OBV'], length=20)  # OBV动量指标
#
#     # MFI策略增强
#     df['MFI'] = ta.mfi(df['high'], df['low'], df['close'], df['volume'], length=14)
#
#     # ================== 离场信号 ==================
#     # SMA平仓信号
#     df['SMA_Exit_30'] = np.where(df['close'] < df['SMA30'], 1, 0)
#
#     # 布林带离场
#     df['BB_Exit'] = np.where(df['close'] > df['BB_Upper'], 1, 0)
#
#     # ================== 复合策略 ==================
#     # SMASIG策略（SMA信号组合）
#     df['SMASIG'] = df['SMA_Cross_30_50'] + df['SMA_Cross']
#     # 添加空值处理
#     df.fillna(method='ffill', inplace=True)
#     df.fillna(0, inplace=True)  # 最终兜底填充
#     df.dropna(subset=['SMA50', 'SMA200'], inplace=True)
#     # SMA-KAMA组合策略
#     df['SMA_KAMA_Signal'] = np.where(
#         (df['SMA50'] > df['KAMA50']) & (df['EMA12'] > df['EMA26']), 1, 0)
#     key_columns = ['MACD', 'RSI', 'BB_UpperBand', 'KDJ_K', 'ADX', 'CCI', 'ATR', 'OBV', 'SAR', 'ROC', 'Williams %R',
#                    'MA5', 'EMA5']
#     print("现有列:", df.columns.tolist())
#     df = df.dropna(subset=key_columns)
#
#     result = {
#         'price': close_prices[-1],
#         'bollinger_upper': round(upper[-1], 2),
#         'bollinger_lower': round(lower[-1], 2),
#         'volume_24h': int(volume_values.sum()),
#         "candles": []}
#
#     for index, row in df.iterrows():
#         ts = int(row['open_time'])
#         # 使用UTC+8时区转换时间戳
#         dt_obj = datetime.fromtimestamp(ts / 1000, tz=timezone(timedelta(hours=8)))  # 直接使用datetime类
#
#         candle = {
#             "open_time": ts,
#             "datetime": dt_obj.strftime("%Y-%m-%d %H:%M:%S"),  # 显示UTC+8时间
#             "open": round(row['open'], 4),
#             "high": round(row['high'], 4),
#             "low": round(row['low'], 4),
#             "close": round(row['close'], 4),
#             "volume": round(row['volume'], 4),
#             "indicators": {
#                 # 基础技术指标
#                 "MACD": round(row['MACD'], 4),
#                 "RSI": round(row['RSI'], 2),
#                 "BB": {
#                     "upper": round(row['BB_UpperBand'], 2),
#                     "middle": round(row['BB_MiddleBand'], 2),
#                     "lower": round(row['BB_LowerBand'], 2)
#                 },
#                 # 趋势信号指标
#                 "trend": {
#                     "SMA_Cross": int(row['SMA_Cross']),
#                     "EMA_Cross": int(row['EMA_Cross']),
#                     "KAMA_Cross": int(row['KAMA_Cross'])
#                 },
#                 # 动量指标
#                 "momentum": {
#                     "StochRSI_K": round(row['Stochastic_RSI_FastK'], 2),
#                     "StochRSI_D": round(row['Stochastic_RSI_FastD'], 2),
#                     "MOM": round(row['MOM'], 4)
#                 },
#                 # 成交量指标
#                 "volume": {
#                     "OBV": int(row['OBV']),
#                     "MFI": round(row['MFI'], 2),
#                     "CMF": round(row['CMF'], 2)
#                 }
#             }
#         }
#         result["candles"].append(candle)
#
#
#     result["global_indicators"] = {
#         "total_volume": int(df['volume'].sum()),
#         "price_range": {
#             "max": round(df['high'].max(), 2),
#             "min": round(df['low'].min(), 2)
#         }
#     }
#     return result

# def get_market_kline_by_technical(symbol: str ="BTCUSDT",type :str ="15m", limit=100):
#     return json.dumps(calculate_technical_indicators(sorted(get_market_data_by_symbol(symbol=symbol,type=type,limit=limit), key=lambda x: x.open_time)) ,ensure_ascii=False)

if __name__ == '__main__':
    # k_line = get_market_kline_by_technical(type='1D',limit=50)
    print("")



