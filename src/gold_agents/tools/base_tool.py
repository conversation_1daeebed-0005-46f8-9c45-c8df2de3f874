#!/usr/bin/env python3
"""
优化的工具基类 - 提供统一的错误处理、日志记录和性能监控
"""

import time
import logging
import traceback
from typing import Dict, Any, Optional
from datetime import datetime
from functools import wraps
from crewai_tools import BaseTool
from pydantic import BaseModel


class ToolMetrics:
    """工具性能指标"""
    def __init__(self):
        self.call_count = 0
        self.total_time = 0.0
        self.error_count = 0
        self.last_call_time = None
        self.avg_response_time = 0.0

    def record_call(self, execution_time: float, success: bool = True):
        """记录工具调用"""
        self.call_count += 1
        self.total_time += execution_time
        self.last_call_time = datetime.now()
        self.avg_response_time = self.total_time / self.call_count
        
        if not success:
            self.error_count += 1

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "call_count": self.call_count,
            "error_count": self.error_count,
            "error_rate": self.error_count / max(self.call_count, 1),
            "avg_response_time": self.avg_response_time,
            "total_time": self.total_time,
            "last_call_time": self.last_call_time.isoformat() if self.last_call_time else None
        }


def performance_monitor(func):
    """性能监控装饰器"""
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        start_time = time.time()
        success = True
        result = None
        
        try:
            result = func(self, *args, **kwargs)
            return result
        except Exception as e:
            success = False
            self.logger.error(f"工具执行失败: {str(e)}")
            self.logger.debug(f"错误详情: {traceback.format_exc()}")
            
            # 返回标准错误格式
            return {
                "status": "error",
                "message": str(e),
                "error_type": type(e).__name__,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        finally:
            execution_time = time.time() - start_time
            
            # 记录性能指标
            if hasattr(self, 'metrics'):
                self.metrics.record_call(execution_time, success)
            
            # 记录执行日志
            status = "成功" if success else "失败"
            self.logger.info(f"工具执行{status} - 耗时: {execution_time:.3f}s")
            
    return wrapper


def retry_on_failure(max_retries: int = 3, delay: float = 1.0):
    """失败重试装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(self, *args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    if attempt < max_retries:
                        self.logger.warning(f"第{attempt + 1}次尝试失败: {str(e)}, {delay}秒后重试...")
                        time.sleep(delay * (attempt + 1))  # 指数退避
                    else:
                        self.logger.error(f"所有重试失败，最终错误: {str(e)}")
                        
            raise last_exception
            
        return wrapper
    return decorator


class OptimizedBaseTool(BaseTool):
    """优化的工具基类"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.logger = self._setup_logger()
        self.metrics = ToolMetrics()
        self._config = {}
        self._initialized = False

    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(f"{self.__class__.__name__}")
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
            
        return logger

    def _load_config(self, config_path: str = None) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if config_path:
                import yaml
                with open(config_path, 'r', encoding='utf-8') as f:
                    self._config = yaml.safe_load(f)
                    self.logger.info(f"成功加载配置文件: {config_path}")
            return self._config
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {str(e)}")
            return {}

    def _validate_params(self, params: Dict[str, Any], required_fields: list = None) -> bool:
        """验证参数"""
        try:
            if required_fields:
                missing_fields = [field for field in required_fields if field not in params]
                if missing_fields:
                    raise ValueError(f"缺少必需参数: {missing_fields}")
            
            return True
        except Exception as e:
            self.logger.error(f"参数验证失败: {str(e)}")
            raise

    def _format_response(self, status: str, data: Any = None, message: str = "", 
                        error_type: str = None) -> Dict[str, Any]:
        """格式化响应"""
        response = {
            "status": status,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "tool_name": self.name
        }
        
        if data is not None:
            response["data"] = data
            
        if message:
            response["message"] = message
            
        if error_type:
            response["error_type"] = error_type
            
        return response

    @performance_monitor
    def _run(self, **kwargs: Any) -> Dict[str, Any]:
        """执行工具的主要逻辑 - 子类需要重写此方法"""
        raise NotImplementedError("子类必须实现 _run 方法")

    def get_metrics(self) -> Dict[str, Any]:
        """获取工具性能指标"""
        return {
            "tool_name": self.name,
            "metrics": self.metrics.get_stats()
        }

    def reset_metrics(self):
        """重置性能指标"""
        self.metrics = ToolMetrics()
        self.logger.info("性能指标已重置")

    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 基础健康检查
            health_status = {
                "status": "healthy",
                "tool_name": self.name,
                "initialized": self._initialized,
                "config_loaded": bool(self._config),
                "metrics": self.metrics.get_stats(),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            # 子类可以重写此方法添加特定的健康检查
            return self._custom_health_check(health_status)
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "tool_name": self.name,
                "error": str(e),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

    def _custom_health_check(self, base_status: Dict[str, Any]) -> Dict[str, Any]:
        """自定义健康检查 - 子类可以重写"""
        return base_status

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        if exc_type:
            self.logger.error(f"工具执行异常: {exc_val}")
        return False


class ToolRegistry:
    """工具注册表 - 管理所有工具实例"""
    
    _instance = None
    _tools = {}
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def register_tool(self, tool: OptimizedBaseTool):
        """注册工具"""
        self._tools[tool.name] = tool
    
    def get_tool(self, name: str) -> Optional[OptimizedBaseTool]:
        """获取工具"""
        return self._tools.get(name)
    
    def get_all_tools(self) -> Dict[str, OptimizedBaseTool]:
        """获取所有工具"""
        return self._tools.copy()
    
    def get_tools_metrics(self) -> Dict[str, Any]:
        """获取所有工具的性能指标"""
        return {
            name: tool.get_metrics() 
            for name, tool in self._tools.items()
        }
    
    def health_check_all(self) -> Dict[str, Any]:
        """对所有工具进行健康检查"""
        return {
            name: tool.health_check() 
            for name, tool in self._tools.items()
        }


# 全局工具注册表实例
tool_registry = ToolRegistry()


if __name__ == '__main__':
    # 测试基类功能
    class TestTool(OptimizedBaseTool):
        name = "TestTool"
        description = "测试工具"
        
        def _run(self, **kwargs):
            return self._format_response("success", {"test": "data"}, "测试成功")
    
    # 测试工具
    tool = TestTool()
    result = tool.run(test_param="test_value")
    print("测试结果:", result)
    print("性能指标:", tool.get_metrics())
    print("健康检查:", tool.health_check())
