#!/usr/bin/env python3
"""
智能触发管理器 - 在保持现有防重复机制基础上的增强版本
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from collections import deque, defaultdict
from dataclasses import dataclass
import json


@dataclass
class TriggerRecord:
    """触发记录"""
    timestamp: datetime
    trading_pair: str
    price: float
    signal_strength: float
    trigger_reason: str
    success: Optional[bool] = None  # 触发后的成功状态


class SmartTriggerManager:
    """智能触发管理器 - 增强版防重复推送"""
    
    def __init__(self, base_cooldown_minutes: int = 15):
        """初始化智能触发管理器"""
        self.base_cooldown_minutes = base_cooldown_minutes
        
        # 核心数据结构
        self.last_trigger_times = {}  # 每个交易对的最后触发时间
        self.trigger_history = deque(maxlen=1000)  # 触发历史记录
        self.trigger_counts = defaultdict(int)  # 每个交易对的触发计数
        self.success_rates = defaultdict(list)  # 每个交易对的成功率历史
        
        # 配置参数
        self.config = {
            'min_cooldown_minutes': 5,      # 最小冷却时间
            'max_cooldown_minutes': 60,     # 最大冷却时间
            'adaptive_cooldown': True,      # 是否启用自适应冷却
            'price_similarity_threshold': 0.005,  # 价格相似度阈值（0.5%）
            'success_rate_window': 20,      # 成功率计算窗口
            'high_frequency_threshold': 6,  # 高频触发阈值（每小时）
        }
        
        # 时间窗口限制
        self.time_window_limits = {
            'minute': 1,    # 1分钟内最多1次
            'hour': 4,      # 1小时内最多4次
            'day': 20       # 1天内最多20次
        }

    def should_allow_trigger(self, trading_pair: str, current_price: float, 
                           signal_strength: float, market_volatility: float = 0.02) -> Dict[str, Any]:
        """智能判断是否允许触发"""
        
        # 1. 基础冷却时间检查（保持原有逻辑）
        basic_cooldown_result = self._check_basic_cooldown(trading_pair)
        if not basic_cooldown_result['allow']:
            return basic_cooldown_result
        
        # 2. 时间窗口限制检查
        window_check_result = self._check_time_window_limits(trading_pair)
        if not window_check_result['allow']:
            return window_check_result
        
        # 3. 价格相似性检查
        similarity_check_result = self._check_price_similarity(trading_pair, current_price)
        if not similarity_check_result['allow']:
            return similarity_check_result
        
        # 4. 自适应冷却时间检查
        if self.config['adaptive_cooldown']:
            adaptive_cooldown = self._calculate_adaptive_cooldown(
                trading_pair, signal_strength, market_volatility
            )
            adaptive_check_result = self._check_adaptive_cooldown(trading_pair, adaptive_cooldown)
            if not adaptive_check_result['allow']:
                return adaptive_check_result
        
        # 5. 频率控制检查
        frequency_check_result = self._check_trigger_frequency(trading_pair)
        if not frequency_check_result['allow']:
            return frequency_check_result
        
        # 所有检查通过
        return {
            'allow': True,
            'reason': '所有检查通过',
            'recommended_cooldown': self._get_recommended_cooldown(trading_pair, signal_strength, market_volatility),
            'trigger_count': self.trigger_counts[trading_pair],
            'recent_success_rate': self._get_recent_success_rate(trading_pair)
        }

    def record_trigger(self, trading_pair: str, current_price: float, 
                      signal_strength: float, trigger_reason: str) -> str:
        """记录触发事件"""
        trigger_record = TriggerRecord(
            timestamp=datetime.now(),
            trading_pair=trading_pair,
            price=current_price,
            signal_strength=signal_strength,
            trigger_reason=trigger_reason
        )
        
        # 更新记录
        self.last_trigger_times[trading_pair] = trigger_record.timestamp
        self.trigger_history.append(trigger_record)
        self.trigger_counts[trading_pair] += 1
        
        # 生成触发ID
        trigger_id = f"{trading_pair}_{trigger_record.timestamp.strftime('%Y%m%d_%H%M%S')}"
        
        return trigger_id

    def update_trigger_result(self, trigger_id: str, success: bool):
        """更新触发结果"""
        # 从trigger_id解析信息
        parts = trigger_id.split('_')
        if len(parts) >= 3:
            trading_pair = parts[0]
            timestamp_str = '_'.join(parts[1:])
            
            # 查找对应的触发记录并更新
            for record in reversed(self.trigger_history):
                if (record.trading_pair == trading_pair and 
                    record.timestamp.strftime('%Y%m%d_%H%M%S') == timestamp_str):
                    record.success = success
                    
                    # 更新成功率历史
                    self.success_rates[trading_pair].append(success)
                    if len(self.success_rates[trading_pair]) > self.config['success_rate_window']:
                        self.success_rates[trading_pair].pop(0)
                    break

    def _check_basic_cooldown(self, trading_pair: str) -> Dict[str, Any]:
        """检查基础冷却时间（保持原有逻辑）"""
        last_trigger = self.last_trigger_times.get(trading_pair)
        if last_trigger is None:
            return {'allow': True, 'reason': '首次触发'}
        
        cooldown_period = timedelta(minutes=self.base_cooldown_minutes)
        elapsed = datetime.now() - last_trigger
        
        if elapsed >= cooldown_period:
            return {'allow': True, 'reason': '基础冷却时间已过'}
        else:
            remaining_seconds = int((cooldown_period - elapsed).total_seconds())
            return {
                'allow': False,
                'reason': f'基础冷却时间未到，还需等待{remaining_seconds}秒',
                'remaining_seconds': remaining_seconds,
                'cooldown_type': 'basic'
            }

    def _check_time_window_limits(self, trading_pair: str) -> Dict[str, Any]:
        """检查时间窗口限制"""
        now = datetime.now()
        
        # 检查各个时间窗口
        for window_name, limit in self.time_window_limits.items():
            if window_name == 'minute':
                window_start = now - timedelta(minutes=1)
            elif window_name == 'hour':
                window_start = now - timedelta(hours=1)
            elif window_name == 'day':
                window_start = now - timedelta(days=1)
            else:
                continue
            
            # 计算窗口内的触发次数
            window_triggers = [
                record for record in self.trigger_history
                if (record.trading_pair == trading_pair and 
                    record.timestamp >= window_start)
            ]
            
            if len(window_triggers) >= limit:
                return {
                    'allow': False,
                    'reason': f'{window_name}时间窗口内触发次数已达上限({limit}次)',
                    'window_triggers': len(window_triggers),
                    'window_limit': limit,
                    'cooldown_type': 'time_window'
                }
        
        return {'allow': True, 'reason': '时间窗口检查通过'}

    def _check_price_similarity(self, trading_pair: str, current_price: float) -> Dict[str, Any]:
        """检查价格相似性"""
        # 检查最近30分钟内的触发
        cutoff_time = datetime.now() - timedelta(minutes=30)
        recent_triggers = [
            record for record in self.trigger_history
            if (record.trading_pair == trading_pair and 
                record.timestamp >= cutoff_time)
        ]
        
        for record in recent_triggers:
            price_diff = abs(current_price - record.price) / record.price
            if price_diff < self.config['price_similarity_threshold']:
                return {
                    'allow': False,
                    'reason': f'价格过于相似，差异仅{price_diff:.3%}',
                    'similar_price': record.price,
                    'current_price': current_price,
                    'similarity_threshold': self.config['price_similarity_threshold'],
                    'cooldown_type': 'price_similarity'
                }
        
        return {'allow': True, 'reason': '价格相似性检查通过'}

    def _calculate_adaptive_cooldown(self, trading_pair: str, signal_strength: float, 
                                   market_volatility: float) -> int:
        """计算自适应冷却时间"""
        base_time = self.base_cooldown_minutes
        
        # 信号强度因子
        if signal_strength > 0.9:
            signal_factor = 0.5  # 强信号减少冷却时间
        elif signal_strength > 0.8:
            signal_factor = 0.7
        elif signal_strength > 0.7:
            signal_factor = 1.0
        else:
            signal_factor = 1.5  # 弱信号增加冷却时间
        
        # 波动率因子
        if market_volatility > 0.05:
            volatility_factor = 0.8  # 高波动减少冷却时间
        elif market_volatility > 0.02:
            volatility_factor = 1.0
        else:
            volatility_factor = 1.2  # 低波动增加冷却时间
        
        # 成功率因子
        success_rate = self._get_recent_success_rate(trading_pair)
        if success_rate > 0.7:
            success_factor = 0.8  # 高成功率减少冷却时间
        elif success_rate > 0.5:
            success_factor = 1.0
        else:
            success_factor = 1.3  # 低成功率增加冷却时间
        
        # 频率因子
        trigger_count = self.trigger_counts[trading_pair]
        if trigger_count > 10:
            frequency_factor = 1.2  # 频繁触发增加冷却时间
        else:
            frequency_factor = 1.0
        
        # 计算最终冷却时间
        adaptive_cooldown = int(base_time * signal_factor * volatility_factor * success_factor * frequency_factor)
        
        # 限制在合理范围内
        return max(self.config['min_cooldown_minutes'], 
                  min(self.config['max_cooldown_minutes'], adaptive_cooldown))

    def _check_adaptive_cooldown(self, trading_pair: str, adaptive_cooldown: int) -> Dict[str, Any]:
        """检查自适应冷却时间"""
        last_trigger = self.last_trigger_times.get(trading_pair)
        if last_trigger is None:
            return {'allow': True, 'reason': '首次触发'}
        
        cooldown_period = timedelta(minutes=adaptive_cooldown)
        elapsed = datetime.now() - last_trigger
        
        if elapsed >= cooldown_period:
            return {'allow': True, 'reason': f'自适应冷却时间({adaptive_cooldown}分钟)已过'}
        else:
            remaining_seconds = int((cooldown_period - elapsed).total_seconds())
            return {
                'allow': False,
                'reason': f'自适应冷却时间未到，还需等待{remaining_seconds}秒',
                'remaining_seconds': remaining_seconds,
                'adaptive_cooldown_minutes': adaptive_cooldown,
                'cooldown_type': 'adaptive'
            }

    def _check_trigger_frequency(self, trading_pair: str) -> Dict[str, Any]:
        """检查触发频率"""
        # 检查最近1小时的触发频率
        hour_ago = datetime.now() - timedelta(hours=1)
        recent_triggers = [
            record for record in self.trigger_history
            if (record.trading_pair == trading_pair and 
                record.timestamp >= hour_ago)
        ]
        
        if len(recent_triggers) >= self.config['high_frequency_threshold']:
            return {
                'allow': False,
                'reason': f'触发频率过高，1小时内已触发{len(recent_triggers)}次',
                'recent_trigger_count': len(recent_triggers),
                'frequency_threshold': self.config['high_frequency_threshold'],
                'cooldown_type': 'high_frequency'
            }
        
        return {'allow': True, 'reason': '触发频率检查通过'}

    def _get_recent_success_rate(self, trading_pair: str) -> float:
        """获取最近的成功率"""
        success_history = self.success_rates.get(trading_pair, [])
        if not success_history:
            return 0.5  # 默认成功率
        
        return sum(success_history) / len(success_history)

    def _get_recommended_cooldown(self, trading_pair: str, signal_strength: float, 
                                 market_volatility: float) -> int:
        """获取推荐的冷却时间"""
        if self.config['adaptive_cooldown']:
            return self._calculate_adaptive_cooldown(trading_pair, signal_strength, market_volatility)
        else:
            return self.base_cooldown_minutes

    def get_trigger_statistics(self, trading_pair: str = None) -> Dict[str, Any]:
        """获取触发统计信息"""
        if trading_pair:
            # 特定交易对的统计
            pair_triggers = [r for r in self.trigger_history if r.trading_pair == trading_pair]
            return {
                'trading_pair': trading_pair,
                'total_triggers': len(pair_triggers),
                'trigger_count': self.trigger_counts[trading_pair],
                'recent_success_rate': self._get_recent_success_rate(trading_pair),
                'last_trigger_time': self.last_trigger_times.get(trading_pair),
                'cooldown_remaining': self._get_cooldown_remaining(trading_pair)
            }
        else:
            # 全局统计
            return {
                'total_triggers': len(self.trigger_history),
                'active_pairs': len(self.last_trigger_times),
                'average_success_rate': self._calculate_average_success_rate(),
                'trigger_counts_by_pair': dict(self.trigger_counts),
                'config': self.config
            }

    def _get_cooldown_remaining(self, trading_pair: str) -> Optional[int]:
        """获取剩余冷却时间（秒）"""
        last_trigger = self.last_trigger_times.get(trading_pair)
        if last_trigger is None:
            return None
        
        cooldown_period = timedelta(minutes=self.base_cooldown_minutes)
        elapsed = datetime.now() - last_trigger
        remaining = cooldown_period - elapsed
        
        return max(0, int(remaining.total_seconds())) if remaining.total_seconds() > 0 else 0

    def _calculate_average_success_rate(self) -> float:
        """计算平均成功率"""
        all_success_rates = []
        for pair_rates in self.success_rates.values():
            if pair_rates:
                all_success_rates.extend(pair_rates)
        
        if not all_success_rates:
            return 0.0
        
        return sum(all_success_rates) / len(all_success_rates)

    def reset_pair_statistics(self, trading_pair: str):
        """重置特定交易对的统计信息"""
        if trading_pair in self.last_trigger_times:
            del self.last_trigger_times[trading_pair]
        
        self.trigger_counts[trading_pair] = 0
        self.success_rates[trading_pair] = []
        
        # 从历史记录中移除该交易对的记录
        self.trigger_history = deque([
            record for record in self.trigger_history 
            if record.trading_pair != trading_pair
        ], maxlen=1000)

    def update_config(self, new_config: Dict[str, Any]):
        """更新配置"""
        self.config.update(new_config)


if __name__ == '__main__':
    # 测试智能触发管理器
    manager = SmartTriggerManager()
    
    # 测试触发判断
    result = manager.should_allow_trigger("BTC", 50000.0, 0.8, 0.03)
    print("触发判断结果:", result)
    
    # 记录触发
    if result['allow']:
        trigger_id = manager.record_trigger("BTC", 50000.0, 0.8, "测试触发")
        print("触发记录ID:", trigger_id)
        
        # 更新触发结果
        manager.update_trigger_result(trigger_id, True)
    
    # 获取统计信息
    stats = manager.get_trigger_statistics("BTC")
    print("统计信息:", stats)
