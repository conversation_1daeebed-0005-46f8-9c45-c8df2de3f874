# src/gold_agents/tools/jin10_news_tool.py
import json

import akshare as ak
from datetime import datetime
from crewai.tools import BaseTool
from pydantic import BaseModel, Field
from typing import Any, Optional, Type
from .news_database_manager import get_latest_news
from .hot_news_database_manager import get_latest_hot_news

class Jin10NewsToolSchema(BaseModel):
    """Input for PDFReadTool."""

    # file_path: str = Field(..., description="Mandatory file full path to read the file")


class Jin10NewsTool(BaseTool):
    name: str = "获取新闻数据工具"
    description: str = "可以获取金十数据网站最新的金融新闻数据"
    args_schema: Type[BaseModel] = Jin10NewsToolSchema
    # file_path: Optional[str] = None

    def __init__(self, **kwargs: Any) -> None:
        super().__init__(**kwargs)

    def _run(
            self,
            **kwargs: Any,
    ) -> str:
        return {"7*24h快讯":get_latest_news() ,"今日大事件":get_latest_hot_news()}




class Jin10NewsSpeculativeSentimentReportToolSchema(BaseModel):
    """Input for PDFReadTool."""



class Jin10NewsSpeculativeSentimentReportTool(BaseTool):
    name: str = "金十数据-比特币持仓报告"
    description: str = "金十数据-比特币持仓报告"
    args_schema: Type[BaseModel] = Jin10NewsToolSchema


    def __init__(self, **kwargs: Any) -> None:
        super().__init__(**kwargs)

    def _run(
            self,
            **kwargs: Any,
    ) -> str:
        cloumn="代码、公司名称-英文、国家/地区、市值、比特币占市值比重、持仓成本、持仓占比、持仓量、当日持仓市值、查询日期、公告链接、_、分类、倍数、_、公司名称-中文"
        data =ak.crypto_bitcoin_hold_report()
        return {"cloumn":cloumn,"data":data}



# 可以在这里添加简单测试
if __name__ == '__main__':
    # print(get_latest_market_news())
    # validate_tool = Jin10NewsTool()
    # print(validate_tool.run())
    jin10_news_tool = Jin10NewsSpeculativeSentimentReportTool()
    result = jin10_news_tool.run()
    print("金十数据-比特币持仓报告结果:")
    print(f"字段说明: {result['cloumn']}")
    print("持仓数据详情:")
    for index, row in result['data'].iterrows():
        print(f"公司名称: {row['公司名称-英文']} ({row['公司名称-中文']}), "
              f"国家/地区: {row['国家/地区']}, "
              f"市值: {row['市值']}, "
              f"比特币占市值比重: {row['比特币占市值比重']}, "
              f"持仓成本: {row['持仓成本']}, "
              f"持仓占比: {row['持仓占比']}, "
              f"持仓量: {row['持仓量']}, "
              f"当日持仓市值: {row['当日持仓市值']}, "
              f"查询日期: {row['查询日期']}")
    # 获取实时行情数据
    # stock_realtime = ak.spot_symbol_table_sge()
    # stock_realtime = ak.spot_quotations_sge(symbol="Au99.99")
    # stock_realtime = ak.futures_contract_detail(symbol="IM2402")
    # print(stock_realtime)
    # futures_foreign_hist_df = ak.futures_foreign_hist(symbol="XAUUSD")
    # print(futures_foreign_hist_df)

    # subscribes = ak.futures_foreign_commodity_subscribe_exchange_symbol()
    #
    # for item in subscribes:
    #     futures_foreign_detail_df = ak.futures_foreign_detail(symbol=item)
    #     print(item)
    # futures_foreign_commodity_realtime_df = ak.futures_foreign_commodity_realtime(
    #     symbol=["XAU"]
    # )
    # print(futures_foreign_commodity_realtime_df)
    # print( ak.futures_global_spot_em())
    # df = ak.forex_spot_em(symbol="XAUUSD", start_date="2023-01-01", end_date="2023-12-31")
    # forex_spot_em_df = ak.forex_spot_em()

    # forex_hist_em_df = ak.forex_hist_em(symbol="XAUUSD")
    # print(forex_hist_em_df)
    # forex_spot_em_df =ak.macro_fx_sentiment("20250424","20250424")
    # print(forex_spot_em_df.head())
    # 金十数据-其他-加密货币实时行情
    # print("-----------金十数据-其他-加密货币实时行情------------")
    # crypto_js_spot_df = ak.crypto_js_spot()
    # for index, row in crypto_js_spot_df.iterrows():
    #     print(row["市场"], row["交易品种"], row["最近报价"], row["涨跌额"], row["涨跌幅"], row["24小时最高"], row["24小时最低"], row["24小时成交量"], row["更新时间"])
    # test_date = datetime.now().date().isoformat().replace("-", "")
    # start_date = "-".join([test_date[:4], test_date[4:6], test_date[6:]])
    # end_date = "-".join([test_date[:4], test_date[4:6], test_date[6:]])
    # print(start_date)
    # print(end_date)
    # #  金十数据-外汇-投机情绪报告
    # macro_fx_sentiment_df = ak.macro_fx_sentiment(start_date=test_date, end_date=test_date)
    # for index, row in macro_fx_sentiment_df.iterrows():
    #     print(row['date'],row['XAUUSD'] )

    # 金十数据中心-经济指标-央行利率-主要央行利率-美联储利率决议报告
    # macro_bank_usa_interest_rate_df = ak.macro_bank_usa_interest_rate()
    # print(macro_bank_usa_interest_rate_df)
    # 华尔街见闻-日历-宏观
    # print("-----------华尔街见闻-日历-宏观------------")
    # macro_info_ws_df =  ak.macro_info_ws(date="********")
    # for index, row in macro_info_ws_df.iterrows():
    #     print(row["时间"], row["地区"], row["事件"], row["重要性"], row["今值"], row["预期"], row["前值"], row["链接"])

    # cpio  = ak.macro_usa_cpi_yoy()
    # print(cpio)
