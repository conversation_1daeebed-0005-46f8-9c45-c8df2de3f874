#!/usr/bin/env python3
"""
交易复盘工具 - 用于分析历史交易记录，生成复盘报告和改进建议
"""

import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field
from crewai_tools import BaseTool
import pandas as pd
import numpy as np


class TradingReviewToolSchema(BaseModel):
    """交易复盘工具的输入参数模式"""
    action: str = Field(..., description="操作类型：daily_review, weekly_review, strategy_analysis, performance_analysis")
    start_date: Optional[str] = Field(None, description="开始日期 YYYY-MM-DD")
    end_date: Optional[str] = Field(None, description="结束日期 YYYY-MM-DD")
    trading_pair: str = Field(default="BTC", description="交易对")
    strategy_id: Optional[int] = Field(None, description="特定策略ID")


class TradingReviewTool(BaseTool):
    """交易复盘工具类"""
    name: str = "TradingReviewTool"
    description: str = """
    这是一个用于交易复盘分析的工具。
    主要功能：
    1. 分析历史交易记录和Agent决策过程
    2. 生成详细的复盘报告
    3. 识别交易模式和改进机会
    4. 提供策略优化建议
    
    使用示例：
    1. 日度复盘：
       tool.run(action="daily_review", start_date="2024-01-01")
    
    2. 策略分析：
       tool.run(action="strategy_analysis", strategy_id=123)
    
    3. 绩效分析：
       tool.run(action="performance_analysis", start_date="2024-01-01", end_date="2024-01-31")
    """
    args_schema: type[BaseModel] = TradingReviewToolSchema

    def __init__(self, db_path: str = "trading_review.db", **kwargs):
        """初始化复盘工具"""
        super().__init__(**kwargs)
        self.db_path = db_path
        self._init_database()

    def _init_database(self):
        """初始化复盘数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建Agent决策记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS agent_decisions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT NOT NULL,
                        agent_name TEXT NOT NULL,
                        task_name TEXT NOT NULL,
                        input_data TEXT NOT NULL,  -- JSON格式的输入数据
                        output_data TEXT NOT NULL, -- JSON格式的输出数据
                        execution_time REAL NOT NULL, -- 执行时间（秒）
                        timestamp DATETIME NOT NULL,
                        success BOOLEAN NOT NULL,
                        error_message TEXT
                    )
                ''')
                
                # 创建交易记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS trading_records (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT NOT NULL,
                        strategy_id INTEGER,
                        trading_pair TEXT NOT NULL,
                        action TEXT NOT NULL, -- open_long, open_short, close_long, close_short
                        order_type TEXT NOT NULL, -- market, limit
                        entry_price REAL,
                        exit_price REAL,
                        size REAL NOT NULL,
                        leverage INTEGER,
                        stop_loss REAL,
                        take_profit REAL,
                        pnl REAL, -- 实际盈亏
                        commission REAL, -- 手续费
                        duration INTEGER, -- 持仓时长（分钟）
                        entry_time DATETIME NOT NULL,
                        exit_time DATETIME,
                        status TEXT NOT NULL, -- open, closed, cancelled
                        notes TEXT
                    )
                ''')
                
                # 创建复盘报告表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS review_reports (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        report_type TEXT NOT NULL, -- daily, weekly, monthly, strategy
                        period_start DATETIME NOT NULL,
                        period_end DATETIME NOT NULL,
                        trading_pair TEXT,
                        total_trades INTEGER,
                        winning_trades INTEGER,
                        losing_trades INTEGER,
                        win_rate REAL,
                        total_pnl REAL,
                        max_profit REAL,
                        max_loss REAL,
                        avg_profit REAL,
                        avg_loss REAL,
                        profit_factor REAL,
                        sharpe_ratio REAL,
                        max_drawdown REAL,
                        key_insights TEXT, -- JSON格式的关键洞察
                        improvement_suggestions TEXT, -- JSON格式的改进建议
                        created_at DATETIME NOT NULL
                    )
                ''')
                
                conn.commit()
                
        except Exception as e:
            print(f"初始化复盘数据库失败: {str(e)}")
            raise

    def _run(self, **kwargs: Any) -> Dict[str, Any]:
        """执行复盘工具的主要逻辑"""
        try:
            action = kwargs.get('action', 'daily_review')
            
            if action == 'daily_review':
                return self._daily_review(kwargs)
            elif action == 'weekly_review':
                return self._weekly_review(kwargs)
            elif action == 'strategy_analysis':
                return self._strategy_analysis(kwargs)
            elif action == 'performance_analysis':
                return self._performance_analysis(kwargs)
            else:
                return {
                    "status": "error",
                    "message": f"不支持的操作类型: {action}",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
        except Exception as e:
            return {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

    def save_agent_decision(self, session_id: str, agent_name: str, task_name: str, 
                           input_data: dict, output_data: dict, execution_time: float, 
                           success: bool = True, error_message: str = None):
        """保存Agent决策记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO agent_decisions 
                    (session_id, agent_name, task_name, input_data, output_data, 
                     execution_time, timestamp, success, error_message)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    session_id, agent_name, task_name,
                    json.dumps(input_data, ensure_ascii=False),
                    json.dumps(output_data, ensure_ascii=False),
                    execution_time, datetime.now(), success, error_message
                ))
                conn.commit()
        except Exception as e:
            print(f"保存Agent决策记录失败: {str(e)}")

    def save_trading_record(self, session_id: str, **trade_data):
        """保存交易记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO trading_records 
                    (session_id, strategy_id, trading_pair, action, order_type, 
                     entry_price, exit_price, size, leverage, stop_loss, take_profit,
                     pnl, commission, duration, entry_time, exit_time, status, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    session_id,
                    trade_data.get('strategy_id'),
                    trade_data.get('trading_pair', 'BTC'),
                    trade_data.get('action'),
                    trade_data.get('order_type'),
                    trade_data.get('entry_price'),
                    trade_data.get('exit_price'),
                    trade_data.get('size'),
                    trade_data.get('leverage'),
                    trade_data.get('stop_loss'),
                    trade_data.get('take_profit'),
                    trade_data.get('pnl'),
                    trade_data.get('commission'),
                    trade_data.get('duration'),
                    trade_data.get('entry_time', datetime.now()),
                    trade_data.get('exit_time'),
                    trade_data.get('status', 'open'),
                    trade_data.get('notes')
                ))
                conn.commit()
        except Exception as e:
            print(f"保存交易记录失败: {str(e)}")

    def _daily_review(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """日度复盘分析"""
        try:
            start_date = params.get('start_date', datetime.now().strftime('%Y-%m-%d'))
            trading_pair = params.get('trading_pair', 'BTC')
            
            # 获取当日交易记录
            trades = self._get_trades_by_date(start_date, start_date, trading_pair)
            
            # 获取当日Agent决策记录
            decisions = self._get_agent_decisions_by_date(start_date, start_date)
            
            # 计算交易统计
            stats = self._calculate_trading_stats(trades)
            
            # 分析Agent表现
            agent_performance = self._analyze_agent_performance(decisions)
            
            # 生成关键洞察
            insights = self._generate_daily_insights(trades, decisions, stats)
            
            # 生成改进建议
            suggestions = self._generate_improvement_suggestions(stats, agent_performance)
            
            report = {
                "report_type": "daily_review",
                "date": start_date,
                "trading_pair": trading_pair,
                "trading_stats": stats,
                "agent_performance": agent_performance,
                "key_insights": insights,
                "improvement_suggestions": suggestions,
                "raw_data": {
                    "trades_count": len(trades),
                    "decisions_count": len(decisions)
                }
            }
            
            # 保存复盘报告
            self._save_review_report(report)
            
            return {
                "status": "success",
                "report": report,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            raise Exception(f"日度复盘失败: {str(e)}")

    def _calculate_trading_stats(self, trades: List[Dict]) -> Dict[str, Any]:
        """计算交易统计数据"""
        if not trades:
            return {
                "total_trades": 0,
                "winning_trades": 0,
                "losing_trades": 0,
                "win_rate": 0.0,
                "total_pnl": 0.0,
                "avg_profit": 0.0,
                "avg_loss": 0.0,
                "max_profit": 0.0,
                "max_loss": 0.0,
                "profit_factor": 0.0
            }
        
        df = pd.DataFrame(trades)
        
        # 基础统计
        total_trades = len(df)
        winning_trades = len(df[df['pnl'] > 0]) if 'pnl' in df.columns else 0
        losing_trades = len(df[df['pnl'] < 0]) if 'pnl' in df.columns else 0
        
        # 盈亏统计
        total_pnl = df['pnl'].sum() if 'pnl' in df.columns else 0.0
        profits = df[df['pnl'] > 0]['pnl'] if 'pnl' in df.columns else pd.Series([])
        losses = df[df['pnl'] < 0]['pnl'] if 'pnl' in df.columns else pd.Series([])
        
        return {
            "total_trades": total_trades,
            "winning_trades": winning_trades,
            "losing_trades": losing_trades,
            "win_rate": winning_trades / total_trades if total_trades > 0 else 0.0,
            "total_pnl": float(total_pnl),
            "avg_profit": float(profits.mean()) if len(profits) > 0 else 0.0,
            "avg_loss": float(losses.mean()) if len(losses) > 0 else 0.0,
            "max_profit": float(profits.max()) if len(profits) > 0 else 0.0,
            "max_loss": float(losses.min()) if len(losses) > 0 else 0.0,
            "profit_factor": float(profits.sum() / abs(losses.sum())) if len(losses) > 0 and losses.sum() != 0 else 0.0
        }

    def _analyze_agent_performance(self, decisions: List[Dict]) -> Dict[str, Any]:
        """分析Agent表现"""
        if not decisions:
            return {}
        
        df = pd.DataFrame(decisions)
        
        performance = {}
        for agent in df['agent_name'].unique():
            agent_data = df[df['agent_name'] == agent]
            
            performance[agent] = {
                "total_decisions": len(agent_data),
                "success_rate": len(agent_data[agent_data['success'] == True]) / len(agent_data),
                "avg_execution_time": float(agent_data['execution_time'].mean()),
                "error_count": len(agent_data[agent_data['success'] == False])
            }
        
        return performance

    def _generate_daily_insights(self, trades: List[Dict], decisions: List[Dict], stats: Dict) -> List[str]:
        """生成日度关键洞察"""
        insights = []
        
        # 交易表现洞察
        if stats['total_trades'] > 0:
            if stats['win_rate'] > 0.6:
                insights.append(f"今日胜率{stats['win_rate']:.1%}，表现优秀")
            elif stats['win_rate'] < 0.4:
                insights.append(f"今日胜率{stats['win_rate']:.1%}，需要改进策略")
            
            if stats['total_pnl'] > 0:
                insights.append(f"今日盈利{stats['total_pnl']:.2f}，策略有效")
            else:
                insights.append(f"今日亏损{abs(stats['total_pnl']):.2f}，需要风险控制")
        
        # Agent协作洞察
        if decisions:
            agent_count = len(set(d['agent_name'] for d in decisions))
            insights.append(f"今日{agent_count}个Agent参与决策，团队协作良好")
        
        return insights

    def _generate_improvement_suggestions(self, stats: Dict, agent_performance: Dict) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        # 基于交易统计的建议
        if stats['win_rate'] < 0.5:
            suggestions.append("建议优化入场策略，提高胜率")
        
        if stats['profit_factor'] < 1.5:
            suggestions.append("建议改进止盈止损设置，提高盈亏比")
        
        # 基于Agent表现的建议
        for agent, perf in agent_performance.items():
            if perf['success_rate'] < 0.9:
                suggestions.append(f"建议优化{agent}的决策逻辑，提高成功率")
            
            if perf['avg_execution_time'] > 5.0:
                suggestions.append(f"建议优化{agent}的执行效率，减少响应时间")
        
        return suggestions

    def _get_trades_by_date(self, start_date: str, end_date: str, trading_pair: str) -> List[Dict]:
        """根据日期获取交易记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM trading_records 
                    WHERE DATE(entry_time) BETWEEN ? AND ? 
                    AND trading_pair = ?
                    ORDER BY entry_time DESC
                ''', (start_date, end_date, trading_pair))
                
                columns = [description[0] for description in cursor.description]
                return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except Exception as e:
            print(f"获取交易记录失败: {str(e)}")
            return []

    def _get_agent_decisions_by_date(self, start_date: str, end_date: str) -> List[Dict]:
        """根据日期获取Agent决策记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM agent_decisions 
                    WHERE DATE(timestamp) BETWEEN ? AND ?
                    ORDER BY timestamp DESC
                ''', (start_date, end_date))
                
                columns = [description[0] for description in cursor.description]
                return [dict(zip(columns, row)) for row in cursor.fetchall()]
        except Exception as e:
            print(f"获取Agent决策记录失败: {str(e)}")
            return []

    def _save_review_report(self, report: Dict[str, Any]):
        """保存复盘报告"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                stats = report.get('trading_stats', {})
                cursor.execute('''
                    INSERT INTO review_reports 
                    (report_type, period_start, period_end, trading_pair, total_trades,
                     winning_trades, losing_trades, win_rate, total_pnl, max_profit,
                     max_loss, avg_profit, avg_loss, profit_factor, key_insights,
                     improvement_suggestions, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    report['report_type'],
                    report.get('date', datetime.now().strftime('%Y-%m-%d')),
                    report.get('date', datetime.now().strftime('%Y-%m-%d')),
                    report.get('trading_pair'),
                    stats.get('total_trades', 0),
                    stats.get('winning_trades', 0),
                    stats.get('losing_trades', 0),
                    stats.get('win_rate', 0.0),
                    stats.get('total_pnl', 0.0),
                    stats.get('max_profit', 0.0),
                    stats.get('max_loss', 0.0),
                    stats.get('avg_profit', 0.0),
                    stats.get('avg_loss', 0.0),
                    stats.get('profit_factor', 0.0),
                    json.dumps(report.get('key_insights', []), ensure_ascii=False),
                    json.dumps(report.get('improvement_suggestions', []), ensure_ascii=False),
                    datetime.now()
                ))
                conn.commit()
        except Exception as e:
            print(f"保存复盘报告失败: {str(e)}")

    def _weekly_review(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """周度复盘分析"""
        # 实现周度复盘逻辑
        pass

    def _strategy_analysis(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """策略分析"""
        # 实现策略分析逻辑
        pass

    def _performance_analysis(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """绩效分析"""
        # 实现绩效分析逻辑
        pass


if __name__ == '__main__':
    # 测试复盘工具
    tool = TradingReviewTool()
    
    # 模拟保存一些测试数据
    tool.save_agent_decision(
        session_id="test_session_001",
        agent_name="trader",
        task_name="execute_trade",
        input_data={"trading_pair": "BTC", "action": "open_long"},
        output_data={"status": "success", "order_id": "12345"},
        execution_time=1.5
    )
    
    # 测试日度复盘
    result = tool.run(action="daily_review", start_date="2024-01-01", trading_pair="BTC")
    print("复盘结果:", json.dumps(result, indent=2, ensure_ascii=False))
