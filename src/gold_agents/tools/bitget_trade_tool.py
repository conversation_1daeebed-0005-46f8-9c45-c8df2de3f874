from typing import Dict, Any, Type, Optional
from datetime import datetime
from pybitget import Client
from crewai.tools import BaseTool
from pydantic import BaseModel, Field, PrivateAttr
import yaml
import os
from src.gold_agents.database.db_manager import DatabaseManager

class BitgetTradeToolSchema(BaseModel):
    """Bitget交易工具的输入参数模式"""
    trading_pair: str = Field(..., description="交易对，可选值：BTC, ETH")
    strategy_id: Optional[int] = Field(None, description="策略ID，可选")
    action: str = Field(..., description="交易动作，可选值：open_long, open_short")
    order_type: str = Field(..., description="订单类型，可选值：market, limit")
    price: Optional[float] = Field(None, description="价格，仅限价单需要")
    size: float = Field(..., description="交易数量 每次开单0.001")
    stop_loss: Optional[float] = Field(None, description="止损价格")
    take_profit: Optional[float] = Field(None, description="止盈价格")

class BitgetTradeTool(BaseTool):
    """Bitget交易执行工具类"""
    name: str = "BitgetTradeTool"
    description: str = """
    这是一个用于在Bitget平台执行合约交易的工具。
    主要功能：
    1. 执行开仓操作（做多/做空）
    2. 设置止损止盈
    3. 记录交易信息
    
    使用示例：
    tool.run(
        trading_pair="BTC",
        strategy_id=1,
        action="open_long",
        order_type="limit",
        price=50000,
        size=0.1,
        stop_loss=49000,
        take_profit=51000
    )
    """
    args_schema: Type[BaseModel] = BitgetTradeToolSchema

    # 使用私有属性存储内部状态
    _config: Dict[str, Any] = PrivateAttr(default_factory=dict)
    _environment: str = PrivateAttr(default="test")
    _is_test: bool = PrivateAttr(default=True)
    _margin_coin: str = PrivateAttr(default="SUSDT")
    _client: Client = PrivateAttr(default=None)
    _db: DatabaseManager = PrivateAttr(default=None)
    _trading_pair: str = PrivateAttr(default="")
    _symbol: str = PrivateAttr(default="")
    _price_scale: int = PrivateAttr(default=1)
    _leverage: int = PrivateAttr(default=125)
    _volume_scale: int = PrivateAttr(default=3)
    _strategy_id: Optional[int] = PrivateAttr(default=None)
    _product_type: str = PrivateAttr(default="SUMCBL")
    def __init__(self, **kwargs: Any) -> None:
        """初始化工具"""
        super().__init__(**kwargs)
        self._load_config()
        self._init_environment()
        self._init_client()
        self._init_database()

    def _load_config(self) -> None:
        """加载配置文件"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), '../config/trader_config.yaml')
            with open(config_path, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f)
        except Exception as e:
            raise Exception(f"加载配置文件失败: {str(e)}")

    def _init_environment(self) -> None:
        """初始化环境配置"""
        self._environment = self._config['environment']
        if self._environment not in ['test', 'prod']:
            raise ValueError("环境配置错误，必须是 'test' 或 'prod'")
        self._is_test = self._environment == 'test'
        self._margin_coin = "SUSDT" if self._is_test else "USDT"
        self._product_type = "SUMCBL" if self._is_test else "UMCBL"

    def _init_client(self) -> None:
        """初始化API客户端"""
        api_config = self._config['api']
        if not all([api_config['api_key'], api_config['api_secret'], api_config['passphrase']]):
            raise ValueError("API配置不完整，请检查配置文件")
        
        try:
            self._client = Client(
                api_key=api_config['api_key'],
                api_secret_key=api_config['api_secret'],
                passphrase=api_config['passphrase']
            )
        except Exception as e:
            raise Exception(f"初始化API客户端失败: {str(e)}")

    def _init_database(self) -> None:
        """初始化数据库管理器"""
        self._db = DatabaseManager()

    def _init_trading_params(self, trading_pair: str, strategy_id: Optional[int] = None) -> None:
        """初始化交易参数"""
        self._trading_pair = trading_pair
        self._symbol = f"S{trading_pair}SUSDT_SUMCBL" if self._is_test else f"{trading_pair}USDT_UMCBL"
        self._strategy_id = strategy_id
        
        # 从配置中获取交易参数
        trading_config = self._config.get('trading', {})
        self._price_scale = trading_config.get('price_scale', 1)
        self._leverage = trading_config.get('leverage', 125)
        self._volume_scale = trading_config.get('volume_scale', 3)

    def _format_price(self, price: float) -> str:
        """格式化价格"""
        return f"{price:.{self._price_scale}f}"

    def _format_volume(self, volume: float) -> str:
        """格式化数量"""
        return f"{volume:.{self._volume_scale}f}"

    def _run(self, **kwargs: Any) -> Dict[str, Any]:
        """执行工具的主要逻辑"""
        try:
            # 初始化交易参数
            self._init_trading_params(
                kwargs.get('trading_pair', 'BTC'),
                kwargs.get('strategy_id')
            )
            
            # 执行交易
            result = self._execute_trade(kwargs)
            
            return {
                "status": "success",
                "message": f"成功执行{kwargs['action']}交易",
                "order_id": result.get('data', {}).get('orderId'),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        except Exception as e:
            return {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

    def _check_account_balance(self) -> None:
        """检查账户余额和保证金"""
        try:
            result = self._client.mix_get_accounts(
                productType=self._product_type
            )

            print("===============账户信息==================")
            print(f"接口响应：{result}")
            if not result or 'data' not in result:
                raise Exception("获取账户信息失败")
            
            account_info = result['data'][0]
            print(account_info)
            available_margin = float(account_info.get('available', '0'))
            print(f"可用保证金: {available_margin} {self._margin_coin}")
            
            # 计算保证金率（使用权益和未实现盈亏计算）
            equity = float(account_info.get('equity', '0'))
            unrealized_pl = float(account_info.get('unrealizedPL', '0'))
            
            if equity > 0:
                margin_ratio = ((equity - unrealized_pl) / equity) * 100
            else:
                margin_ratio = 0.0
                
            print(f"保证金率: {margin_ratio:.2f}%")            
            print("===============账户信息==================")
            print(f"可用保证金: {available_margin} {self._margin_coin}")
            print(f"保证金率: {margin_ratio}%")
            
            if available_margin <= 0:
                raise Exception("可用保证金不足")
            if margin_ratio > 90:  # 保证金率超过90%时发出警告
                print("警告：保证金率过高，请注意风险")
                
        except Exception as e:
            raise Exception(f"检查账户余额失败: {str(e)}")

    def _check_position_risk(self, symbol: str, size: float, price: float) -> None:
        """检查持仓风险"""
        try:
            # 获取持仓信息
            result = self._client.mix_get_all_positions(
                productType=self._product_type,
                marginCoin=self._margin_coin
            )
            
            print("===============持仓风险检查==================")
            print(f"交易对: {symbol}")
            print(f"产品类型: {self._product_type}")
            print(f"保证金币种: {self._margin_coin}")
            print(f"接口响应: {result}")
            
            current_size = 0.0
            if result and 'data' in result and result['data']:
                # 找到对应交易对的持仓
                for position in result['data']:
                    if position.get('symbol', '').startswith(symbol):
                        current_size = float(position.get('total', 0))
                        break
            
            # 计算新增持仓后的总风险
            total_size = current_size + size
            total_value = total_size * price
            
            print(f"当前持仓: {current_size}")
            print(f"新增持仓: {size}")
            print(f"总持仓: {total_size}")
            print(f"持仓价值: {total_value} {self._margin_coin}")
            
            # 检查是否超过风险限制
            if total_value > 100000:  # 假设风险限制为10万USDT
                raise Exception("超过风险限制")
                
        except Exception as e:
            raise Exception(f"检查持仓风险失败: {str(e)}")

    def _check_order_params(self, params: Dict[str, Any]) -> None:
        """检查订单参数"""
        try:
            # 检查价格
            if params.get('order_type') == 'limit':
                if not params.get('price') or params['price'] <= 0:
                    raise ValueError("限价单价格无效")
                
                # 获取当前市场价格
                ticker = self._client.mix_get_single_symbol_ticker(symbol=self._symbol)
                if ticker and 'data' in ticker:
                    last_price = float(ticker['data'].get('last', 0))
                    price_diff = abs(params['price'] - last_price) / last_price
                    
                    if price_diff > 0.1:  # 价格偏离超过10%
                        raise ValueError("价格偏离过大")
            
            # 检查数量
            if not params.get('size') or params['size'] <= 0:
                raise ValueError("交易数量无效")
            
            # 检查止盈止损
            if params.get('stop_loss') and params.get('take_profit'):
                if params['stop_loss'] >= params['take_profit']:
                    raise ValueError("止损价格必须小于止盈价格")
                
        except Exception as e:
            raise Exception(f"检查订单参数失败: {str(e)}")



    def _execute_trade(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行交易"""
        try:
            # 交易前检查
            self._check_account_balance()
            self._check_position_risk(self._trading_pair, params['size'], params.get('price', 0))
            self._check_order_params(params)
            
            if params['action'] not in ['open_long', 'open_short']:
                raise ValueError(f"无效的交易动作: {params['action']}")

            # 执行主订单
            print("===============下单成功==================")
            result = self._execute_main_order(self._symbol, params)
            print(f"下单结果: {result}")
            # 检查下单结果
            if result and result.get('code') == '00000' and result.get('msg') == 'success':
                print(f"订单ID: {result.get('data', {}).get('orderId')}")
                print(f"客户端订单ID: {result.get('data', {}).get('clientOid')}")
                return {
                    "status": "success",
                    "message": f"成功执行{params['action']}交易",
                    "order_id": result.get('data', {}).get('orderId'),
                    "client_order_id": result.get('data', {}).get('clientOid'),
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            else:
                error_msg = result.get('msg', '未知错误')
                raise Exception(f"下单失败: {error_msg}")
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"执行交易失败: {str(e)}",
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

    def _execute_main_order(self, symbol: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """执行主订单"""
        try:
            # 构建订单参数
            order_params = {
                "symbol": symbol,
                "marginCoin": self._margin_coin,
                "size": str(params['size']),
                "side": params['action'],
                "orderType": params.get('order_type', 'market'),
                "timeInForceValue": 'normal',  # 普通限价单
                "reduceOnly": False  # 开仓单
            }
            
            # 如果是限价单，添加价格
            if params.get('order_type') == 'limit':
                if not params.get('price'):
                    raise ValueError("限价单需要提供价格")
                order_params["price"] = str(params['price'])
            
            # 添加止盈止损价格
            if params.get('take_profit'):
                order_params["presetTakeProfitPrice"] = str(params['take_profit'])
            if params.get('stop_loss'):
                order_params["presetStopLossPrice"] = str(params['stop_loss'])
            
            print("===============下单参数==================")
            print(f"交易对: {symbol}")
            print(f"保证金币种: {self._margin_coin}")
            print(f"订单参数: {order_params}")
            
            # 执行下单
            result = self._client.mix_place_order(**order_params)
            print("下单结果:", result)
            
            return result
            
        except Exception as e:
            raise Exception(f"下单失败: {str(e)}")

    def _get_position_info(self, trading_pair: str) -> Dict[str, Any]:
        """获取持仓信息"""
        try:
            # 获取所有持仓信息
            result = self._client.mix_get_all_positions(
                productType=self._product_type,
                marginCoin=self._margin_coin
            )
            print("===============获取所有持仓信息==================")
            print(f"接口响应：{result}")
            print(f"产品类型: {self._product_type}")
            print(f"保证金币种: {self._margin_coin}")

            if result and 'data' in result and result['data']:
                # 找到对应交易对的持仓
                for position in result['data']:
                    if position.get('symbol', '').startswith(trading_pair):
                        position_info = self._format_position_info(position)
                        self._save_position(position)
                        return position_info
            return []
        except Exception as e:
            raise Exception(f"获取持仓信息失败: {str(e)}")

    def _save_trade(self, params: Dict[str, Any], order_id: str, side: str) -> None:
        """保存交易记录"""
        self._db.save_trade(
            strategy_id=self._strategy_id,
            signal_id=params.get('signal_id'),
            trading_pair=self._trading_pair,
            order_id=order_id,
            side=side,
            order_type=params['order_type'],
            price=params.get('price', 0),
            size=params['size'],
            leverage=self._leverage,
            status='open'
        )



if __name__ == '__main__':
    # 测试工具
    tool = BitgetTradeTool()
    
    # 测试执行交易
    trade_result = tool.run(
        trading_pair="BTC",
        strategy_id=1,
        action="open_long",
        order_type="limit",
        price=105550,
        size=0.001,
        stop_loss=104550,
        take_profit=106550
    )
    print("交易结果:", trade_result) 