# src/gold_agents/tools/wecom_notification_tool.py
import requests
import json
import os
import datetime
from typing import Any, Optional, Type, Dict, Union

from crewai.tools import BaseTool
from pydantic import BaseModel, Field
from typing import Optional


class WeComNotificationToolSchema(BaseModel):
    message: str = Field(..., description="企业微信推送的消息，必须是标准策略JSON格式或格式化的字符串")
    symbol: str = Field(..., description="合约类型BTC / ETH")


class WeComNotificationTool(BaseTool):
    name: str = "企业微信通知工具"
    description: str = ("用于将交易策略通过企业微信机器人发送到指定的群聊。"
                        "输入参数必须是标准策略JSON格式或格式化的消息字符串。"
                        "工具会自动将JSON数据格式化为标准的策略提醒格式。"
                        )
    message: Optional[str] = ""
    force_push: bool = False
    args_schema: Type[BaseModel] = WeComNotificationToolSchema
    symbol: str = ""

    def __init__(self, message: Optional[str] = None, force_push: bool = False ,symbol: str = ""):
        super().__init__()
        self.message = message
        self.force_push = force_push
        self.symbol = symbol

    def _validate_strategy_json(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证策略JSON数据是否符合标准格式，如果不符合则尝试转换或补充"""
        # 检查是否为交易策略数据
        if not any(key in data for key in [
            "short_term_strategy", "medium_term_strategy", "long_term_strategy", 
            "final_decision", "entry", "take_profit", "stop_loss"
        ]):
            # 不是标准交易策略数据，尝试构建标准格式
            if "content" in data and isinstance(data["content"], str):
                # 如果是纯文本内容，构建一个风险提示
                return {
                    "risk_tip": data["content"],
                    "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            else:
                # 返回最小化的有效数据结构
                return {
                    "risk_tip": "收到非标准格式的策略信息，请谨慎操作",
                    "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
        # 标准化现有字段
        if "final_decision" in data and not any(x in data for x in ["short_term_strategy", "medium_term_strategy", "long_term_strategy"]):
            # 老格式，转换为新格式 (short_term_strategy)
            
            # 提取所有相关字段
            final_decision = data.get("final_decision", "")
            entry = data.get("entry", "")
            take_profit = data.get("take_profit", "")
            stop_loss = data.get("stop_loss", "")
            
            trend = data.get("trend", "")
            if not trend and "trend_1h" in data and "trend_4h" in data:
                trend = f"{data.get('trend_1h', '')}（1h）, {data.get('trend_4h', '')}（4h）"
                
            signal_strength = data.get("signal_strength", "") or data.get("confidence", "")
            support = data.get("support", "")
            resistance = data.get("resistance", "")
            
            # 构建风险提示，包含情绪和政策（如果存在）
            sentiment = data.get("sentiment", "")
            policy = data.get("policy_trend", "") or (data.get("supporting_factors", [""])[0] if isinstance(data.get("supporting_factors", []), list) and data.get("supporting_factors", []) else "")
            risk_reason = data.get("reason", "") or "市场波动较大，请谨慎操作"
            risk_tip = f"{risk_reason}"
            if sentiment or policy:
                 risk_tip += f" (情绪: {sentiment}, 政策: {policy})"

            return {
                "short_term_strategy": {
                    "final_decision": final_decision,
                    "entry": entry,
                    "take_profit": take_profit,
                    "stop_loss": stop_loss,
                    "trend": trend,
                    "signal_strength": signal_strength,
                    "support": support, # 确保包含
                    "resistance": resistance # 确保包含
                },
                "risk_tip": risk_tip # 使用包含情绪/政策的风险提示
            }
            
        # 如果已经是新格式，确保必要的字段存在
        if "short_term_strategy" in data or "medium_term_strategy" in data or "long_term_strategy" in data:
            for period in ["short_term_strategy", "medium_term_strategy", "long_term_strategy"]:
                if period in data and isinstance(data[period], dict):
                    # 确保每个周期的必要字段存在
                    strat = data[period]
                    # 添加默认值，如果缺少必要字段
                    if "final_decision" not in strat:
                        strat["final_decision"] = "hold"
                        
        return data

    def _format_single_period_message(self, period_name: str, strategy_data: Dict[str, Any], current_time: str, risk_tip: str, force_push: bool) -> Optional[str]:
        """格式化单个周期的策略消息"""
        decision = strategy_data.get('final_decision', '').lower()
        if decision == 'buy':
            decision_text = "看多"
        elif decision == 'sell':
            decision_text = "看空"
        elif decision in ['hold', 'wait', 'neutral']:
            decision_text = "观望"
        else:
            decision_text = decision

        # 根据强制推送设置决定是否过滤观望策略
        if not force_push and decision_text in ["观望"]:
            return None

        # 对观望策略，允许字段缺失也输出消息
        if decision_text == "观望":
            formatted_message = f"🌟 <font color=\"gold\">【XAU/USD-策略】</font>\n⏰ 时间：{current_time}\n✅ 策略建议：【{period_name}】观望\n⚠️ 风险提示：{risk_tip}"
            return formatted_message

        entry = strategy_data.get("entry", "")
        if entry and not isinstance(entry, str):
            entry = str(entry)

        take_profit = strategy_data.get("take_profit", "")
        if take_profit and not isinstance(take_profit, str):
            take_profit = str(take_profit)

        stop_loss = strategy_data.get("stop_loss", "")
        if stop_loss and not isinstance(stop_loss, str):
            stop_loss = str(stop_loss)

        trend = strategy_data.get("trend", "")
        signal_strength = strategy_data.get("signal_strength", "")
        support = strategy_data.get("support", "")
        resistance = strategy_data.get("resistance", "")

        # 判断是否有入场机会
        if decision_text in ["看多", "看空"]:
            action_msg = f"<font color=\"red\">【{period_name}】{decision_text} @ {entry}</font>"
            tp_sl_msg = f"🎯 止盈：{take_profit}，止损：{stop_loss}"
        else:
            action_msg = f"【{period_name}】{decision_text}"
            tp_sl_msg = ""

        # formatted_message = f"🌟 <font color=\"gold\">【XAU/USD-策略】</font>\n⏰ 时间：{current_time}\n📊 趋势：{trend}\n📈 信号强度：{signal_strength}\n🔍 支撑：{support}，阻力：{resistance}\n✅ 策略建议：{action_msg}"
        formatted_message = f"🌟 <font color=\"gold\">【XAU/USD-策略】</font>\n⏰ 时间：{current_time}\n📊 趋势：{trend}\n✅ 策略建议：{action_msg}"

        if tp_sl_msg:
            formatted_message += f"\n{tp_sl_msg}"

        # formatted_message += f"\n⚠️ 风险提示：{risk_tip}"
        return formatted_message

    def _format_strategy_message(self, data: Dict[str, Any], force_push: bool = False) -> list:
        """将策略JSON数据格式化为标准的企业微信通知格式，适配多周期结构化策略内容
        
        参数：
            data: 策略JSON数据
            force_push: 是否强制推送，如果为True，则不过滤观望策略（已废弃，生成时不再过滤）
        
        返回值：
            - 如果是多周期策略，返回消息列表，每个元素是一个周期的消息
            - 如果是单周期策略，返回包含单个消息的列表
        """
        # 先验证和标准化数据
        data = self._validate_strategy_json(data)
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M")
        messages = []

        # 兼容多周期结构化策略内容
        # 1. 先判断是否为新结构（含short_term_strategy/medium_term_strategy等）
        if "short_term_strategy" in data or "medium_term_strategy" in data or "long_term_strategy" in data:
            # 创建多个消息，每个周期一个
            risk_tip = data.get("risk_tip", "市场波动较大，请谨慎操作")

            # 短期策略
            if "short_term_strategy" in data:
                short_msg = self._format_single_period_message("短期", data["short_term_strategy"], current_time, risk_tip, force_push)
                if short_msg:
                    messages.append(short_msg)

            # 中期策略
            if "medium_term_strategy" in data:
                medium_msg = self._format_single_period_message("中期", data["medium_term_strategy"], current_time, risk_tip, force_push)
                if medium_msg:
                    messages.append(medium_msg)

            # 长期策略
            if "long_term_strategy" in data:
                long_msg = self._format_single_period_message("长期", data["long_term_strategy"], current_time, risk_tip, force_push)
                if long_msg:
                    messages.append(long_msg)
            
            # # 如果有消息，直接返回，不再处理原有结构 (注释掉这部分，确保所有周期都被处理)
            # if messages:
            #     return messages
        
        # 如果messages列表不为空（即处理了新结构的多周期或转换后的旧结构），则直接返回
        # 注意：如果旧结构被转换为观望策略且force_push=False，messages可能为空
        return messages

    def _run(self, message: str,symbol:str) -> str:
        """使用工具发送企业微信消息。"""
        try:
            # 尝试提取消息中的JSON部分
            json_data = None
            # 检查消息是否包含JSON格式的数据（被花括号包围的部分）
            import re
            # 使用更精确的正则表达式匹配完整的JSON对象
            json_match = re.search(r'\{[\s\S]*?\}(?=\s*$|\s*\n|\s*\r)', message)
            if json_match:
                try:
                    json_str = json_match.group(0)
                    json_data = json.loads(json_str)
                except json.JSONDecodeError:
                    # JSON解析失败，继续使用原始消息处理
                    pass
            
            # 如果成功提取到JSON数据
            if json_data:
                # 格式化消息，传递强制推送参数
                formatted_messages = self._format_strategy_message(json_data, self.force_push)
                
                # 检查是否有消息需要发送
                if formatted_messages and len(formatted_messages) > 0:
                    # 分别发送每个消息，并在发送间隔1秒
                    results = []
                    for msg in formatted_messages:
                        result = self._send_message(msg)
                        results.append(result)
                        # 添加短暂延迟，确保消息顺序正确且避免频率限制
                        import time
                        time.sleep(1)
                    
                    # 返回所有消息的发送状态
                    return {
                        "send_status": 200,
                        "send_msg": f'成功发送 {len(results)} 条消息',
                        "message_preview": "\n\n---\n\n".join([r.get("message_preview", "") for r in results])
                    }
                else:
                    # 没有需要发送的消息（全部是观望策略）
                    if self.force_push:
                        # 强制推送模式下，应该有观望策略的消息
                        return {
                            "send_status": 500,
                            "send_msg": '强制推送模式下未能发送观望策略，请检查代码逻辑',
                            "message_preview": "强制推送模式下应该发送观望策略，但未能成功格式化消息"
                        }
                    else:
                        return {
                            "send_status": 200,
                            "send_msg": '没有需要发送的消息，所有策略均为观望',
                            "message_preview": "当前所有策略均为观望，根据设置不发送消息"
                        }
            
            # 如果通过正则没有提取到JSON数据，尝试将整个消息解析为JSON
            else:
                try:
                    json_data = json.loads(message)
                    # 如果成功解析为JSON，则格式化消息，传递强制推送参数
                    formatted_messages = self._format_strategy_message(json_data, self.force_push)
                    
                    # 检查是否有消息需要发送
                    if formatted_messages and len(formatted_messages) > 0:
                        # 分别发送每个消息，并在发送间隔1秒
                        results = []
                        for msg in formatted_messages:
                            result = self._send_message(msg)
                            results.append(result)
                            # 添加短暂延迟，确保消息顺序正确且避免频率限制
                            import time
                            time.sleep(1)
                        
                        # 返回所有消息的发送状态
                        return {
                            "send_status": 200,
                            "send_msg": f'成功发送 {len(results)} 条消息',
                            "message_preview": "\n\n---\n\n".join([r.get("message_preview", "") for r in results])
                        }
                    else:
                        # 没有需要发送的消息（全部是观望策略）
                        if self.force_push:
                            # 强制推送模式下，应该有观望策略的消息
                            return {
                                "send_status": 500,
                                "send_msg": '强制推送模式下未能发送观望策略，请检查代码逻辑',
                                "message_preview": "强制推送模式下应该发送观望策略，但未能成功格式化消息"
                            }
                        else:
                            return {
                                "send_status": 200,
                                "send_msg": '没有需要发送的消息，所有策略均为观望',
                                "message_preview": "当前所有策略均为观望，根据设置不发送消息"
                            }
                
                except json.JSONDecodeError:
                    # 如果整个消息也无法解析为JSON，则返回错误
                    return {
                        "send_status": 400, # Bad Request
                        "send_msg": '输入消息不是有效的JSON格式。',
                        "message_preview": "无法处理的输入消息格式"
                    }

        except Exception as e:
            # 处理发送或格式化过程中可能出现的任何其他异常
            return {
                "send_status": 500,
                "send_msg": f'处理或发送企业微信消息时出错: {e}',
                "message_preview": "处理或发送失败"
            }
            
            
    def _send_message(self, formatted_message: str) -> Dict[str, Any]:
        """发送单个消息到企业微信"""
        robot_name = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=22f9bc8d-bc61-45f2-beac-d996dbe425d3'
        # 构造企业微信消息 (统一风格)
        wecom_message = {
            "msgtype": "markdown",
            "markdown": {
                "content": formatted_message
            }
        }

        # 发送消息到企业微信机器人
        headers = {'Content-Type': 'application/json'}
        response = requests.post(robot_name, headers=headers, data=json.dumps(wecom_message))

        if response.status_code == 200 and response.json().get("errcode") == 0:
            return {
                "send_status": 200,
                "send_msg": 'success',
                "message_preview": formatted_message
            }
        else:
            return {
                "send_status": response.status_code,
                "send_msg": response.json(),
                "message_preview": formatted_message
            }

    def mock_test(self, message: str, mock_response: bool = True) -> Dict[str, Any]:
        """模拟测试企业微信消息发送功能，不实际发送消息
        
        Args:
            message: 要发送的消息，可以是JSON字符串、格式化的消息字符串或混合格式
            mock_response: 是否模拟响应，默认为True，如果为False则实际发送消息
            
        Returns:
            Dict: 包含发送状态、消息和预览的字典
        """
        try:
            # 尝试提取消息中的JSON部分
            json_data = None
            # 检查消息是否包含JSON格式的数据（被花括号包围的部分）
            import re
            # 使用更精确的正则表达式匹配完整的JSON对象
            json_match = re.search(r'\{[\s\S]*?\}(?=\s*$|\s*\n|\s*\r)', message)
            if json_match:
                try:
                    json_str = json_match.group(0)
                    json_data = json.loads(json_str)
                except json.JSONDecodeError:
                    # JSON解析失败，继续使用原始消息处理
                    pass
            
            # 如果成功提取到JSON数据
            if json_data:
                # 格式化消息，传递强制推送参数
                formatted_messages = self._format_strategy_message(json_data, self.force_push)
                
                # 检查是否有消息需要发送
                if not formatted_messages or len(formatted_messages) == 0:
                    # 没有需要发送的消息（全部是观望策略）
                    if self.force_push:
                        # 强制推送模式下，应该有观望策略的消息
                        return {
                            "send_status": 500,
                            "send_msg": '强制推送模式下未能发送观望策略，请检查代码逻辑 (mocked)',
                            "message_preview": "强制推送模式下应该发送观望策略，但未能成功格式化消息",
                            "is_mocked": True
                        }
                    else:
                        return {
                            "send_status": 200,
                            "send_msg": '没有需要发送的消息，所有策略均为观望 (mocked)',
                            "message_preview": "当前所有策略均为观望，根据设置不发送消息",
                            "is_mocked": True
                        }
                
                # 构造企业微信消息预览
                message_previews = []
                wecom_messages = []
                
                for msg in formatted_messages:
                    message_previews.append(msg)
                    wecom_messages.append({
                        "msgtype": "markdown",
                        "markdown": {
                            "content": msg
                        }
                    })
                
                if mock_response:
                    # 模拟成功响应
                    return {
                        "send_status": 200,
                        "send_msg": f'成功发送 {len(formatted_messages)} 条消息',
                        "message_preview": message_previews[0] if message_previews else "",
                        "all_message_previews": message_previews,
                        "wecom_payload": wecom_messages,
                        "is_mocked": True
                    }
            
            # 如果没有提取到JSON或JSON处理失败，尝试直接解析整个消息为JSON
            try:
                data = json.loads(message)
                # 如果成功解析为JSON，则格式化消息，传递强制推送参数
                formatted_messages = self._format_strategy_message(data, self.force_push)
                
                # 检查是否有消息需要发送
                if not formatted_messages or len(formatted_messages) == 0:
                    # 没有需要发送的消息（全部是观望策略）
                    if self.force_push:
                        # 强制推送模式下，应该有观望策略的消息
                        return {
                            "send_status": 500,
                            "send_msg": '强制推送模式下未能发送观望策略，请检查代码逻辑 (mocked)',
                            "message_preview": "强制推送模式下应该发送观望策略，但未能成功格式化消息",
                            "is_mocked": True
                        }
                    else:
                        return {
                            "send_status": 200,
                            "send_msg": '没有需要发送的消息，所有策略均为观望 (mocked)',
                            "message_preview": "当前所有策略均为观望，根据设置不发送消息",
                            "is_mocked": True
                        }
                
                # 构造企业微信消息预览
                message_previews = []
                wecom_messages = []
                
                for msg in formatted_messages:
                    message_previews.append(msg)
                    wecom_messages.append({
                        "msgtype": "markdown",
                        "markdown": {
                            "content": msg
                        }
                    })
                
                if mock_response:
                    # 模拟成功响应
                    return {
                        "send_status": 200,
                        "send_msg": f'成功发送 {len(formatted_messages)} 条消息',
                        "message_preview": message_previews[0] if message_previews else "",
                        "all_message_previews": message_previews,
                        "wecom_payload": wecom_messages,
                        "is_mocked": True
                    }
            
            except json.JSONDecodeError:
                # JSON解析失败，返回明确的错误信息
                return {
                    "send_status": 400, # Bad Request
                    "send_msg": '输入消息无法解析为有效的JSON格式 (mocked)',
                    "message_preview": f"无法解析以下输入为JSON：\n---\n{message}\n---",
                    "is_mocked": True
                }

            # 如果代码执行到这里，说明既没有成功提取JSON，也没有直接解析JSON成功
            # 这通常意味着输入不是JSON格式
            # 检查是否包含基本的策略信息关键词，以提供更友好的提示
            if any(keyword in message.lower() for keyword in ["buy", "sell", "多", "空", "hold", "wait", "neutral", "持有", "观望"]):
                # 看起来像策略信息，但格式不正确
                formatted_message = f"🌟 【{self.symbol}-策略】\n⏰ 时间：{datetime.datetime.now().strftime('%Y-%m-%d %H:%M')}\n\n{message}\n\n⚠️ 提示：此消息未使用标准格式，请谨慎参考"
                return {
                    "send_status": 400, # Bad Request
                    "send_msg": '输入消息看起来像策略但不是有效的JSON格式 (mocked)',
                    "message_preview": formatted_message,
                    "is_mocked": True
                }
            else:
                # 不像策略信息，直接使用原始消息进行模拟发送
                formatted_message = message
                return {
                    "send_status": 200,
                    "send_msg": '成功模拟发送原始非JSON消息 (mocked)',
                    "message_preview": formatted_message,
                    "is_mocked": True
                }
                
        except requests.exceptions.RequestException as e:
            # 这部分是模拟网络请求错误，保持不变
            return {
                "send_status": 500,
                "send_msg": f'模拟发送企业微信消息时出错: {e} (mocked)',
                "message_preview": "发送失败",
                "is_mocked": True
            }
        except Exception as e:
            # 这部分是模拟其他处理错误，保持不变
            return {
                "send_status": 500,
                "send_msg": f'模拟处理企业微信消息时发生未知错误: {e} (mocked)',
                "message_preview": "处理失败",
                "is_mocked": True
            }


if __name__ == '__main__':
    # 测试JSON格式输入 - 标准格式
    # test_json = json.dumps({
    #     "final_decision": "buy",
    #     "entry": 2352.0,
    #     "take_profit": 2370.0,
    #     "stop_loss": 2332.0,
    #     "trend_1h": "上涨",
    #     "trend_4h": "上涨",
    #     "sentiment": "积极",
    #     "policy_trend": "中国增持",
    #     "reason": "地缘政治不确定性高"
    # })

    # 测试JSON格式输入 - 多周期格式
    test_multi_period_json = json.dumps(
        {
            "short_term_strategy": {
                "final_decision": "hold",
                "entry": 3314.65,
                "take_profit": 3301.4,
                "stop_loss": 3327.94,
                "trend": "震荡偏空",
                "signal_strength": "中等",
                "support": "3301.4",
                "resistance": "3314.65, 3327.94",
                "key_patterns": []
            },
            "medium_term_strategy": {
                "final_decision": "hold",
                "entry": 3314.65,
                "take_profit": 3250.00,
                "stop_loss": 3350.00,
                "trend": "震荡",
                "signal_strength": "弱",
                "support": "3301.4",
                "resistance": "3327.94",
                "key_patterns": []
            },
            "risk_tip": "当前市场波动性中等，地缘政治风险较高，建议谨慎操作，控制仓位。"
        }
        
    )

    # 测试字符串格式输入
    # test_string = """🌟 【XAU/USD-策略】\n⏰ 时间：2023-04-23 14:00\n📊 趋势：上涨（1h, 4h）\n💬 情绪：积极\n🏛️ 政策：中国增持\n✅ 策略建议：Buy @ 2352\n🎯 止盈：2370，止损：2332\n⚠️ 风险提示：地缘政治不确定性高"""
    
    # 创建工具实例
    tool = WeComNotificationTool(force_push=False)
    
    # 使用模拟测试方法测试
    # print("\n=== 模拟测试标准JSON格式 ===")
    # mock_result = tool._run(test_json)
    # print(f"发送状态: {mock_result['send_status']}")
    # print(f"发送消息: {mock_result['send_msg']}")
    # print(f"消息预览:\n{mock_result['message_preview']}")
    
    print("\n=== 模拟测试多周期JSON格式 ===")
    mock_result = tool._run(test_multi_period_json)
    print(f"发送状态: {mock_result['send_status']}")
    print(f"发送消息: {mock_result['send_msg']}")
    print(f"消息预览:\n{mock_result['message_preview']}")
    
    print("\n=== 模拟测试字符串格式 ===")
    # mock_result = tool.mock_test(test_string)
    # print(f"发送状态: {mock_result['send_status']}")
    # print(f"发送消息: {mock_result['send_msg']}")
    # print(f"消息预览:\n{mock_result['message_preview']}")
    
    # 如果需要实际发送消息，可以取消下面的注释
    # print("\n=== 实际发送消息 ===")
    # status = tool._run(test_json)
    # print(status)
