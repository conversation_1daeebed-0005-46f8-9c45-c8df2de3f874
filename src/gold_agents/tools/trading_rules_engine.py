#!/usr/bin/env python3
"""
交易准则执行引擎 - 确保所有交易决策符合既定的交易准则
"""

import yaml
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from pydantic import BaseModel, Field
from crewai_tools import BaseTool
from pathlib import Path


class TradingRulesEngineSchema(BaseModel):
    """交易准则引擎的输入参数模式"""
    action: str = Field(..., description="操作类型：validate_trade, check_risk, evaluate_performance")
    trade_data: Optional[dict] = Field(None, description="交易数据")
    account_data: Optional[dict] = Field(None, description="账户数据")
    market_data: Optional[dict] = Field(None, description="市场数据")


class TradingRulesEngine(BaseTool):
    """交易准则执行引擎"""
    name: str = "TradingRulesEngine"
    description: str = """
    这是一个用于执行交易准则的工具。
    主要功能：
    1. 验证交易决策是否符合交易准则
    2. 检查风险控制措施
    3. 评估绩效表现
    4. 执行应急处理机制
    
    使用示例：
    1. 验证交易：
       tool.run(action="validate_trade", trade_data={...}, account_data={...})
    
    2. 检查风险：
       tool.run(action="check_risk", account_data={...}, market_data={...})
    
    3. 评估绩效：
       tool.run(action="evaluate_performance", account_data={...})
    """
    args_schema: type[BaseModel] = TradingRulesEngineSchema

    def __init__(self, rules_config_path: str = None, **kwargs):
        """初始化交易准则引擎"""
        super().__init__(**kwargs)
        
        if rules_config_path is None:
            rules_config_path = Path(__file__).parent.parent / "config" / "trading_rules.yaml"
        
        self.rules_config_path = rules_config_path
        self.rules = self._load_trading_rules()
        self.violation_history = []

    def _load_trading_rules(self) -> Dict[str, Any]:
        """加载交易准则配置"""
        try:
            with open(self.rules_config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"加载交易准则失败: {str(e)}")
            return {}

    def _run(self, **kwargs: Any) -> Dict[str, Any]:
        """执行交易准则检查"""
        try:
            action = kwargs.get('action')
            
            if action == 'validate_trade':
                return self._validate_trade_decision(
                    kwargs.get('trade_data', {}),
                    kwargs.get('account_data', {}),
                    kwargs.get('market_data', {})
                )
            elif action == 'check_risk':
                return self._check_risk_compliance(
                    kwargs.get('account_data', {}),
                    kwargs.get('market_data', {})
                )
            elif action == 'evaluate_performance':
                return self._evaluate_performance(kwargs.get('account_data', {}))
            elif action == 'emergency_check':
                return self._emergency_procedures_check(
                    kwargs.get('account_data', {}),
                    kwargs.get('market_data', {})
                )
            else:
                return {
                    "status": "error",
                    "message": f"不支持的操作类型: {action}",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
        except Exception as e:
            return {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

    def _validate_trade_decision(self, trade_data: Dict, account_data: Dict, market_data: Dict) -> Dict[str, Any]:
        """验证交易决策是否符合准则"""
        try:
            violations = []
            warnings = []
            
            # 检查入场规则
            entry_violations = self._check_entry_rules(trade_data, account_data, market_data)
            violations.extend(entry_violations)
            
            # 检查仓位管理规则
            position_violations = self._check_position_rules(trade_data, account_data)
            violations.extend(position_violations)
            
            # 检查风险控制规则
            risk_violations = self._check_risk_rules(trade_data, account_data)
            violations.extend(risk_violations)
            
            # 检查策略信号质量
            signal_warnings = self._check_signal_quality(trade_data)
            warnings.extend(signal_warnings)
            
            # 判断是否允许交易
            is_allowed = len(violations) == 0
            
            result = {
                "status": "success",
                "is_allowed": is_allowed,
                "violations": violations,
                "warnings": warnings,
                "risk_score": self._calculate_risk_score(trade_data, account_data),
                "recommendations": self._generate_recommendations(violations, warnings),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            # 记录违规历史
            if violations:
                self.violation_history.append({
                    "timestamp": datetime.now(),
                    "violations": violations,
                    "trade_data": trade_data
                })
            
            return result
            
        except Exception as e:
            raise Exception(f"验证交易决策失败: {str(e)}")

    def _check_entry_rules(self, trade_data: Dict, account_data: Dict, market_data: Dict) -> List[str]:
        """检查入场规则"""
        violations = []
        entry_rules = self.rules.get('mandatory_rules', {}).get('entry_rules', [])
        
        # 检查策略信号强度
        signal_strength = trade_data.get('signal_strength', 0)
        min_strength = self.rules.get('decision_framework', {}).get('signal_evaluation', {}).get('strength_thresholds', {}).get('medium', 0.7)
        
        if signal_strength < min_strength:
            violations.append(f"信号强度{signal_strength:.2f}低于最低要求{min_strength}")
        
        # 检查账户风险敞口
        position_size = trade_data.get('size', 0)
        entry_price = trade_data.get('price', 0)
        position_value = position_size * entry_price
        account_balance = account_data.get('balance', 0)
        
        if account_balance > 0:
            risk_ratio = position_value / account_balance
            max_risk = self.rules.get('mandatory_rules', {}).get('position_rules', [])
            # 从规则文本中提取2%的限制
            if risk_ratio > 0.02:
                violations.append(f"单笔交易风险{risk_ratio:.2%}超过2%限制")
        
        # 检查止损止盈设置
        if not trade_data.get('stop_loss') or not trade_data.get('take_profit'):
            violations.append("必须设置止损和止盈")
        
        # 检查市场流动性
        market_depth = market_data.get('depth', 0)
        min_depth = self.rules.get('decision_framework', {}).get('market_conditions', {}).get('liquidity_requirements', {}).get('min_depth', 100000)
        
        if market_depth < min_depth:
            violations.append(f"市场深度{market_depth}低于最低要求{min_depth}")
        
        return violations

    def _check_position_rules(self, trade_data: Dict, account_data: Dict) -> List[str]:
        """检查仓位管理规则"""
        violations = []
        
        # 检查总持仓限制
        current_position_value = account_data.get('position_value', 0)
        new_position_value = trade_data.get('size', 0) * trade_data.get('price', 0)
        total_position_value = current_position_value + new_position_value
        account_balance = account_data.get('balance', 0)
        
        if account_balance > 0:
            total_position_ratio = total_position_value / account_balance
            if total_position_ratio > 0.20:  # 20%限制
                violations.append(f"总持仓{total_position_ratio:.2%}超过20%限制")
        
        # 检查杠杆倍数
        leverage = trade_data.get('leverage', 1)
        max_leverage = self.rules.get('mandatory_rules', {}).get('position_rules', [])
        if leverage > 5:  # 从规则中提取5倍限制
            violations.append(f"杠杆倍数{leverage}超过5倍限制")
        
        # 检查保证金率
        margin_ratio = account_data.get('margin_ratio', 1.0)
        if margin_ratio < 0.50:  # 50%限制
            violations.append(f"保证金率{margin_ratio:.2%}低于50%限制")
        
        return violations

    def _check_risk_rules(self, trade_data: Dict, account_data: Dict) -> List[str]:
        """检查风险控制规则"""
        violations = []
        
        # 检查日亏损限制
        daily_pnl = account_data.get('daily_pnl', 0)
        account_balance = account_data.get('balance', 0)
        
        if account_balance > 0 and daily_pnl < 0:
            daily_loss_ratio = abs(daily_pnl) / account_balance
            if daily_loss_ratio > 0.05:  # 5%限制
                violations.append(f"日亏损{daily_loss_ratio:.2%}超过5%限制，禁止交易")
        
        # 检查连续亏损
        consecutive_losses = account_data.get('consecutive_losses', 0)
        if consecutive_losses >= 3:
            violations.append("连续3笔亏损，暂停交易")
        
        # 检查最大回撤
        max_drawdown = account_data.get('max_drawdown', 0)
        if max_drawdown > 0.10:  # 10%限制
            violations.append(f"最大回撤{max_drawdown:.2%}超过10%，启动应急措施")
        
        return violations

    def _check_signal_quality(self, trade_data: Dict) -> List[str]:
        """检查策略信号质量"""
        warnings = []
        
        # 检查信号置信度
        confidence = trade_data.get('confidence', 0)
        preferred_confidence = self.rules.get('decision_framework', {}).get('signal_evaluation', {}).get('confidence_requirements', {}).get('preferred', 0.75)
        
        if confidence < preferred_confidence:
            warnings.append(f"信号置信度{confidence:.2f}低于偏好水平{preferred_confidence}")
        
        # 检查策略一致性
        strategy_type = trade_data.get('strategy_type', '')
        if not strategy_type:
            warnings.append("缺少策略类型信息")
        
        return warnings

    def _calculate_risk_score(self, trade_data: Dict, account_data: Dict) -> float:
        """计算风险评分（0-100，越高风险越大）"""
        risk_score = 0
        
        # 基于仓位大小的风险
        position_size = trade_data.get('size', 0)
        entry_price = trade_data.get('price', 0)
        position_value = position_size * entry_price
        account_balance = account_data.get('balance', 1)
        
        position_risk = (position_value / account_balance) * 100
        risk_score += min(position_risk * 5, 30)  # 最多30分
        
        # 基于杠杆的风险
        leverage = trade_data.get('leverage', 1)
        leverage_risk = (leverage - 1) * 5
        risk_score += min(leverage_risk, 20)  # 最多20分
        
        # 基于市场波动率的风险
        volatility = trade_data.get('market_volatility', 0.02)
        volatility_risk = volatility * 1000
        risk_score += min(volatility_risk, 25)  # 最多25分
        
        # 基于信号质量的风险
        signal_strength = trade_data.get('signal_strength', 0.5)
        signal_risk = (1 - signal_strength) * 25
        risk_score += signal_risk  # 最多25分
        
        return min(risk_score, 100)

    def _generate_recommendations(self, violations: List[str], warnings: List[str]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        if violations:
            recommendations.append("立即修正违规项目后再进行交易")
            
            for violation in violations:
                if "信号强度" in violation:
                    recommendations.append("等待更强的策略信号或降低仓位大小")
                elif "风险" in violation and "超过" in violation:
                    recommendations.append("减少交易规模或提高账户余额")
                elif "止损" in violation:
                    recommendations.append("设置合理的止损和止盈价格")
                elif "保证金率" in violation:
                    recommendations.append("增加保证金或减少持仓")
        
        if warnings:
            recommendations.append("考虑优化以下方面以提高交易质量")
            
            for warning in warnings:
                if "置信度" in warning:
                    recommendations.append("等待更高置信度的信号或使用更保守的仓位")
                elif "策略类型" in warning:
                    recommendations.append("确保策略信息完整性")
        
        return recommendations

    def _check_risk_compliance(self, account_data: Dict, market_data: Dict) -> Dict[str, Any]:
        """检查风险合规性"""
        try:
            risk_issues = []
            risk_level = "low"
            
            # 检查账户风险指标
            margin_ratio = account_data.get('margin_ratio', 1.0)
            if margin_ratio < 0.3:
                risk_issues.append("保证金率过低，需要立即处理")
                risk_level = "critical"
            elif margin_ratio < 0.5:
                risk_issues.append("保证金率偏低，建议减仓")
                risk_level = "high"
            
            # 检查日亏损
            daily_pnl = account_data.get('daily_pnl', 0)
            account_balance = account_data.get('balance', 1)
            daily_loss_ratio = abs(daily_pnl) / account_balance if daily_pnl < 0 else 0
            
            if daily_loss_ratio > 0.05:
                risk_issues.append("日亏损超过5%，停止交易")
                risk_level = "critical"
            elif daily_loss_ratio > 0.03:
                risk_issues.append("日亏损偏高，谨慎交易")
                risk_level = "high" if risk_level != "critical" else risk_level
            
            # 检查市场风险
            volatility = market_data.get('volatility', 0.02)
            if volatility > 0.08:
                risk_issues.append("市场波动率极高，建议暂停交易")
                risk_level = "high" if risk_level not in ["critical"] else risk_level
            
            return {
                "status": "success",
                "risk_level": risk_level,
                "risk_issues": risk_issues,
                "compliance_status": "compliant" if not risk_issues else "non_compliant",
                "recommended_actions": self._get_risk_actions(risk_level, risk_issues),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            raise Exception(f"检查风险合规性失败: {str(e)}")

    def _get_risk_actions(self, risk_level: str, risk_issues: List[str]) -> List[str]:
        """获取风险应对措施"""
        actions = []
        
        if risk_level == "critical":
            actions.extend([
                "立即停止所有新开仓",
                "评估现有持仓风险",
                "考虑紧急平仓",
                "发送风险警报"
            ])
        elif risk_level == "high":
            actions.extend([
                "暂停新开仓",
                "减少现有仓位",
                "提高风险监控频率",
                "准备应急措施"
            ])
        elif risk_level == "medium":
            actions.extend([
                "谨慎开仓",
                "加强风险监控",
                "优化止损设置"
            ])
        
        return actions

    def _evaluate_performance(self, account_data: Dict) -> Dict[str, Any]:
        """评估绩效表现"""
        try:
            # 获取绩效标准
            daily_standards = self.rules.get('performance_standards', {}).get('daily_metrics', {})
            
            # 计算当前绩效指标
            current_metrics = {
                "win_rate": account_data.get('win_rate', 0),
                "profit_factor": account_data.get('profit_factor', 0),
                "max_drawdown": account_data.get('max_drawdown', 0),
                "daily_return": account_data.get('daily_return', 0)
            }
            
            # 评估绩效
            performance_issues = []
            performance_grade = "excellent"
            
            # 检查胜率
            if current_metrics['win_rate'] < daily_standards.get('min_win_rate', 0.6):
                performance_issues.append(f"胜率{current_metrics['win_rate']:.2%}低于标准")
                performance_grade = "poor"
            
            # 检查盈利因子
            if current_metrics['profit_factor'] < daily_standards.get('min_profit_factor', 1.5):
                performance_issues.append(f"盈利因子{current_metrics['profit_factor']:.2f}低于标准")
                performance_grade = "poor" if performance_grade != "poor" else performance_grade
            
            # 检查最大回撤
            if current_metrics['max_drawdown'] > daily_standards.get('max_drawdown', 0.03):
                performance_issues.append(f"最大回撤{current_metrics['max_drawdown']:.2%}超过标准")
                performance_grade = "poor" if performance_grade != "poor" else performance_grade
            
            if not performance_issues and performance_grade != "poor":
                if (current_metrics['win_rate'] > 0.7 and 
                    current_metrics['profit_factor'] > 2.0 and 
                    current_metrics['max_drawdown'] < 0.02):
                    performance_grade = "excellent"
                else:
                    performance_grade = "good"
            
            return {
                "status": "success",
                "performance_grade": performance_grade,
                "current_metrics": current_metrics,
                "performance_issues": performance_issues,
                "improvement_suggestions": self._get_performance_suggestions(performance_issues),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            raise Exception(f"评估绩效表现失败: {str(e)}")

    def _get_performance_suggestions(self, issues: List[str]) -> List[str]:
        """获取绩效改进建议"""
        suggestions = []
        
        for issue in issues:
            if "胜率" in issue:
                suggestions.append("优化入场策略，提高信号质量")
            elif "盈利因子" in issue:
                suggestions.append("改进止盈止损设置，提高盈亏比")
            elif "回撤" in issue:
                suggestions.append("加强风险控制，减少单笔亏损")
        
        if not suggestions:
            suggestions.append("继续保持当前优秀表现")
        
        return suggestions

    def _emergency_procedures_check(self, account_data: Dict, market_data: Dict) -> Dict[str, Any]:
        """应急程序检查"""
        try:
            emergency_triggers = self.rules.get('emergency_procedures', {}).get('triggers', {})
            triggered_procedures = []
            
            # 检查高亏损触发
            daily_pnl = account_data.get('daily_pnl', 0)
            account_balance = account_data.get('balance', 1)
            if daily_pnl < 0:
                loss_ratio = abs(daily_pnl) / account_balance
                if loss_ratio >= emergency_triggers.get('high_loss', 0.05):
                    triggered_procedures.append("high_loss")
            
            # 检查低保证金触发
            margin_ratio = account_data.get('margin_ratio', 1.0)
            if margin_ratio <= emergency_triggers.get('low_margin', 0.3):
                triggered_procedures.append("low_margin")
            
            # 检查市场暴跌触发
            market_change = market_data.get('price_change_24h', 0)
            if market_change <= -emergency_triggers.get('market_crash', 0.1):
                triggered_procedures.append("market_crash")
            
            # 获取应急措施
            emergency_actions = []
            if triggered_procedures:
                immediate_actions = self.rules.get('emergency_procedures', {}).get('actions', {}).get('immediate', [])
                escalated_actions = self.rules.get('emergency_procedures', {}).get('actions', {}).get('escalated', [])
                
                emergency_actions.extend(immediate_actions)
                if len(triggered_procedures) > 1:  # 多个触发条件时升级措施
                    emergency_actions.extend(escalated_actions)
            
            return {
                "status": "success",
                "emergency_triggered": len(triggered_procedures) > 0,
                "triggered_procedures": triggered_procedures,
                "emergency_actions": emergency_actions,
                "severity": "critical" if len(triggered_procedures) > 1 else "high" if triggered_procedures else "normal",
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            raise Exception(f"应急程序检查失败: {str(e)}")


if __name__ == '__main__':
    # 测试交易准则引擎
    engine = TradingRulesEngine()
    
    # 测试交易验证
    trade_data = {
        "action": "open_long",
        "size": 0.1,
        "price": 50000,
        "stop_loss": 49000,
        "take_profit": 51000,
        "signal_strength": 0.8,
        "confidence": 0.75,
        "leverage": 3
    }
    
    account_data = {
        "balance": 10000,
        "position_value": 1000,
        "margin_ratio": 0.6,
        "daily_pnl": -200,
        "consecutive_losses": 1
    }
    
    market_data = {
        "depth": 150000,
        "volatility": 0.03,
        "price_change_24h": -0.02
    }
    
    result = engine.run(
        action="validate_trade",
        trade_data=trade_data,
        account_data=account_data,
        market_data=market_data
    )
    
    print("交易验证结果:", json.dumps(result, indent=2, ensure_ascii=False))
