#!/usr/bin/env python3
"""
专业市场分析工具 - 基于专业交易员思维的市场分析
"""

from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from pydantic import BaseModel, Field
from crewai_tools import BaseTool
import numpy as np
import pandas as pd
from dataclasses import dataclass


@dataclass
class MarketState:
    """市场状态数据类"""
    trend_direction: str  # 'bullish', 'bearish', 'sideways'
    trend_strength: float  # 0-1
    volatility_regime: str  # 'low', 'normal', 'high'
    volatility_percentile: float  # 0-1
    liquidity_score: float  # 0-1
    market_phase: str  # 'trending', 'ranging', 'transitional'


@dataclass
class TradingOpportunity:
    """交易机会数据类"""
    signal_type: str  # 'breakout', 'pullback', 'reversal'
    confidence: float  # 0-1
    timeframe_confluence: bool
    volume_confirmation: bool
    key_level_break: bool
    risk_reward_ratio: float
    optimal_entry: float
    stop_loss: float
    take_profit: float


class ProfessionalMarketAnalyzerSchema(BaseModel):
    """专业市场分析工具的输入参数模式"""
    action: str = Field(..., description="操作类型：analyze_opportunity, assess_risk, detect_regime")
    trading_pair: str = Field("BTC", description="交易对")
    timeframes: Optional[List[str]] = Field(['5m', '15m', '1h', '4h'], description="分析时间框架")
    lookback_periods: Optional[int] = Field(100, description="回看周期数")


class ProfessionalMarketAnalyzer(BaseTool):
    """专业市场分析工具"""
    name: str = "ProfessionalMarketAnalyzer"
    description: str = """
    这是一个基于专业交易员思维的高级市场分析工具。
    主要功能：
    1. 多时间框架趋势分析
    2. 交易机会识别和验证
    3. 市场环境和制度检测
    4. 动态风险评估
    5. 最优入场时机判断
    
    使用示例：
    1. 分析交易机会：
       tool.run(action="analyze_opportunity", trading_pair="BTC")
    
    2. 评估市场风险：
       tool.run(action="assess_risk", trading_pair="BTC")
    
    3. 检测市场制度：
       tool.run(action="detect_regime", trading_pair="BTC")
    """
    args_schema: type[BaseModel] = ProfessionalMarketAnalyzerSchema

    def __init__(self, **kwargs):
        """初始化专业市场分析工具"""
        super().__init__(**kwargs)
        self.price_data = {}  # 缓存价格数据
        self.indicators = {}  # 缓存技术指标

    def _run(self, **kwargs: Any) -> Dict[str, Any]:
        """执行市场分析"""
        try:
            action = kwargs.get('action', 'analyze_opportunity')
            trading_pair = kwargs.get('trading_pair', 'BTC')
            
            if action == 'analyze_opportunity':
                return self._analyze_trading_opportunity(kwargs)
            elif action == 'assess_risk':
                return self._assess_market_risk(kwargs)
            elif action == 'detect_regime':
                return self._detect_market_regime(kwargs)
            else:
                return {
                    "status": "error",
                    "message": f"不支持的操作类型: {action}",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
        except Exception as e:
            return {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

    def _analyze_trading_opportunity(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """分析交易机会"""
        try:
            trading_pair = params.get('trading_pair', 'BTC')
            timeframes = params.get('timeframes', ['5m', '15m', '1h', '4h'])
            
            # 1. 多时间框架分析
            timeframe_analysis = self._multi_timeframe_analysis(trading_pair, timeframes)
            
            # 2. 关键位分析
            key_levels = self._identify_key_levels(trading_pair)
            
            # 3. 成交量分析
            volume_analysis = self._analyze_volume_profile(trading_pair)
            
            # 4. 市场结构分析
            market_structure = self._analyze_market_structure(trading_pair)
            
            # 5. 综合机会评估
            opportunity = self._evaluate_trading_opportunity(
                timeframe_analysis, key_levels, volume_analysis, market_structure
            )
            
            return {
                "status": "success",
                "trading_pair": trading_pair,
                "opportunity": opportunity.__dict__ if opportunity else None,
                "timeframe_analysis": timeframe_analysis,
                "key_levels": key_levels,
                "volume_analysis": volume_analysis,
                "market_structure": market_structure,
                "recommendation": self._generate_trading_recommendation(opportunity),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            raise Exception(f"分析交易机会失败: {str(e)}")

    def _multi_timeframe_analysis(self, trading_pair: str, timeframes: List[str]) -> Dict[str, Any]:
        """多时间框架分析"""
        analysis = {}
        
        for tf in timeframes:
            # 模拟获取不同时间框架的数据
            tf_data = self._get_timeframe_data(trading_pair, tf)
            
            analysis[tf] = {
                'trend_direction': self._calculate_trend_direction(tf_data),
                'trend_strength': self._calculate_trend_strength(tf_data),
                'support_resistance': self._find_support_resistance(tf_data),
                'momentum': self._calculate_momentum(tf_data)
            }
        
        # 检查时间框架一致性
        confluence_score = self._calculate_timeframe_confluence(analysis)
        
        return {
            'individual_analysis': analysis,
            'confluence_score': confluence_score,
            'confluence_rating': 'strong' if confluence_score > 0.7 else 'moderate' if confluence_score > 0.4 else 'weak'
        }

    def _identify_key_levels(self, trading_pair: str) -> Dict[str, Any]:
        """识别关键支撑阻力位"""
        # 模拟价格数据
        current_price = 50000.0  # 当前价格
        
        # 计算关键位
        support_levels = [
            current_price * 0.98,  # 近期支撑
            current_price * 0.95,  # 强支撑
            current_price * 0.92   # 主要支撑
        ]
        
        resistance_levels = [
            current_price * 1.02,  # 近期阻力
            current_price * 1.05,  # 强阻力
            current_price * 1.08   # 主要阻力
        ]
        
        return {
            'current_price': current_price,
            'support_levels': support_levels,
            'resistance_levels': resistance_levels,
            'nearest_support': min(support_levels, key=lambda x: abs(x - current_price)),
            'nearest_resistance': min(resistance_levels, key=lambda x: abs(x - current_price)),
            'key_level_distance': self._calculate_key_level_distance(current_price, support_levels + resistance_levels)
        }

    def _analyze_volume_profile(self, trading_pair: str) -> Dict[str, Any]:
        """分析成交量特征"""
        # 模拟成交量数据
        recent_volume = np.random.normal(1000000, 200000, 20)
        avg_volume = np.mean(recent_volume)
        current_volume = recent_volume[-1]
        
        volume_surge = current_volume > avg_volume * 1.5
        volume_dry_up = current_volume < avg_volume * 0.5
        
        return {
            'current_volume': current_volume,
            'average_volume': avg_volume,
            'volume_ratio': current_volume / avg_volume,
            'volume_surge': volume_surge,
            'volume_dry_up': volume_dry_up,
            'volume_trend': 'increasing' if current_volume > avg_volume else 'decreasing',
            'volume_confirmation': volume_surge  # 成交量确认信号
        }

    def _analyze_market_structure(self, trading_pair: str) -> Dict[str, Any]:
        """分析市场结构"""
        # 模拟市场结构分析
        return {
            'higher_highs': True,
            'higher_lows': True,
            'structure_break': False,
            'trend_continuation': True,
            'reversal_signals': [],
            'structure_strength': 0.8
        }

    def _evaluate_trading_opportunity(self, timeframe_analysis, key_levels, volume_analysis, market_structure) -> Optional[TradingOpportunity]:
        """评估交易机会"""
        # 检查时间框架一致性
        timeframe_confluence = timeframe_analysis['confluence_score'] > 0.6
        
        # 检查成交量确认
        volume_confirmation = volume_analysis['volume_confirmation']
        
        # 检查关键位突破
        current_price = key_levels['current_price']
        nearest_resistance = key_levels['nearest_resistance']
        key_level_break = current_price > nearest_resistance * 1.001  # 突破确认
        
        # 计算置信度
        confidence = 0.0
        if timeframe_confluence:
            confidence += 0.4
        if volume_confirmation:
            confidence += 0.3
        if key_level_break:
            confidence += 0.3
        
        # 只有置信度足够高才返回交易机会
        if confidence >= 0.6:
            return TradingOpportunity(
                signal_type='breakout' if key_level_break else 'pullback',
                confidence=confidence,
                timeframe_confluence=timeframe_confluence,
                volume_confirmation=volume_confirmation,
                key_level_break=key_level_break,
                risk_reward_ratio=self._calculate_risk_reward_ratio(current_price, key_levels),
                optimal_entry=current_price,
                stop_loss=key_levels['nearest_support'],
                take_profit=current_price * 1.02
            )
        
        return None

    def _assess_market_risk(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """评估市场风险"""
        try:
            trading_pair = params.get('trading_pair', 'BTC')
            
            # 1. 波动率风险
            volatility_risk = self._assess_volatility_risk(trading_pair)
            
            # 2. 流动性风险
            liquidity_risk = self._assess_liquidity_risk(trading_pair)
            
            # 3. 相关性风险
            correlation_risk = self._assess_correlation_risk(trading_pair)
            
            # 4. 尾部风险
            tail_risk = self._assess_tail_risk(trading_pair)
            
            # 5. 综合风险评级
            overall_risk = self._calculate_overall_risk(volatility_risk, liquidity_risk, correlation_risk, tail_risk)
            
            return {
                "status": "success",
                "trading_pair": trading_pair,
                "risk_assessment": {
                    "volatility_risk": volatility_risk,
                    "liquidity_risk": liquidity_risk,
                    "correlation_risk": correlation_risk,
                    "tail_risk": tail_risk,
                    "overall_risk": overall_risk
                },
                "risk_alerts": self._generate_risk_alerts(overall_risk),
                "recommended_actions": self._recommend_risk_actions(overall_risk),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            raise Exception(f"评估市场风险失败: {str(e)}")

    def _detect_market_regime(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """检测市场制度"""
        try:
            trading_pair = params.get('trading_pair', 'BTC')
            
            # 模拟市场制度检测
            market_state = MarketState(
                trend_direction='bullish',
                trend_strength=0.7,
                volatility_regime='normal',
                volatility_percentile=0.6,
                liquidity_score=0.8,
                market_phase='trending'
            )
            
            # 策略建议
            strategy_recommendations = self._get_strategy_for_regime(market_state)
            
            return {
                "status": "success",
                "trading_pair": trading_pair,
                "market_state": market_state.__dict__,
                "regime_description": self._describe_market_regime(market_state),
                "strategy_recommendations": strategy_recommendations,
                "parameter_adjustments": self._suggest_parameter_adjustments(market_state),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            raise Exception(f"检测市场制度失败: {str(e)}")

    # 辅助方法
    def _get_timeframe_data(self, trading_pair: str, timeframe: str) -> List[float]:
        """获取时间框架数据（模拟）"""
        # 模拟价格数据
        base_price = 50000.0
        return [base_price + np.random.normal(0, 500) for _ in range(100)]

    def _calculate_trend_direction(self, data: List[float]) -> str:
        """计算趋势方向"""
        if len(data) < 2:
            return 'neutral'
        return 'bullish' if data[-1] > data[0] else 'bearish'

    def _calculate_trend_strength(self, data: List[float]) -> float:
        """计算趋势强度"""
        if len(data) < 2:
            return 0.0
        return min(abs(data[-1] - data[0]) / data[0] * 10, 1.0)

    def _find_support_resistance(self, data: List[float]) -> Dict[str, float]:
        """寻找支撑阻力位"""
        return {
            'support': min(data[-20:]) if len(data) >= 20 else min(data),
            'resistance': max(data[-20:]) if len(data) >= 20 else max(data)
        }

    def _calculate_momentum(self, data: List[float]) -> float:
        """计算动量"""
        if len(data) < 10:
            return 0.0
        return (data[-1] - data[-10]) / data[-10]

    def _calculate_timeframe_confluence(self, analysis: Dict[str, Any]) -> float:
        """计算时间框架一致性"""
        directions = [tf_data['trend_direction'] for tf_data in analysis.values()]
        bullish_count = directions.count('bullish')
        bearish_count = directions.count('bearish')
        total_count = len(directions)
        
        return max(bullish_count, bearish_count) / total_count

    def _calculate_key_level_distance(self, current_price: float, levels: List[float]) -> float:
        """计算到关键位的距离"""
        nearest_level = min(levels, key=lambda x: abs(x - current_price))
        return abs(current_price - nearest_level) / current_price

    def _calculate_risk_reward_ratio(self, current_price: float, key_levels: Dict[str, Any]) -> float:
        """计算风险收益比"""
        stop_loss = key_levels['nearest_support']
        take_profit = current_price * 1.02
        
        risk = abs(current_price - stop_loss)
        reward = abs(take_profit - current_price)
        
        return reward / risk if risk > 0 else 0

    def _generate_trading_recommendation(self, opportunity: Optional[TradingOpportunity]) -> str:
        """生成交易建议"""
        if not opportunity:
            return "当前市场条件不满足交易标准，建议观望"
        
        if opportunity.confidence > 0.8:
            return f"高质量{opportunity.signal_type}机会，建议积极参与"
        elif opportunity.confidence > 0.6:
            return f"中等质量{opportunity.signal_type}机会，可以小仓位参与"
        else:
            return "信号质量一般，建议谨慎观望"

    def _assess_volatility_risk(self, trading_pair: str) -> Dict[str, Any]:
        """评估波动率风险"""
        return {
            'current_volatility': 0.15,
            'volatility_percentile': 0.6,
            'risk_level': 'moderate',
            'recommendation': '正常仓位'
        }

    def _assess_liquidity_risk(self, trading_pair: str) -> Dict[str, Any]:
        """评估流动性风险"""
        return {
            'liquidity_score': 0.8,
            'bid_ask_spread': 0.001,
            'market_depth': 'good',
            'risk_level': 'low'
        }

    def _assess_correlation_risk(self, trading_pair: str) -> Dict[str, Any]:
        """评估相关性风险"""
        return {
            'correlation_with_btc': 0.7,
            'correlation_risk': 'moderate',
            'diversification_benefit': 'limited'
        }

    def _assess_tail_risk(self, trading_pair: str) -> Dict[str, Any]:
        """评估尾部风险"""
        return {
            'var_95': 0.03,
            'cvar_95': 0.05,
            'tail_risk': 'moderate',
            'extreme_scenario_probability': 0.05
        }

    def _calculate_overall_risk(self, volatility_risk, liquidity_risk, correlation_risk, tail_risk) -> Dict[str, Any]:
        """计算综合风险"""
        return {
            'risk_score': 0.6,
            'risk_level': 'moderate',
            'max_position_size': 0.02,
            'recommended_leverage': 3
        }

    def _generate_risk_alerts(self, overall_risk: Dict[str, Any]) -> List[str]:
        """生成风险警报"""
        alerts = []
        if overall_risk['risk_score'] > 0.7:
            alerts.append("市场风险较高，建议减少仓位")
        if overall_risk['risk_score'] > 0.8:
            alerts.append("市场风险很高，建议暂停交易")
        return alerts

    def _recommend_risk_actions(self, overall_risk: Dict[str, Any]) -> List[str]:
        """推荐风险应对行动"""
        actions = []
        risk_level = overall_risk['risk_level']
        
        if risk_level == 'high':
            actions.extend([
                "降低杠杆至2倍以下",
                "减少单笔交易仓位至1%",
                "增加止损频率"
            ])
        elif risk_level == 'moderate':
            actions.extend([
                "保持正常杠杆3倍",
                "单笔交易仓位控制在2%",
                "密切监控市场变化"
            ])
        
        return actions

    def _get_strategy_for_regime(self, market_state: MarketState) -> Dict[str, Any]:
        """根据市场制度推荐策略"""
        if market_state.market_phase == 'trending':
            return {
                'primary_strategy': 'trend_following',
                'signal_threshold': 0.7,
                'stop_loss_distance': 0.02,
                'take_profit_ratio': 2.0
            }
        elif market_state.market_phase == 'ranging':
            return {
                'primary_strategy': 'mean_reversion',
                'signal_threshold': 0.8,
                'stop_loss_distance': 0.015,
                'take_profit_ratio': 1.5
            }
        else:
            return {
                'primary_strategy': 'conservative',
                'signal_threshold': 0.9,
                'stop_loss_distance': 0.01,
                'take_profit_ratio': 1.0
            }

    def _describe_market_regime(self, market_state: MarketState) -> str:
        """描述市场制度"""
        return f"当前市场处于{market_state.trend_direction}趋势，" \
               f"趋势强度{market_state.trend_strength:.1%}，" \
               f"波动率{market_state.volatility_regime}，" \
               f"市场阶段为{market_state.market_phase}"

    def _suggest_parameter_adjustments(self, market_state: MarketState) -> Dict[str, Any]:
        """建议参数调整"""
        adjustments = {}
        
        if market_state.volatility_regime == 'high':
            adjustments['position_size_multiplier'] = 0.7
            adjustments['stop_loss_tighter'] = True
        elif market_state.volatility_regime == 'low':
            adjustments['position_size_multiplier'] = 1.2
            adjustments['take_profit_wider'] = True
        
        return adjustments


if __name__ == '__main__':
    # 测试专业市场分析工具
    analyzer = ProfessionalMarketAnalyzer()
    
    # 测试交易机会分析
    opportunity_result = analyzer.run(action="analyze_opportunity", trading_pair="BTC")
    print("交易机会分析:", opportunity_result)
    
    # 测试风险评估
    risk_result = analyzer.run(action="assess_risk", trading_pair="BTC")
    print("风险评估:", risk_result)
    
    # 测试市场制度检测
    regime_result = analyzer.run(action="detect_regime", trading_pair="BTC")
    print("市场制度检测:", regime_result)
