import os
import sys
import logging
from logging.handlers import TimedRotatingFileHandler
import sqlite3
import json
import requests
from datetime import datetime, timedelta
from collections import deque, defaultdict
from pybitget.stream import BitgetWsClient, SubscribeReq, handel_error
from pybitget.enums import *
from pybitget import logger

# 配置日志
def setup_logger():
    """配置日志记录器"""
    # 创建logs目录（如果不存在）
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 创建日志记录器
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    
    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 创建文件处理器（按天切割）
    log_file = os.path.join(log_dir, 'price_monitor.log')
    file_handler = TimedRotatingFileHandler(
        filename=log_file,
        when='midnight',  # 每天午夜切割
        interval=1,       # 间隔为1天
        backupCount=30,   # 保留30天的日志
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    return logger

# 初始化日志记录器
logger = setup_logger()

# 企业微信机器人配置
WECHAT_ROBOT_URL = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=22f9bc8d-bc61-45f2-beac-d996dbe425d3'

# API配置
API_CONFIG = {
    'api_key': 'bg_44025411ed4ba2c0032ac4e2dff2b18e',
    'api_secret': 'd30fa4f8e6d80462659ed7e525d489020413a4e9d11cd16a3c8f6ceeccd87500',
    'passphrase': '1137285095',
    'environment': 'prod'  # 'prod' 或 'test'
}

# 交易对配置
TRADING_PAIRS = ['BTC']  # 需要监控的交易对列表

# 价格监控配置
PRICE_MONITOR_CONFIG = {
    'medium_term': {
        'threshold': 0.02  # 价格变化阈值
    }
}

def send_wechat_message(trading_pair: str, price: float, price_change: float, volatility: float, levels: dict):
    """发送企业微信消息"""
    try:
        message = {
            "msgtype": "markdown",
            "markdown": {
                "content": (
                    f"# 价格监控告警 ({datetime.now().strftime('%Y-%m-%d %H:%M:%S')})\n"
                    f"> **交易对**: {trading_pair}\n"
                    f"> **当前价格**: {price:.2f}\n"
                    f"> **价格变化**: <font color=\"{'info' if price_change >= 0 else 'warning'}\">{price_change:.2%}</font>\n"
                    f"> **波动率**: <font color=\"{'warning' if volatility > 0.02 else 'info'}\">{volatility:.2%}</font>\n"
                    f"> **支撑位**: {', '.join([f'{level:.2f}' for level in levels['support_levels']])}\n"
                    f"> **压力位**: {', '.join([f'{level:.2f}' for level in levels['resistance_levels']])}\n"
                    f"> **MA5**: {levels['ma_values']['ma5']:.2f}\n"
                    f"> **MA20**: {levels['ma_values']['ma20']:.2f}"
                )
            }
        }
        
        headers = {'Content-Type': 'application/json'}
        response = requests.post(WECHAT_ROBOT_URL, headers=headers, data=json.dumps(message))
        if response.status_code == 200:
            logger.info(f"企业微信消息发送成功: {trading_pair}")
        else:
            logger.error(f"企业微信消息发送失败: {response.text}")
    except Exception as e:
        logger.error(f"发送企业微信消息失败: {str(e)}")

class DatabaseManager:
    def __init__(self, db_path: str = 'price_data.db'):
        self.db_path = db_path
        # 设置各表的最大数据量
        self.max_records = {
            'price_data': 100000,    # 价格数据最大记录数
            'price_levels': 10000,   # 价格水平数据最大记录数
            'kline_data': 100000     # K线数据最大记录数
        }
        self._init_db()

    def _init_db(self):
        """初始化数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                # 创建价格数据表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS price_data (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        trading_pair TEXT NOT NULL,
                        price REAL NOT NULL,
                        timestamp DATETIME NOT NULL,
                        volume REAL,
                        high REAL,
                        low REAL
                    )
                ''')
                # 创建价格水平表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS price_levels (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        trading_pair TEXT NOT NULL,
                        support_level REAL,
                        resistance_level REAL,
                        ma5 REAL,
                        ma10 REAL,
                        ma20 REAL,
                        ma50 REAL,
                        timestamp DATETIME NOT NULL
                    )
                ''')
                # 创建K线数据表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS kline_data (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        trading_pair TEXT NOT NULL,
                        open_price REAL NOT NULL,
                        high_price REAL NOT NULL,
                        low_price REAL NOT NULL,
                        close_price REAL NOT NULL,
                        volume REAL NOT NULL,
                        timestamp DATETIME NOT NULL,
                        UNIQUE(trading_pair, timestamp)
                    )
                ''')
                conn.commit()
                logger.info("数据库初始化成功")
        except Exception as e:
            logger.error(f"数据库初始化失败: {str(e)}")
            raise

    def _cleanup_old_data(self, table_name: str):
        """清理旧数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                # 获取当前记录数
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                
                # 如果超过最大记录数，删除最旧的数据
                if count > self.max_records[table_name]:
                    # 计算需要删除的记录数
                    delete_count = count - self.max_records[table_name]
                    # 删除最旧的记录
                    cursor.execute(f"""
                        DELETE FROM {table_name}
                        WHERE id IN (
                            SELECT id FROM {table_name}
                            ORDER BY timestamp ASC
                            LIMIT ?
                        )
                    """, (delete_count,))
                    conn.commit()
                    logger.info(f"已清理 {table_name} 表中的 {delete_count} 条旧数据")
        except Exception as e:
            logger.error(f"清理 {table_name} 表数据失败: {str(e)}")

    def save_price_data(self, trading_pair: str, price: float, timestamp: datetime, volume: float = None, high: float = None, low: float = None):
        """保存价格数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO price_data (trading_pair, price, timestamp, volume, high, low)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (trading_pair, price, timestamp, volume, high, low))
                conn.commit()
                # 清理旧数据
                self._cleanup_old_data('price_data')
        except Exception as e:
            logger.error(f"保存价格数据失败: {str(e)}")

    def save_price_levels(self, trading_pair: str, support_level: float, resistance_level: float, 
                         ma5: float, ma10: float, ma20: float, ma50: float, timestamp: datetime):
        """保存价格水平数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO price_levels (trading_pair, support_level, resistance_level, 
                                           ma5, ma10, ma20, ma50, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (trading_pair, support_level, resistance_level, ma5, ma10, ma20, ma50, timestamp))
                conn.commit()
                # 清理旧数据
                self._cleanup_old_data('price_levels')
        except Exception as e:
            logger.error(f"保存价格水平数据失败: {str(e)}")

    def save_kline_data(self, trading_pair: str, open_price: float, high_price: float, low_price: float, 
                       close_price: float, volume: float, timestamp: datetime):
        """保存K线数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO kline_data 
                    (trading_pair, open_price, high_price, low_price, close_price, volume, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (trading_pair, open_price, high_price, low_price, close_price, volume, timestamp))
                conn.commit()
                # 清理旧数据
                self._cleanup_old_data('kline_data')
        except Exception as e:
            logger.error(f"保存K线数据失败: {str(e)}")

    def get_table_size(self, table_name: str) -> int:
        """获取表的数据量"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                return cursor.fetchone()[0]
        except Exception as e:
            logger.error(f"获取表 {table_name} 数据量失败: {str(e)}")
            return 0

    def get_database_size(self) -> dict:
        """获取数据库各表的数据量"""
        return {
            'price_data': self.get_table_size('price_data'),
            'price_levels': self.get_table_size('price_levels'),
            'kline_data': self.get_table_size('kline_data')
        }

    def get_recent_prices(self, trading_pair: str, minutes: int = 60) -> list:
        """获取最近的价格数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT price, timestamp FROM price_data
                    WHERE trading_pair = ? AND timestamp >= datetime('now', ?)
                    ORDER BY timestamp DESC
                ''', (trading_pair, f'-{minutes} minutes'))
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"获取价格数据失败: {str(e)}")
            return []

    def get_recent_klines(self, trading_pair: str, minutes: int = 60) -> list:
        """获取最近的K线数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT open_price, high_price, low_price, close_price, volume, timestamp 
                    FROM kline_data
                    WHERE trading_pair = ? AND timestamp >= datetime('now', ?)
                    ORDER BY timestamp DESC
                ''', (trading_pair, f'-{minutes} minutes'))
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"获取K线数据失败: {str(e)}")
            return []

class PriceLevels:
    def __init__(self, window_size: int = 100):
        self.window_size = window_size
        self.prices = deque(maxlen=window_size)
        self.support_levels = []  # 支撑位列表
        self.resistance_levels = []  # 压力位列表
        self.current_high = float('-inf')
        self.current_low = float('inf')
        self.last_update_time = None
        self.time_window = timedelta(minutes=15)  # 15分钟时间窗口
        
        # 价格区间统计
        self.price_ranges = {
            '1m': {'high': float('-inf'), 'low': float('inf')},
            '5m': {'high': float('-inf'), 'low': float('inf')},
            '15m': {'high': float('-inf'), 'low': float('inf')},
            '1h': {'high': float('-inf'), 'low': float('inf')}
        }
        
        # 移动平均线
        self.ma_values = {
            'ma5': deque(maxlen=5),
            'ma10': deque(maxlen=10),
            'ma20': deque(maxlen=20),
            'ma50': deque(maxlen=50)
        }
        
        # 波动率统计
        self.volatility = {
            '1m': deque(maxlen=60),
            '5m': deque(maxlen=12),
            '15m': deque(maxlen=4),
            '1h': deque(maxlen=1)
        }

    def update(self, price: float, timestamp: datetime):
        """更新价格数据"""
        self.prices.append(price)
        
        # 更新时间窗口内的最高最低点
        if self.last_update_time is None or timestamp - self.last_update_time >= self.time_window:
            self.current_high = float('-inf')
            self.current_low = float('inf')
            self.last_update_time = timestamp
        
        self.current_high = max(self.current_high, price)
        self.current_low = min(self.current_low, price)
        
        # 更新移动平均线
        for ma_name, ma_queue in self.ma_values.items():
            ma_queue.append(price)
        
        # 更新价格区间
        self._update_price_ranges(price, timestamp)
        
        # 更新波动率
        self._update_volatility(price, timestamp)
        
        # 更新支撑位和压力位
        self._update_levels()

    def _update_price_ranges(self, price: float, timestamp: datetime):
        """更新不同时间周期的价格区间"""
        for period in self.price_ranges:
            if self._is_new_period(timestamp, period):
                self.price_ranges[period]['high'] = price
                self.price_ranges[period]['low'] = price
            else:
                self.price_ranges[period]['high'] = max(self.price_ranges[period]['high'], price)
                self.price_ranges[period]['low'] = min(self.price_ranges[period]['low'], price)

    def _update_volatility(self, price: float, timestamp: datetime):
        """更新不同时间周期的波动率"""
        if len(self.prices) < 2:
            return
            
        current_volatility = abs((price - self.prices[-2]) / self.prices[-2])
        
        for period in self.volatility:
            if self._is_new_period(timestamp, period):
                self.volatility[period].append(current_volatility)
            else:
                self.volatility[period][-1] = current_volatility

    def _is_new_period(self, timestamp: datetime, period: str) -> bool:
        """判断是否进入新的时间周期"""
        if self.last_update_time is None:
            return True
            
        if period == '1m':
            return timestamp.minute != self.last_update_time.minute
        elif period == '5m':
            return timestamp.minute % 5 == 0 and timestamp.minute != self.last_update_time.minute
        elif period == '15m':
            return timestamp.minute % 15 == 0 and timestamp.minute != self.last_update_time.minute
        elif period == '1h':
            return timestamp.hour != self.last_update_time.hour
        return False

    def _update_levels(self):
        """更新支撑位和压力位"""
        if len(self.prices) < 20:
            return

        prices = list(self.prices)
        
        # 计算移动平均线
        ma_values = {
            'ma5': sum(list(self.ma_values['ma5'])) / len(self.ma_values['ma5']) if self.ma_values['ma5'] else None,
            'ma10': sum(list(self.ma_values['ma10'])) / len(self.ma_values['ma10']) if self.ma_values['ma10'] else None,
            'ma20': sum(list(self.ma_values['ma20'])) / len(self.ma_values['ma20']) if self.ma_values['ma20'] else None,
            'ma50': sum(list(self.ma_values['ma50'])) / len(self.ma_values['ma50']) if self.ma_values['ma50'] else None
        }
        
        # 更新支撑位
        self.support_levels = [
            min([v for v in ma_values.values() if v is not None]),  # 均线支撑
            min(prices[-20:]),  # 近期最低点
            self.price_ranges['15m']['low']  # 15分钟周期最低点
        ]
        
        # 更新压力位
        self.resistance_levels = [
            max([v for v in ma_values.values() if v is not None]),  # 均线压力
            max(prices[-20:]),  # 近期最高点
            self.price_ranges['15m']['high']  # 15分钟周期最高点
        ]

    def get_levels(self) -> dict:
        """获取所有价格水平"""
        return {
            'support_levels': self.support_levels,
            'resistance_levels': self.resistance_levels,
            'current_high': self.current_high,
            'current_low': self.current_low,
            'price_ranges': self.price_ranges,
            'ma_values': {k: sum(v)/len(v) if v else None for k, v in self.ma_values.items()},
            'volatility': {k: sum(v)/len(v) if v else 0 for k, v in self.volatility.items()},
            'time_window': self.time_window.total_seconds() / 60
        }

class KlineData:
    def __init__(self):
        self.open = None
        self.high = None
        self.low = None
        self.close = None
        self.volume = None
        self.timestamp = None

    def update(self, data: list):
        """更新K线数据
        data格式: [timestamp, open, high, low, close, volume]
        所有值都是字符串格式
        """
        try:
            # 确保数据是列表格式
            if not isinstance(data, list):
                raise ValueError(f"数据必须是列表格式，当前格式: {type(data)}")
            
            # 记录原始数据
            logger.debug(f"正在处理K线数据: {data}")
            
            # 解析数据
            self.timestamp = datetime.fromtimestamp(int(data[0]) / 1000)
            self.open = float(data[1])
            self.high = float(data[2])
            self.low = float(data[3])
            self.close = float(data[4])
            self.volume = float(data[5])
            
            logger.debug(f"K线数据更新成功 - 时间: {self.timestamp}, 开盘价: {self.open}, 收盘价: {self.close}")
        except Exception as e:
            logger.error(f"更新K线数据失败: {str(e)}, 数据: {data}")
            logger.exception("K线数据更新详细错误：")
            raise

    def to_dict(self):
        """转换为字典格式"""
        return {
            'open': self.open,
            'high': self.high,
            'low': self.low,
            'close': self.close,
            'volume': self.volume,
            'timestamp': self.timestamp
        }

class TechnicalIndicators:
    """技术指标计算类"""
    def __init__(self):
        self.indicators = {}
        
    def calculate_rsi(self, prices: list, period: int = 14) -> float:
        """计算RSI指标"""
        try:
            if len(prices) < period + 1:
                return 50.0
                
            # 计算价格变化
            deltas = [prices[i] - prices[i-1] for i in range(1, len(prices))]
            
            # 分离上涨和下跌
            gains = [delta if delta > 0 else 0 for delta in deltas]
            losses = [-delta if delta < 0 else 0 for delta in deltas]
            
            # 计算平均上涨和下跌
            avg_gain = sum(gains[-period:]) / period
            avg_loss = sum(losses[-period:]) / period
            
            if avg_loss == 0:
                return 100.0
                
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            
            return rsi
        except Exception as e:
            logger.error(f"计算RSI失败: {str(e)}")
            return 50.0
            
    def calculate_macd(self, prices: list) -> dict:
        """计算MACD指标"""
        try:
            if len(prices) < 26:
                return {'macd': 0, 'signal': 0, 'histogram': 0}
                
            # 计算EMA
            ema12 = self._calculate_ema(prices, 12)
            ema26 = self._calculate_ema(prices, 26)
            
            # 计算MACD线
            macd_line = ema12 - ema26
            
            # 计算信号线
            signal_line = self._calculate_ema([macd_line], 9)
            
            # 计算柱状图
            histogram = macd_line - signal_line
            
            return {
                'macd': macd_line,
                'signal': signal_line,
                'histogram': histogram
            }
        except Exception as e:
            logger.error(f"计算MACD失败: {str(e)}")
            return {'macd': 0, 'signal': 0, 'histogram': 0}
            
    def calculate_bollinger_bands(self, prices: list, period: int = 20, std_dev: float = 2.0) -> dict:
        """计算布林带"""
        try:
            if len(prices) < period:
                return {'upper': 0, 'middle': 0, 'lower': 0}
                
            # 计算移动平均线
            sma = sum(prices[-period:]) / period
            
            # 计算标准差
            squared_diff = [(price - sma) ** 2 for price in prices[-period:]]
            std = (sum(squared_diff) / period) ** 0.5
            
            return {
                'upper': sma + (std_dev * std),
                'middle': sma,
                'lower': sma - (std_dev * std)
            }
        except Exception as e:
            logger.error(f"计算布林带失败: {str(e)}")
            return {'upper': 0, 'middle': 0, 'lower': 0}
            
    def _calculate_ema(self, prices: list, period: int) -> float:
        """计算指数移动平均线"""
        try:
            if len(prices) < period:
                return prices[-1]
                
            # 计算平滑因子
            multiplier = 2 / (period + 1)
            
            # 计算EMA
            ema = sum(prices[:period]) / period
            for price in prices[period:]:
                ema = (price - ema) * multiplier + ema
                
            return ema
        except Exception as e:
            logger.error(f"计算EMA失败: {str(e)}")
            return prices[-1] if prices else 0

class MarketAnalyzer:
    """市场分析类"""
    def __init__(self):
        self.technical_indicators = TechnicalIndicators()
        
    def analyze_trend(self, prices: list) -> dict:
        """分析价格趋势"""
        try:
            # 计算不同时间周期的趋势
            short_term = self._analyze_short_term_trend(prices)
            medium_term = self._analyze_medium_term_trend(prices)
            long_term = self._analyze_long_term_trend(prices)
            
            return {
                'short_term': short_term,
                'medium_term': medium_term,
                'long_term': long_term
            }
        except Exception as e:
            logger.error(f"分析趋势失败: {str(e)}")
            return {}
            
    def analyze_volume(self, volumes: list) -> dict:
        """分析成交量"""
        try:
            if len(volumes) < 2:
                return {}
                
            # 计算成交量趋势
            volume_trend = self._calculate_volume_trend(volumes)
            
            # 计算量比
            volume_ratio = self._calculate_volume_ratio(volumes)
            
            # 检测大单交易
            large_trades = self._detect_large_trades(volumes)
            
            return {
                'volume_trend': volume_trend,
                'volume_ratio': volume_ratio,
                'large_trades': large_trades
            }
        except Exception as e:
            logger.error(f"分析成交量失败: {str(e)}")
            return {}
            
    def analyze_technical_indicators(self, prices: list) -> dict:
        """分析技术指标"""
        try:
            # 计算RSI
            rsi = self.technical_indicators.calculate_rsi(prices)
            
            # 计算MACD
            macd = self.technical_indicators.calculate_macd(prices)
            
            # 计算布林带
            bollinger_bands = self.technical_indicators.calculate_bollinger_bands(prices)
            
            return {
                'rsi': rsi,
                'macd': macd,
                'bollinger_bands': bollinger_bands
            }
        except Exception as e:
            logger.error(f"分析技术指标失败: {str(e)}")
            return {}
            
    def analyze_market_depth(self, order_book: dict) -> dict:
        """分析市场深度"""
        try:
            # 计算买卖价差
            spread = self._calculate_spread(order_book)
            
            # 计算订单簿失衡度
            imbalance = self._calculate_order_book_imbalance(order_book)
            
            # 计算流动性评分
            liquidity_score = self._calculate_liquidity_score(order_book)
            
            return {
                'spread': spread,
                'imbalance': imbalance,
                'liquidity_score': liquidity_score
            }
        except Exception as e:
            logger.error(f"分析市场深度失败: {str(e)}")
            return {}
            
    def _analyze_short_term_trend(self, prices: list) -> dict:
        """分析短期趋势"""
        try:
            if len(prices) < 5:
                return {'trend': 'neutral', 'strength': 0}
                
            # 计算5周期移动平均线
            ma5 = sum(prices[-5:]) / 5
            
            # 计算趋势强度
            trend_strength = (prices[-1] - ma5) / ma5
            
            # 判断趋势方向
            if trend_strength > 0.001:
                trend = 'up'
            elif trend_strength < -0.001:
                trend = 'down'
            else:
                trend = 'neutral'
                
            return {
                'trend': trend,
                'strength': abs(trend_strength)
            }
        except Exception as e:
            logger.error(f"分析短期趋势失败: {str(e)}")
            return {'trend': 'neutral', 'strength': 0}
            
    def _analyze_medium_term_trend(self, prices: list) -> dict:
        """分析中期趋势"""
        try:
            if len(prices) < 20:
                return {'trend': 'neutral', 'strength': 0}
                
            # 计算20周期移动平均线
            ma20 = sum(prices[-20:]) / 20
            
            # 计算趋势强度
            trend_strength = (prices[-1] - ma20) / ma20
            
            # 判断趋势方向
            if trend_strength > 0.002:
                trend = 'up'
            elif trend_strength < -0.002:
                trend = 'down'
            else:
                trend = 'neutral'
                
            return {
                'trend': trend,
                'strength': abs(trend_strength)
            }
        except Exception as e:
            logger.error(f"分析中期趋势失败: {str(e)}")
            return {'trend': 'neutral', 'strength': 0}
            
    def _analyze_long_term_trend(self, prices: list) -> dict:
        """分析长期趋势"""
        try:
            if len(prices) < 50:
                return {'trend': 'neutral', 'strength': 0}
                
            # 计算50周期移动平均线
            ma50 = sum(prices[-50:]) / 50
            
            # 计算趋势强度
            trend_strength = (prices[-1] - ma50) / ma50
            
            # 判断趋势方向
            if trend_strength > 0.003:
                trend = 'up'
            elif trend_strength < -0.003:
                trend = 'down'
            else:
                trend = 'neutral'
                
            return {
                'trend': trend,
                'strength': abs(trend_strength)
            }
        except Exception as e:
            logger.error(f"分析长期趋势失败: {str(e)}")
            return {'trend': 'neutral', 'strength': 0}
            
    def _calculate_volume_trend(self, volumes: list) -> dict:
        """计算成交量趋势"""
        try:
            if len(volumes) < 5:
                return {'trend': 'neutral', 'strength': 0}
                
            # 计算5周期平均成交量
            avg_volume = sum(volumes[-5:]) / 5
            
            # 计算趋势强度
            trend_strength = (volumes[-1] - avg_volume) / avg_volume
            
            # 判断趋势方向
            if trend_strength > 0.2:
                trend = 'up'
            elif trend_strength < -0.2:
                trend = 'down'
            else:
                trend = 'neutral'
                
            return {
                'trend': trend,
                'strength': abs(trend_strength)
            }
        except Exception as e:
            logger.error(f"计算成交量趋势失败: {str(e)}")
            return {'trend': 'neutral', 'strength': 0}
            
    def _calculate_volume_ratio(self, volumes: list) -> float:
        """计算量比"""
        try:
            if len(volumes) < 5:
                return 1.0
                
            # 计算最近5个周期的平均成交量
            recent_avg = sum(volumes[-5:]) / 5
            
            # 计算前5个周期的平均成交量
            previous_avg = sum(volumes[-10:-5]) / 5
            
            if previous_avg == 0:
                return 1.0
                
            return recent_avg / previous_avg
        except Exception as e:
            logger.error(f"计算量比失败: {str(e)}")
            return 1.0
            
    def _detect_large_trades(self, volumes: list) -> list:
        """检测大单交易"""
        try:
            if len(volumes) < 5:
                return []
                
            # 计算平均成交量
            avg_volume = sum(volumes[-5:]) / 5
            
            # 检测大单（超过平均成交量2倍）
            large_trades = [
                {'volume': vol, 'ratio': vol/avg_volume}
                for vol in volumes[-5:]
                if vol > avg_volume * 2
            ]
            
            return large_trades
        except Exception as e:
            logger.error(f"检测大单交易失败: {str(e)}")
            return []
            
    def _calculate_spread(self, order_book: dict) -> float:
        """计算买卖价差"""
        try:
            if not order_book['bids'] or not order_book['asks']:
                return 0.0
                
            best_bid = order_book['bids'][0]['price']
            best_ask = order_book['asks'][0]['price']
            
            return (best_ask - best_bid) / best_bid
        except Exception as e:
            logger.error(f"计算买卖价差失败: {str(e)}")
            return 0.0
            
    def _calculate_order_book_imbalance(self, order_book: dict) -> float:
        """计算订单簿失衡度"""
        try:
            if not order_book['bids'] or not order_book['asks']:
                return 0.0
                
            # 计算买卖盘总量
            bid_volume = sum(level['volume'] for level in order_book['bids'])
            ask_volume = sum(level['volume'] for level in order_book['asks'])
            
            # 计算失衡度
            total_volume = bid_volume + ask_volume
            if total_volume == 0:
                return 0.0
                
            return (bid_volume - ask_volume) / total_volume
        except Exception as e:
            logger.error(f"计算订单簿失衡度失败: {str(e)}")
            return 0.0
            
    def _calculate_liquidity_score(self, order_book: dict) -> float:
        """计算流动性评分"""
        try:
            if not order_book['bids'] or not order_book['asks']:
                return 0.0
                
            # 计算买卖价差
            spread = self._calculate_spread(order_book)
            
            # 计算深度
            depth = sum(level['volume'] for level in order_book['bids'][:5]) + \
                    sum(level['volume'] for level in order_book['asks'][:5])
            
            # 计算流动性评分
            liquidity_score = (1 - spread) * (depth / 1000)  # 归一化处理
            
            return max(0, min(1, liquidity_score))  # 确保分数在0-1之间
        except Exception as e:
            logger.error(f"计算流动性评分失败: {str(e)}")
            return 0.0

class PriceMonitor:
    def __init__(self):
        logger.info("初始化价格监控系统...")
        self._price_history = {}  # 存储每个交易对的价格历史
        self._price_levels = {}   # 存储每个交易对的价格水平
        self._kline_data = {}     # 存储每个交易对的K线数据
        self._db_manager = DatabaseManager()
        self._market_analyzer = MarketAnalyzer()  # 添加市场分析器

        # 智能防重复推送管理器
        self._last_trigger_times = defaultdict(lambda: None)  # 每个交易对的最后触发时间
        self._last_trigger_prices = defaultdict(lambda: None)  # 每个交易对的最后触发价格
        self._trigger_counts = defaultdict(int)  # 每个交易对的触发计数
        self._success_rates = defaultdict(list)  # 每个交易对的成功率历史

        # 智能触发配置
        self._trigger_config = {
            'base_cooldown_minutes': 15,        # 基础冷却时间
            'min_cooldown_minutes': 3,          # 最小冷却时间
            'max_cooldown_minutes': 60,         # 最大冷却时间
            'price_similarity_threshold': 0.005, # 价格相似度阈值（0.5%）
            'adaptive_cooldown': True,          # 启用自适应冷却
            'time_window_limits': {             # 时间窗口限制
                'minute': 1,    # 1分钟内最多1次
                'hour': 3,      # 1小时内最多3次（更严格）
                'day': 12       # 1天内最多12次（更合理）
            }
        }

        self._init_ws_client()
        logger.info("价格监控系统初始化完成（已启用防重复推送机制）")

    def _init_ws_client(self):
        """初始化WebSocket客户端"""
        try:
            logger.info("开始初始化WebSocket客户端...")
            logger.info(f"API配置: key={API_CONFIG['api_key'][:8]}***, passphrase={API_CONFIG['passphrase'][:4]}***")

            # 创建WebSocket客户端
            self._ws_client = BitgetWsClient(
                api_key=API_CONFIG['api_key'],
                api_secret=API_CONFIG['api_secret'],
                passphrase=API_CONFIG['passphrase'],
                verbose=True
            ).error_listener(handel_error).build()

            # 订阅所有配置的交易对
            channels = []
            for trading_pair in TRADING_PAIRS:
                # 修改交易对符号格式
                symbol = "BTCUSDT"
                logger.info(f"准备订阅交易对: {trading_pair}, 符号: {symbol}")
                # 订阅1分钟K线频道
                channels.append(SubscribeReq("sp", "candle1m", "BTCUSDT"))
                # 初始化价格历史存储
                self._price_history[trading_pair] = deque(maxlen=1000)
                # 初始化价格水平监控
                self._price_levels[trading_pair] = PriceLevels()
                # 初始化K线数据
                self._kline_data[trading_pair] = KlineData()
                logger.info(f"已初始化 {trading_pair} 的数据存储")

            # 订阅频道
            logger.info(f"开始订阅频道: {[f'{c.inst_type}:{c.channel}:{c.inst_id}' for c in channels]}")
            self._ws_client.subscribe(channels, self._on_message)
            logger.info("WebSocket客户端初始化成功")
        except Exception as e:
            logger.error(f"初始化WebSocket客户端失败: {str(e)}")
            raise



    def _should_trigger_agent(self, trading_pair: str, current_price: float) -> bool:
        """判断是否应该触发交易Agent团队"""
        try:

            price_history = self._price_history[trading_pair]
            price_levels = self._price_levels[trading_pair]
            
            if len(price_history) < 2:
                return False

            # 1. 价格趋势分析
            trend_analysis = self._market_analyzer.analyze_trend(list(price_history))
            
            # 2. 成交量分析
            kline_data = self._kline_data[trading_pair]
            if hasattr(kline_data, 'volume') and kline_data.volume:
                volume_analysis = self._market_analyzer.analyze_volume([kline_data.volume])
            else:
                volume_analysis = self._market_analyzer.analyze_volume([])
            
            # 3. 技术指标分析
            technical_analysis = self._market_analyzer.analyze_technical_indicators(list(price_history))
            
            # 4. 市场深度分析
            depth_analysis = self._market_analyzer.analyze_market_depth(self._get_order_book(trading_pair))
            
            # 计算综合得分
            total_score = self._calculate_comprehensive_score(
                trend_analysis=trend_analysis,
                volume_analysis=volume_analysis,
                technical_analysis=technical_analysis,
                depth_analysis=depth_analysis
            )

            # 计算信号强度（基于综合得分）
            signal_strength = min(total_score / 100.0, 1.0)  # 归一化到0-1

            # 计算市场波动率
            market_volatility = self._calculate_volatility(price_history)

            # 记录详细分析结果
            self._log_analysis_results(
                trading_pair=trading_pair,
                trend_analysis=trend_analysis,
                volume_analysis=volume_analysis,
                technical_analysis=technical_analysis,
                depth_analysis=depth_analysis,
                total_score=total_score,
                signal_strength=signal_strength,
                market_volatility=market_volatility
            )

            # 根据总分决定是否触发
            should_trigger_basic = total_score >= 50  # 基础触发阈值

            logger.info(
                f"🚀计算: {trading_pair}\n"
                f"当前价格: {current_price}\n"
                f"信号强度: {signal_strength:.2%}\n"
                f"市场波动率: {market_volatility:.2%}\n"
                f"趋势分析: {trend_analysis}\n"
                f"成交量分析: {volume_analysis}\n"
                f"技术指标: {technical_analysis}\n"
                f"市场深度: {depth_analysis}\n"
                f"综合得分: {total_score}"
            )

            # 智能防重复检查
            if should_trigger_basic:
                smart_trigger_check = self._check_smart_trigger_conditions(
                    trading_pair, current_price, signal_strength, market_volatility
                )

                if not smart_trigger_check['allow']:
                    logger.info(
                        f"智能防重复机制阻止触发 - 交易对: {trading_pair}\n"
                        f"原因: {smart_trigger_check['reason']}\n"
                        f"信号强度: {signal_strength:.2%}\n"
                        f"市场波动率: {market_volatility:.2%}\n"
                        f"综合得分: {total_score}"
                    )
                    return False

                should_trigger = True
            else:
                should_trigger = False
            
            if should_trigger:
                # 记录触发信息
                self._record_trigger(trading_pair, current_price, signal_strength, total_score)

                logger.info(
                    f"🚀 触发交易Agent团队 - 交易对: {trading_pair}\n"
                    f"当前价格: {current_price}\n"
                    f"信号强度: {signal_strength:.2%}\n"
                    f"市场波动率: {market_volatility:.2%}\n"
                    f"趋势分析: {trend_analysis}\n"
                    f"成交量分析: {volume_analysis}\n"
                    f"技术指标: {technical_analysis}\n"
                    f"市场深度: {depth_analysis}\n"
                    f"综合得分: {total_score}"
                )
                return True
                
            return False
            
        except Exception as e:
            logger.error(f"判断是否触发交易Agent团队失败: {str(e)}")
            return False
            
    def _calculate_comprehensive_score(self, **analysis_results) -> float:
        """计算综合得分"""
        try:
            total_score = 0
            
            # 1. 趋势得分
            trend_analysis = analysis_results.get('trend_analysis', {})
            for timeframe, trend in trend_analysis.items():
                if trend['trend'] != 'neutral':
                    total_score += trend['strength'] * 20
                    
            # 2. 成交量得分
            volume_analysis = analysis_results.get('volume_analysis', {})
            if volume_analysis.get('volume_ratio', 1) > 1.5:
                total_score += 15
            if volume_analysis.get('large_trades'):
                total_score += 10
                
            # 3. 技术指标得分
            technical_analysis = analysis_results.get('technical_analysis', {})
            rsi = technical_analysis.get('rsi', 50)
            if rsi > 70 or rsi < 30:
                total_score += 15
                
            macd = technical_analysis.get('macd', {})
            if abs(macd.get('histogram', 0)) > 0.001:
                total_score += 10
                
            # 4. 市场深度得分
            depth_analysis = analysis_results.get('depth_analysis', {})
            if abs(depth_analysis.get('imbalance', 0)) > 0.2:
                total_score += 15
            if depth_analysis.get('liquidity_score', 0) < 0.5:
                total_score += 10
                
            return total_score
            
        except Exception as e:
            logger.error(f"计算综合得分失败: {str(e)}")
            return 0
            
    def _log_analysis_results(self, trading_pair: str, **analysis_results):
        """记录分析结果"""
        try:
            logger.info(f"交易对 {trading_pair} 分析结果:")

            # 记录信号强度和波动率
            if 'signal_strength' in analysis_results:
                logger.info(f"信号强度: {analysis_results['signal_strength']:.2%}")
            if 'market_volatility' in analysis_results:
                logger.info(f"市场波动率: {analysis_results['market_volatility']:.2%}")

            # 记录趋势分析
            if 'trend_analysis' in analysis_results:
                logger.info("趋势分析:")
                for timeframe, trend in analysis_results['trend_analysis'].items():
                    logger.info(f"  {timeframe}: {trend}")

            # 记录成交量分析
            if 'volume_analysis' in analysis_results:
                logger.info("成交量分析:")
                for metric, value in analysis_results['volume_analysis'].items():
                    logger.info(f"  {metric}: {value}")

            # 记录技术指标
            if 'technical_analysis' in analysis_results:
                logger.info("技术指标:")
                for indicator, value in analysis_results['technical_analysis'].items():
                    logger.info(f"  {indicator}: {value}")

            # 记录市场深度
            if 'depth_analysis' in analysis_results:
                logger.info("市场深度:")
                for metric, value in analysis_results['depth_analysis'].items():
                    logger.info(f"  {metric}: {value}")

            # 记录总分
            if 'total_score' in analysis_results:
                logger.info(f"综合得分: {analysis_results['total_score']}")

        except Exception as e:
            logger.error(f"记录分析结果失败: {str(e)}")

    def _check_smart_trigger_conditions(self, trading_pair: str, current_price: float,
                                       signal_strength: float, market_volatility: float) -> dict:
        """智能防重复触发条件检查"""
        try:
            # 1. 检查基础冷却时间
            cooldown_check = self._check_cooldown_time(trading_pair)
            if not cooldown_check['allow']:
                return cooldown_check

            # 2. 检查价格相似性
            similarity_check = self._check_price_similarity(trading_pair, current_price)
            if not similarity_check['allow']:
                return similarity_check

            # 3. 检查时间窗口限制
            window_check = self._check_time_window_limits(trading_pair)
            if not window_check['allow']:
                return window_check

            # 4. 自适应冷却时间检查
            if self._trigger_config['adaptive_cooldown']:
                adaptive_check = self._check_adaptive_cooldown(trading_pair, signal_strength, market_volatility)
                if not adaptive_check['allow']:
                    return adaptive_check

            return {
                'allow': True,
                'reason': '所有智能检查通过',
                'signal_strength': signal_strength,
                'market_volatility': market_volatility
            }

        except Exception as e:
            logger.error(f"智能触发条件检查失败: {str(e)}")
            return {
                'allow': False,
                'reason': f'检查异常: {str(e)}'
            }

    def _check_cooldown_time(self, trading_pair: str) -> dict:
        """检查基础冷却时间"""
        last_trigger = self._last_trigger_times[trading_pair]
        if last_trigger is None:
            return {'allow': True, 'reason': '首次触发'}

        cooldown_minutes = self._trigger_config['base_cooldown_minutes']
        elapsed = datetime.now() - last_trigger
        required_cooldown = timedelta(minutes=cooldown_minutes)

        if elapsed >= required_cooldown:
            return {'allow': True, 'reason': f'基础冷却时间({cooldown_minutes}分钟)已过'}
        else:
            remaining_seconds = int((required_cooldown - elapsed).total_seconds())
            return {
                'allow': False,
                'reason': f'基础冷却时间未到，还需等待{remaining_seconds}秒',
                'remaining_seconds': remaining_seconds
            }

    def _check_price_similarity(self, trading_pair: str, current_price: float) -> dict:
        """检查价格相似性"""
        last_price = self._last_trigger_prices[trading_pair]
        if last_price is None:
            return {'allow': True, 'reason': '无历史价格记录'}

        threshold = self._trigger_config['price_similarity_threshold']
        price_diff = abs(current_price - last_price) / last_price

        if price_diff >= threshold:
            return {'allow': True, 'reason': f'价格差异足够({price_diff:.3%} >= {threshold:.3%})'}
        else:
            return {
                'allow': False,
                'reason': f'价格过于相似，差异仅{price_diff:.3%}，阈值{threshold:.3%}',
                'price_difference': price_diff,
                'threshold': threshold
            }

    def _check_time_window_limits(self, trading_pair: str) -> dict:
        """检查时间窗口限制"""
        now = datetime.now()
        limits = self._trigger_config['time_window_limits']

        # 检查各个时间窗口
        for window_name, limit in limits.items():
            if window_name == 'minute':
                window_start = now - timedelta(minutes=1)
            elif window_name == 'hour':
                window_start = now - timedelta(hours=1)
            elif window_name == 'day':
                window_start = now - timedelta(days=1)
            else:
                continue

            # 计算窗口内的触发次数（简化实现，基于触发计数）
            # 实际应用中可以维护详细的触发历史
            recent_count = self._estimate_recent_triggers(trading_pair, window_name)

            if recent_count >= limit:
                return {
                    'allow': False,
                    'reason': f'{window_name}时间窗口内触发次数已达上限({recent_count}/{limit})',
                    'window': window_name,
                    'count': recent_count,
                    'limit': limit
                }

        return {'allow': True, 'reason': '时间窗口检查通过'}

    def _estimate_recent_triggers(self, trading_pair: str, window_name: str) -> int:
        """改进的最近触发次数估算"""
        last_trigger = self._last_trigger_times[trading_pair]
        if last_trigger is None:
            return 0

        now = datetime.now()
        elapsed = now - last_trigger

        if window_name == 'minute':
            # 1分钟内：如果最后触发在1分钟内，返回1，否则返回0
            return 1 if elapsed < timedelta(minutes=1) else 0
        elif window_name == 'hour':
            # 1小时内：基于触发频率估算，但不超过实际计数
            if elapsed < timedelta(hours=1):
                return min(self._trigger_counts[trading_pair], 3)  # 更严格的限制
            return 0
        elif window_name == 'day':
            # 1天内：如果最后触发在1天内，返回总计数
            if elapsed < timedelta(days=1):
                return self._trigger_counts[trading_pair]
            return 0

        return 0

    def _check_adaptive_cooldown(self, trading_pair: str, signal_strength: float,
                                market_volatility: float) -> dict:
        """检查自适应冷却时间"""
        last_trigger = self._last_trigger_times[trading_pair]
        if last_trigger is None:
            return {'allow': True, 'reason': '首次触发'}

        # 计算自适应冷却时间
        adaptive_cooldown = self._calculate_adaptive_cooldown(trading_pair, signal_strength, market_volatility)

        elapsed = datetime.now() - last_trigger
        required_cooldown = timedelta(minutes=adaptive_cooldown)

        if elapsed >= required_cooldown:
            return {'allow': True, 'reason': f'自适应冷却时间({adaptive_cooldown}分钟)已过'}
        else:
            remaining_seconds = int((required_cooldown - elapsed).total_seconds())
            return {
                'allow': False,
                'reason': f'自适应冷却时间未到，还需等待{remaining_seconds}秒',
                'adaptive_cooldown_minutes': adaptive_cooldown,
                'remaining_seconds': remaining_seconds
            }

    def _calculate_adaptive_cooldown(self, trading_pair: str, signal_strength: float,
                                   market_volatility: float) -> int:
        """计算自适应冷却时间"""
        base_cooldown = self._trigger_config['base_cooldown_minutes']

        # 信号强度因子
        if signal_strength > 0.9:
            signal_factor = 0.5  # 强信号减少冷却时间
        elif signal_strength > 0.8:
            signal_factor = 0.7
        elif signal_strength > 0.7:
            signal_factor = 1.0
        else:
            signal_factor = 1.5  # 弱信号增加冷却时间

        # 波动率因子
        if market_volatility > 0.05:
            volatility_factor = 0.8  # 高波动减少冷却时间
        elif market_volatility > 0.02:
            volatility_factor = 1.0
        else:
            volatility_factor = 1.2  # 低波动增加冷却时间

        # 成功率因子
        success_rate = self._get_recent_success_rate(trading_pair)
        if success_rate > 0.7:
            success_factor = 0.8  # 高成功率减少冷却时间
        elif success_rate > 0.5:
            success_factor = 1.0
        else:
            success_factor = 1.3  # 低成功率增加冷却时间

        # 计算最终冷却时间
        adaptive_cooldown = int(base_cooldown * signal_factor * volatility_factor * success_factor)

        # 限制在合理范围内
        min_cooldown = self._trigger_config['min_cooldown_minutes']
        max_cooldown = self._trigger_config['max_cooldown_minutes']
        return max(min_cooldown, min(max_cooldown, adaptive_cooldown))

    def _get_recent_success_rate(self, trading_pair: str) -> float:
        """获取最近的成功率"""
        success_history = self._success_rates[trading_pair]
        if not success_history:
            return 0.5  # 默认成功率

        return sum(success_history) / len(success_history)

    def _record_trigger(self, trading_pair: str, current_price: float,
                       signal_strength: float, total_score: float):
        """记录触发信息"""
        try:
            # 更新触发时间和价格
            self._last_trigger_times[trading_pair] = datetime.now()
            self._last_trigger_prices[trading_pair] = current_price
            self._trigger_counts[trading_pair] += 1

            logger.info(
                f"📊 触发记录已更新 - 交易对: {trading_pair}\n"
                f"触发次数: {self._trigger_counts[trading_pair]}\n"
                f"信号强度: {signal_strength:.2%}\n"
                f"综合得分: {total_score}"
            )

        except Exception as e:
            logger.error(f"记录触发信息失败: {str(e)}")

    def update_trigger_success(self, trading_pair: str, success: bool):
        """更新触发成功率（外部调用）"""
        try:
            success_history = self._success_rates[trading_pair]
            success_history.append(success)

            # 保持最近20次的记录
            if len(success_history) > 20:
                success_history.pop(0)

            current_rate = sum(success_history) / len(success_history)
            logger.info(f"触发成功率已更新 - {trading_pair}: {current_rate:.2%}")

        except Exception as e:
            logger.error(f"更新触发成功率失败: {str(e)}")

    def get_trigger_statistics(self, trading_pair: str = None) -> dict:
        """获取触发统计信息"""
        try:
            if trading_pair:
                # 特定交易对的统计
                return {
                    'trading_pair': trading_pair,
                    'trigger_count': self._trigger_counts[trading_pair],
                    'last_trigger_time': self._last_trigger_times[trading_pair],
                    'last_trigger_price': self._last_trigger_prices[trading_pair],
                    'recent_success_rate': self._get_recent_success_rate(trading_pair),
                    'success_history_length': len(self._success_rates[trading_pair])
                }
            else:
                # 全局统计
                total_triggers = sum(self._trigger_counts.values())
                active_pairs = len([pair for pair, count in self._trigger_counts.items() if count > 0])

                # 计算平均成功率
                all_rates = []
                for pair_rates in self._success_rates.values():
                    if pair_rates:
                        all_rates.extend(pair_rates)
                avg_success_rate = sum(all_rates) / len(all_rates) if all_rates else 0.0

                return {
                    'total_triggers': total_triggers,
                    'active_pairs': active_pairs,
                    'average_success_rate': avg_success_rate,
                    'trigger_counts_by_pair': dict(self._trigger_counts),
                    'config': self._trigger_config
                }

        except Exception as e:
            logger.error(f"获取触发统计信息失败: {str(e)}")
            return {'error': str(e)}

    def _calculate_volatility(self, price_history) -> float:
        """计算价格波动率"""
        try:
            if len(price_history) < 2:
                return 0.02  # 默认波动率

            # 计算价格变化率
            prices = list(price_history)
            returns = []
            for i in range(1, len(prices)):
                if prices[i-1] != 0:
                    return_rate = (prices[i] - prices[i-1]) / prices[i-1]
                    returns.append(return_rate)

            if not returns:
                return 0.02

            # 计算标准差作为波动率
            mean_return = sum(returns) / len(returns)
            variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
            volatility = variance ** 0.5

            # 限制在合理范围内
            return max(0.001, min(0.1, volatility))

        except Exception as e:
            logger.error(f"计算波动率失败: {str(e)}")
            return 0.02  # 默认波动率

    def _get_order_book(self, trading_pair: str) -> dict:
        """获取订单簿数据"""
        try:
            # 这里需要实现获取订单簿数据的逻辑
            # 暂时返回空数据
            return {
                'bids': [],
                'asks': []
            }
        except Exception as e:
            logger.error(f"获取订单簿数据失败: {str(e)}")
            return {
                'bids': [],
                'asks': []
            }

    def _on_message(self, message):
        """处理WebSocket消息"""
        try:
            # 如果message是字符串，先解析为JSON对象
            if isinstance(message, str):
                message = json.loads(message)
            
            if 'data' in message:
                data = message['data']
                action = message.get('action', '')
                
                # 处理ticker数据
                if 'last' in data:
                    price = float(data['last'])
                    trading_pair = data.get('symbol', '').split('USDT')[0]
                    timestamp = datetime.now()
                    
                    # logger.info(f"收到ticker数据 - 交易对: {trading_pair}, 价格: {price}, 时间: {timestamp}")
                    
                    if trading_pair in self._price_history:
                        # 更新价格历史
                        self._price_history[trading_pair].append(price)
                        
                        # 更新价格水平
                        self._price_levels[trading_pair].update(price, timestamp)
                        
                        # 保存价格数据到数据库
                        self._db_manager.save_price_data(
                            trading_pair=trading_pair,
                            price=price,
                            timestamp=timestamp,
                            volume=float(data.get('vol24h', 0)),
                            high=float(data.get('high24h', price)),
                            low=float(data.get('low24h', price))
                        )
                        
                        # 保存价格水平数据到数据库
                        levels = self._price_levels[trading_pair].get_levels()
                        self._db_manager.save_price_levels(
                            trading_pair=trading_pair,
                            support_level=min(levels['support_levels']),
                            resistance_level=max(levels['resistance_levels']),
                            ma5=levels['ma_values']['ma5'],
                            ma10=levels['ma_values']['ma10'],
                            ma20=levels['ma_values']['ma20'],
                            ma50=levels['ma_values']['ma50'],
                            timestamp=timestamp
                        )
                        
                        # 检查是否触发交易Agent团队
                        if self._should_trigger_agent(trading_pair, price):
                            # 计算价格变化和波动率
                            price_history = self._price_history[trading_pair]
                            price_change = (price - price_history[0]) / price_history[0]
                            volatility = self._calculate_volatility(price_history)
                            
                            logger.info(f"触发价格监控 - 交易对: {trading_pair}")
                            logger.info(f"价格变化: {price_change:.2%}, 波动率: {volatility:.2%}")
                            logger.info(f"支撑位: {levels['support_levels']}")
                            logger.info(f"压力位: {levels['resistance_levels']}")
                            
                            # 发送企业微信消息
                            send_wechat_message(trading_pair, price, price_change, volatility, levels)
                
                # 处理K线数据
                elif isinstance(data, list):
                    trading_pair = message.get('arg', {}).get('instId', '').split('USDT')[0]
                    if trading_pair in self._kline_data:
                        # logger.info(f"收到K线数据 - 交易对: {trading_pair}, 类型: {action}")
                        
                        if action == 'snapshot':
                            # 处理历史数据
                            latest_kline_data = None
                            for kline in data:
                                if isinstance(kline, list):
                                    self._kline_data[trading_pair].update(kline)
                                    kline_data = self._kline_data[trading_pair]
                                    latest_kline_data = kline_data  # 记录最新的K线数据

                                    # logger.info(f"处理历史K线 - 时间: {kline_data.timestamp}")
                                    # logger.info(f"开盘价: {kline_data.open}, 最高价: {kline_data.high}, 最低价: {kline_data.low}, 收盘价: {kline_data.close}")
                                    # logger.info(f"成交量: {kline_data.volume}")

                                    # 保存K线数据到数据库
                                    self._db_manager.save_kline_data(
                                        trading_pair=trading_pair,
                                        open_price=kline_data.open,
                                        high_price=kline_data.high,
                                        low_price=kline_data.low,
                                        close_price=kline_data.close,
                                        volume=kline_data.volume,
                                        timestamp=kline_data.timestamp
                                    )

                            # 🚀 新增：对最新的历史K线数据进行触发检查
                            if latest_kline_data and trading_pair in self._price_history:
                                current_price = latest_kline_data.close

                                # 更新价格历史
                                self._price_history[trading_pair].append(current_price)

                                # 更新价格水平
                                self._price_levels[trading_pair].update(current_price, latest_kline_data.timestamp)

                                # 检查是否触发交易Agent团队（历史数据通常不触发，但可以用于初始化检查）
                                logger.info(f"📊 历史K线数据加载完成 - 交易对: {trading_pair}, 最新价格: {current_price}")
                                # 注意：历史数据通常不触发实际交易，这里只是更新数据结构
                        elif action == 'update':
                            # 处理实时更新数据
                            if isinstance(data[0], list):
                                kline = data[0]  # 获取第一个K线数据
                                self._kline_data[trading_pair].update(kline)
                                kline_data = self._kline_data[trading_pair]

                                # logger.info(f"处理实时K线 - 时间: {kline_data.timestamp}")
                                # logger.info(f"开盘价: {kline_data.open}, 最高价: {kline_data.high}, 最低价: {kline_data.low}, 收盘价: {kline_data.close}")
                                # logger.info(f"成交量: {kline_data.volume}")

                                # 保存K线数据到数据库
                                self._db_manager.save_kline_data(
                                    trading_pair=trading_pair,
                                    open_price=kline_data.open,
                                    high_price=kline_data.high,
                                    low_price=kline_data.low,
                                    close_price=kline_data.close,
                                    volume=kline_data.volume,
                                    timestamp=kline_data.timestamp
                                )

                                # 🚀 新增：K线数据触发检查
                                # 使用收盘价作为当前价格进行触发检查
                                current_price = kline_data.close

                                # 更新价格历史（如果需要）
                                if trading_pair in self._price_history:
                                    self._price_history[trading_pair].append(current_price)

                                    # 更新价格水平
                                    self._price_levels[trading_pair].update(current_price, kline_data.timestamp)

                                    # 检查是否触发交易Agent团队
                                    if self._should_trigger_agent(trading_pair, current_price):
                                        # 计算价格变化和波动率
                                        price_history = self._price_history[trading_pair]
                                        price_change = (current_price - price_history[0]) / price_history[0] if len(price_history) > 1 else 0
                                        volatility = self._calculate_volatility(price_history)

                                        # 获取价格水平信息
                                        levels = self._price_levels[trading_pair].get_levels()

                                        logger.info(f"🚀 K线数据触发价格监控 - 交易对: {trading_pair}")
                                        logger.info(f"K线时间: {kline_data.timestamp}")
                                        logger.info(f"OHLCV: O:{kline_data.open} H:{kline_data.high} L:{kline_data.low} C:{kline_data.close} V:{kline_data.volume}")
                                        logger.info(f"价格变化: {price_change:.2%}, 波动率: {volatility:.2%}")
                                        logger.info(f"支撑位: {levels['support_levels']}")
                                        logger.info(f"压力位: {levels['resistance_levels']}")

                                        # 发送企业微信消息
                                        send_wechat_message(trading_pair, current_price, price_change, volatility, levels)
                            
        except Exception as e:
            logger.error(f"处理WebSocket消息失败: {str(e)}")
            logger.error(f"错误消息内容: {message}")
            logger.exception("详细错误信息：")  # 添加详细的错误堆栈信息

def main():
    """主函数"""
    try:
        logger.info("启动价格监控系统...")
        # 创建价格监控实例
        monitor = PriceMonitor()
        logger.info("价格监控系统已启动")
    except Exception as e:
        logger.error(f"程序运行失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()