#!/usr/bin/env python3
"""
增强价格监控系统 - 基于专业交易员思维的价格监控和交易唤醒机制
"""

import sys
import json
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from pathlib import Path
from collections import deque
import numpy as np

# 添加项目路径
sys.path.append(str(Path(__file__).parent / "src"))

from gold_agents.tools.professional_market_analyzer import ProfessionalMarketAnalyzer
from gold_agents.crew import GoldAgents


class EnhancedPriceMonitor:
    """增强价格监控系统"""
    
    def __init__(self):
        """初始化增强价格监控系统"""
        self.logger = self._setup_logger()
        self.market_analyzer = ProfessionalMarketAnalyzer()
        self.price_history = deque(maxlen=1000)
        self.last_trigger_time = None
        self.trading_session_active = False
        
        # 专业交易员级别的触发条件
        self.trigger_config = {
            'min_confidence': 0.7,  # 最低置信度
            'min_risk_reward': 1.5,  # 最低风险收益比
            'max_risk_level': 0.7,   # 最大风险等级
            'cooldown_minutes': 15,  # 触发冷却时间
            'timeframe_confluence_required': True,  # 需要时间框架一致性
            'volume_confirmation_required': True,   # 需要成交量确认
            'key_level_break_required': False      # 是否必须突破关键位
        }
        
        self.logger.info("增强价格监控系统初始化完成")

    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(f"{self.__class__.__name__}")
        
        if not logger.handlers:
            # 创建logs目录
            log_dir = Path("logs")
            log_dir.mkdir(exist_ok=True)
            
            # 文件处理器
            file_handler = logging.FileHandler(
                log_dir / f"enhanced_price_monitor_{datetime.now().strftime('%Y%m%d')}.log",
                encoding='utf-8'
            )
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            ))
            
            # 控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(logging.Formatter(
                '%(asctime)s - %(levelname)s - %(message)s'
            ))
            
            logger.addHandler(file_handler)
            logger.addHandler(console_handler)
            logger.setLevel(logging.INFO)
            
        return logger

    def should_trigger_trading_team(self, trading_pair: str, current_price: float) -> Dict[str, Any]:
        """专业交易员级别的交易团队触发判断"""
        try:
            self.logger.info(f"开始专业级交易机会分析 - {trading_pair}: {current_price}")
            
            # 1. 检查冷却时间
            if not self._check_cooldown():
                return {
                    'should_trigger': False,
                    'reason': '触发冷却时间未到',
                    'next_check_time': self.last_trigger_time + timedelta(minutes=self.trigger_config['cooldown_minutes'])
                }
            
            # 2. 分析交易机会
            opportunity_analysis = self.market_analyzer.run(
                action="analyze_opportunity",
                trading_pair=trading_pair
            )
            
            if opportunity_analysis['status'] != 'success':
                return {
                    'should_trigger': False,
                    'reason': '市场分析失败',
                    'error': opportunity_analysis.get('message', 'unknown')
                }
            
            # 3. 评估市场风险
            risk_analysis = self.market_analyzer.run(
                action="assess_risk",
                trading_pair=trading_pair
            )
            
            # 4. 检测市场制度
            regime_analysis = self.market_analyzer.run(
                action="detect_regime",
                trading_pair=trading_pair
            )
            
            # 5. 综合评估是否触发
            trigger_decision = self._evaluate_trigger_conditions(
                opportunity_analysis, risk_analysis, regime_analysis, current_price
            )
            
            if trigger_decision['should_trigger']:
                self.last_trigger_time = datetime.now()
                self.logger.info(f"🚀 触发交易团队 - {trading_pair}")
                self._log_trigger_details(trigger_decision, opportunity_analysis, risk_analysis)
            else:
                self.logger.info(f"⏸️ 不触发交易团队 - {trigger_decision['reason']}")
            
            return trigger_decision
            
        except Exception as e:
            self.logger.error(f"交易团队触发判断失败: {str(e)}")
            return {
                'should_trigger': False,
                'reason': f'分析异常: {str(e)}',
                'error': str(e)
            }

    def _check_cooldown(self) -> bool:
        """检查触发冷却时间"""
        if self.last_trigger_time is None:
            return True
        
        cooldown_period = timedelta(minutes=self.trigger_config['cooldown_minutes'])
        return datetime.now() - self.last_trigger_time >= cooldown_period

    def _evaluate_trigger_conditions(self, opportunity_analysis: Dict[str, Any], 
                                   risk_analysis: Dict[str, Any], 
                                   regime_analysis: Dict[str, Any],
                                   current_price: float) -> Dict[str, Any]:
        """评估触发条件"""
        try:
            # 提取关键数据
            opportunity = opportunity_analysis.get('opportunity')
            risk_assessment = risk_analysis.get('risk_assessment', {})
            overall_risk = risk_assessment.get('overall_risk', {})
            
            # 初始化检查结果
            checks = {
                'opportunity_exists': False,
                'confidence_sufficient': False,
                'risk_reward_acceptable': False,
                'risk_level_acceptable': False,
                'timeframe_confluence': False,
                'volume_confirmation': False,
                'key_level_break': False
            }
            
            reasons = []
            
            # 1. 检查是否存在交易机会
            if opportunity:
                checks['opportunity_exists'] = True
                
                # 2. 检查置信度
                confidence = opportunity.get('confidence', 0)
                if confidence >= self.trigger_config['min_confidence']:
                    checks['confidence_sufficient'] = True
                else:
                    reasons.append(f"置信度不足: {confidence:.2%} < {self.trigger_config['min_confidence']:.2%}")
                
                # 3. 检查风险收益比
                risk_reward = opportunity.get('risk_reward_ratio', 0)
                if risk_reward >= self.trigger_config['min_risk_reward']:
                    checks['risk_reward_acceptable'] = True
                else:
                    reasons.append(f"风险收益比不足: {risk_reward:.2f} < {self.trigger_config['min_risk_reward']}")
                
                # 4. 检查时间框架一致性
                if opportunity.get('timeframe_confluence', False):
                    checks['timeframe_confluence'] = True
                elif self.trigger_config['timeframe_confluence_required']:
                    reasons.append("缺少时间框架一致性确认")
                
                # 5. 检查成交量确认
                if opportunity.get('volume_confirmation', False):
                    checks['volume_confirmation'] = True
                elif self.trigger_config['volume_confirmation_required']:
                    reasons.append("缺少成交量确认")
                
                # 6. 检查关键位突破
                if opportunity.get('key_level_break', False):
                    checks['key_level_break'] = True
                elif self.trigger_config['key_level_break_required']:
                    reasons.append("未突破关键位")
                
            else:
                reasons.append("当前无交易机会")
            
            # 7. 检查市场风险
            risk_score = overall_risk.get('risk_score', 1.0)
            if risk_score <= self.trigger_config['max_risk_level']:
                checks['risk_level_acceptable'] = True
            else:
                reasons.append(f"市场风险过高: {risk_score:.2%} > {self.trigger_config['max_risk_level']:.2%}")
            
            # 8. 综合判断
            required_checks = [
                'opportunity_exists',
                'confidence_sufficient', 
                'risk_reward_acceptable',
                'risk_level_acceptable'
            ]
            
            # 添加可选的必需检查
            if self.trigger_config['timeframe_confluence_required']:
                required_checks.append('timeframe_confluence')
            if self.trigger_config['volume_confirmation_required']:
                required_checks.append('volume_confirmation')
            if self.trigger_config['key_level_break_required']:
                required_checks.append('key_level_break')
            
            # 检查所有必需条件
            all_required_met = all(checks[check] for check in required_checks)
            
            return {
                'should_trigger': all_required_met,
                'reason': '所有条件满足' if all_required_met else '; '.join(reasons),
                'checks': checks,
                'opportunity_data': opportunity,
                'risk_data': overall_risk,
                'confidence': opportunity.get('confidence', 0) if opportunity else 0,
                'risk_reward_ratio': opportunity.get('risk_reward_ratio', 0) if opportunity else 0,
                'market_risk_score': risk_score
            }
            
        except Exception as e:
            return {
                'should_trigger': False,
                'reason': f'评估条件时出错: {str(e)}',
                'error': str(e)
            }

    def _log_trigger_details(self, trigger_decision: Dict[str, Any], 
                           opportunity_analysis: Dict[str, Any], 
                           risk_analysis: Dict[str, Any]):
        """记录触发详情"""
        try:
            self.logger.info("=" * 60)
            self.logger.info("🎯 交易团队触发详情")
            self.logger.info("=" * 60)
            
            # 机会详情
            opportunity = opportunity_analysis.get('opportunity')
            if opportunity:
                self.logger.info(f"📊 交易机会:")
                self.logger.info(f"   信号类型: {opportunity.get('signal_type', 'unknown')}")
                self.logger.info(f"   置信度: {opportunity.get('confidence', 0):.2%}")
                self.logger.info(f"   风险收益比: {opportunity.get('risk_reward_ratio', 0):.2f}")
                self.logger.info(f"   最优入场: {opportunity.get('optimal_entry', 0):.2f}")
                self.logger.info(f"   止损位: {opportunity.get('stop_loss', 0):.2f}")
                self.logger.info(f"   止盈位: {opportunity.get('take_profit', 0):.2f}")
            
            # 风险详情
            risk_assessment = risk_analysis.get('risk_assessment', {})
            overall_risk = risk_assessment.get('overall_risk', {})
            self.logger.info(f"⚠️ 风险评估:")
            self.logger.info(f"   风险评分: {overall_risk.get('risk_score', 0):.2%}")
            self.logger.info(f"   风险等级: {overall_risk.get('risk_level', 'unknown')}")
            self.logger.info(f"   建议仓位: {overall_risk.get('max_position_size', 0):.2%}")
            self.logger.info(f"   建议杠杆: {overall_risk.get('recommended_leverage', 1)}x")
            
            # 检查详情
            checks = trigger_decision.get('checks', {})
            self.logger.info(f"✅ 条件检查:")
            for check_name, result in checks.items():
                status = "✅" if result else "❌"
                self.logger.info(f"   {check_name}: {status}")
            
            self.logger.info("=" * 60)
            
        except Exception as e:
            self.logger.error(f"记录触发详情失败: {str(e)}")

    def trigger_trading_team(self, trading_pair: str, trigger_data: Dict[str, Any]) -> Dict[str, Any]:
        """触发交易团队执行"""
        try:
            self.logger.info(f"🚀 启动交易团队 - {trading_pair}")
            
            # 准备交易团队输入数据
            team_input = {
                "trading_pair": trading_pair,
                "trigger_reason": trigger_data.get('reason', '专业分析触发'),
                "opportunity_data": trigger_data.get('opportunity_data', {}),
                "risk_data": trigger_data.get('risk_data', {}),
                "market_conditions": {
                    "confidence": trigger_data.get('confidence', 0),
                    "risk_reward_ratio": trigger_data.get('risk_reward_ratio', 0),
                    "market_risk_score": trigger_data.get('market_risk_score', 0)
                },
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            # 启动交易团队
            gold_agents = GoldAgents()
            
            # 执行交易分析和决策
            result = gold_agents.crew().kickoff(inputs=team_input)
            
            self.trading_session_active = True
            
            self.logger.info(f"✅ 交易团队执行完成")
            
            return {
                "status": "success",
                "trading_result": result,
                "team_input": team_input,
                "execution_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            self.logger.error(f"触发交易团队失败: {str(e)}")
            return {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        finally:
            # 确保资源清理
            try:
                gold_agents.close()
            except:
                pass

    def monitor_price_changes(self, trading_pair: str = "BTC"):
        """监控价格变化（模拟）"""
        self.logger.info(f"开始监控 {trading_pair} 价格变化...")
        
        # 模拟价格数据
        base_price = 50000.0
        
        try:
            while True:
                # 模拟价格波动
                price_change = np.random.normal(0, 200)
                current_price = base_price + price_change
                
                self.price_history.append(current_price)
                
                self.logger.info(f"📈 {trading_pair} 当前价格: {current_price:.2f}")
                
                # 检查是否应该触发交易团队
                trigger_decision = self.should_trigger_trading_team(trading_pair, current_price)
                
                if trigger_decision['should_trigger']:
                    # 触发交易团队
                    trading_result = self.trigger_trading_team(trading_pair, trigger_decision)
                    
                    if trading_result['status'] == 'success':
                        self.logger.info("🎉 交易团队执行成功")
                    else:
                        self.logger.error(f"❌ 交易团队执行失败: {trading_result.get('message', 'unknown')}")
                
                # 等待下次检查
                time.sleep(10)  # 10秒检查一次
                
        except KeyboardInterrupt:
            self.logger.info("收到停止信号，正在关闭价格监控...")
        except Exception as e:
            self.logger.error(f"价格监控异常: {str(e)}")

    def get_monitor_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        return {
            "monitor_active": True,
            "last_trigger_time": self.last_trigger_time.strftime("%Y-%m-%d %H:%M:%S") if self.last_trigger_time else None,
            "trading_session_active": self.trading_session_active,
            "price_history_length": len(self.price_history),
            "trigger_config": self.trigger_config,
            "cooldown_remaining": self._get_cooldown_remaining()
        }

    def _get_cooldown_remaining(self) -> Optional[int]:
        """获取剩余冷却时间（秒）"""
        if self.last_trigger_time is None:
            return None
        
        cooldown_period = timedelta(minutes=self.trigger_config['cooldown_minutes'])
        elapsed = datetime.now() - self.last_trigger_time
        remaining = cooldown_period - elapsed
        
        return max(0, int(remaining.total_seconds())) if remaining.total_seconds() > 0 else 0


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="增强价格监控系统")
    parser.add_argument("--trading-pair", default="BTC", help="监控的交易对")
    parser.add_argument("--test-trigger", action="store_true", help="测试触发逻辑")
    
    args = parser.parse_args()
    
    monitor = EnhancedPriceMonitor()
    
    if args.test_trigger:
        # 测试触发逻辑
        print("🧪 测试触发逻辑...")
        result = monitor.should_trigger_trading_team(args.trading_pair, 50000.0)
        print(f"触发结果: {json.dumps(result, indent=2, ensure_ascii=False, default=str)}")
    else:
        # 启动价格监控
        monitor.monitor_price_changes(args.trading_pair)


if __name__ == "__main__":
    main()
