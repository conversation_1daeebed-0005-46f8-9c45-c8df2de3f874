#!/usr/bin/env python3
"""
测试交易员Agent的复盘功能集成
验证交易员是否能正确使用复盘结果查询工具
"""

import sys
import json
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.append(str(Path(__file__).parent / "src"))


def test_trader_review_tools():
    """测试交易员的复盘工具"""
    print("🤖 测试交易员Agent的复盘工具集成...")
    print("="*50)
    
    try:
        # 1. 测试复盘查询工具
        print("🔍 测试复盘结果查询工具...")
        from gold_agents.tools.review_result_query_tool import ReviewResultQueryTool
        
        query_tool = ReviewResultQueryTool()
        
        # 测试获取最新报告
        print("   📊 获取最新复盘报告...")
        latest_report = query_tool.run(action="get_latest_report", report_type="daily")
        print(f"   结果: {latest_report['status']}")
        
        if latest_report['status'] == 'success':
            report = latest_report['report']
            print(f"   报告ID: {report['id']}")
            print(f"   报告类型: {report['report_type']}")
            print(f"   创建时间: {report['created_at']}")
            
            # 显示关键指标
            if report.get('win_rate'):
                print(f"   胜率: {report['win_rate']:.2%}")
            if report.get('profit_factor'):
                print(f"   盈利因子: {report['profit_factor']:.2f}")
            if report.get('max_drawdown'):
                print(f"   最大回撤: {report['max_drawdown']:.2%}")
        
        # 测试获取绩效摘要
        print("\n   📈 获取绩效摘要...")
        performance_summary = query_tool.run(action="get_performance_summary", days_back=30)
        print(f"   结果: {performance_summary['status']}")
        
        if performance_summary['status'] == 'success':
            summary = performance_summary['performance_summary']
            print(f"   分析期间: {performance_summary['period']}")
            print(f"   报告数量: {performance_summary['reports_count']}")
            
            # 显示趋势分析
            trends = performance_summary.get('trend_analysis', [])
            if trends:
                print("   趋势分析:")
                for trend in trends[:3]:  # 显示前3个趋势
                    print(f"     - {trend}")
        
        # 测试获取洞察摘要
        print("\n   💡 获取洞察摘要...")
        insights_summary = query_tool.run(action="get_insights_summary", days_back=7)
        print(f"   结果: {insights_summary['status']}")
        
        if insights_summary['status'] == 'success':
            insights = insights_summary['insights_summary']
            print(f"   关键洞察数量: {insights['total_insights']}")
            print(f"   改进建议数量: {insights['total_suggestions']}")
            
            # 显示最近的洞察
            recent_insights = insights_summary.get('recent_insights', [])
            if recent_insights:
                print("   最近洞察:")
                for insight in recent_insights[:2]:  # 显示前2个洞察
                    print(f"     - {insight}")
        
        print("✅ 复盘结果查询工具测试成功")
        
    except Exception as e:
        print(f"❌ 复盘结果查询工具测试失败: {str(e)}")
        return False
    
    return True


def test_trader_agent_configuration():
    """测试交易员Agent配置"""
    print("\n🎯 测试交易员Agent配置...")
    print("="*40)
    
    try:
        # 检查交易员Agent的工具配置
        from gold_agents.crew import GoldAgents
        
        gold_agents = GoldAgents()
        trader_agent = gold_agents.trader()
        
        # 获取工具列表
        tool_names = [tool.__class__.__name__ for tool in trader_agent.tools]
        print(f"📋 交易员Agent工具列表: {tool_names}")
        
        # 检查必要的工具
        required_tools = {
            'ReviewResultQueryTool': '复盘结果查询工具',
            'StrategyTool': '策略工具',
            'BitgetMarketTool': '市场数据工具',
            'BitgetAccountTool': '账户工具',
            'TradingRulesEngine': '交易规则引擎'
        }
        
        missing_tools = []
        for tool_class, tool_desc in required_tools.items():
            if not any(tool_class in tool_name for tool_name in tool_names):
                missing_tools.append(f"{tool_class} ({tool_desc})")
        
        if missing_tools:
            print(f"❌ 缺少必要工具: {missing_tools}")
            return False
        else:
            print("✅ 所有必要工具已配置")
        
        # 检查Agent配置
        agent_config = trader_agent.config
        print(f"📝 Agent角色: {agent_config.get('role', 'unknown')}")
        print(f"🎯 Agent目标: {agent_config.get('goal', 'unknown')}")
        
        # 检查是否包含复盘相关内容
        prompt = agent_config.get('prompt', '')
        review_keywords = ['复盘', 'ReviewResultQueryTool', '绩效', '洞察', '改进建议']
        
        found_keywords = []
        for keyword in review_keywords:
            if keyword in prompt:
                found_keywords.append(keyword)
        
        if found_keywords:
            print(f"✅ 提示词包含复盘相关内容: {found_keywords}")
        else:
            print("❌ 提示词缺少复盘相关内容")
            return False
        
        gold_agents.close()
        print("✅ 交易员Agent配置检查成功")
        
    except Exception as e:
        print(f"❌ 交易员Agent配置检查失败: {str(e)}")
        return False
    
    return True


def test_trader_review_workflow():
    """测试交易员的复盘工作流程"""
    print("\n🔄 测试交易员复盘工作流程...")
    print("="*40)
    
    try:
        # 模拟交易员使用复盘工具的完整流程
        from gold_agents.tools.review_result_query_tool import ReviewResultQueryTool
        
        query_tool = ReviewResultQueryTool()
        
        print("📋 模拟交易员工作流程:")
        
        # 步骤1: 查询最新复盘报告
        print("   1. 查询最新复盘报告...")
        latest_report = query_tool.run(action="get_latest_report")
        
        if latest_report['status'] == 'success':
            print("      ✅ 成功获取复盘报告")
            
            # 提取关键信息
            report = latest_report['report']
            key_insights = report.get('key_insights', [])
            improvement_suggestions = report.get('improvement_suggestions', [])
            
            print(f"      📊 关键洞察: {len(key_insights)}条")
            print(f"      💡 改进建议: {len(improvement_suggestions)}条")
            
        else:
            print(f"      ⚠️ 无法获取复盘报告: {latest_report.get('message', 'unknown')}")
        
        # 步骤2: 获取绩效趋势
        print("   2. 分析绩效趋势...")
        performance_summary = query_tool.run(action="get_performance_summary", days_back=7)
        
        if performance_summary['status'] == 'success':
            print("      ✅ 成功获取绩效趋势")
            
            # 分析趋势
            trends = performance_summary.get('trend_analysis', [])
            if trends:
                print("      📈 发现趋势:")
                for trend in trends[:2]:
                    print(f"         - {trend}")
        
        # 步骤3: 获取具体洞察
        print("   3. 获取具体洞察...")
        insights_summary = query_tool.run(action="get_insights_summary", days_back=7)
        
        if insights_summary['status'] == 'success':
            print("      ✅ 成功获取洞察摘要")
            
            # 提取可执行的建议
            recent_suggestions = insights_summary.get('recent_suggestions', [])
            if recent_suggestions:
                print("      🎯 可执行建议:")
                for suggestion in recent_suggestions[:2]:
                    print(f"         - {suggestion}")
        
        # 步骤4: 模拟决策应用
        print("   4. 应用复盘结果到交易决策...")
        
        # 构建交易员的决策输出（模拟）
        trader_decision = {
            "role": "Trader",
            "action": "open_long",
            "symbol": "BTCUSDT",
            "order_type": "limit",
            "price": 50000.0,
            "size": 0.1,
            "stop_loss": 49000.0,
            "take_profit": 51000.0,
            "reason": "基于复盘分析的策略信号",
            "review_insights": {
                "latest_review_applied": True,
                "key_insights": key_insights[:2] if 'key_insights' in locals() else [],
                "strategy_adjustments": ["基于复盘调整信号阈值"],
                "performance_reference": {
                    "recent_win_rate": 0.65,
                    "recent_profit_factor": 2.1
                }
            },
            "risk_metrics": {
                "account_risk": 0.02,
                "position_size": 0.1,
                "margin_ratio": 0.6
            },
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        print("      ✅ 成功生成包含复盘洞察的交易决策")
        print(f"      📋 决策包含 {len(trader_decision['review_insights']['key_insights'])} 条洞察")
        
        print("🎉 交易员复盘工作流程测试成功")
        
    except Exception as e:
        print(f"❌ 交易员复盘工作流程测试失败: {str(e)}")
        return False
    
    return True


def test_agent_prompt_content():
    """测试Agent提示词内容"""
    print("\n📝 测试Agent提示词内容...")
    print("="*30)
    
    try:
        # 读取agents.yaml配置
        import yaml
        config_path = Path(__file__).parent / "src" / "gold_agents" / "config" / "agents.yaml"
        
        with open(config_path, 'r', encoding='utf-8') as f:
            agents_config = yaml.safe_load(f)
        
        trader_config = agents_config.get('trader', {})
        
        # 检查基本配置
        print(f"📋 角色: {trader_config.get('role', 'unknown')}")
        print(f"🎯 目标: {trader_config.get('goal', 'unknown')}")
        
        # 检查目标是否包含复盘内容
        goal = trader_config.get('goal', '')
        if '复盘' in goal:
            print("✅ 目标包含复盘相关内容")
        else:
            print("❌ 目标缺少复盘相关内容")
            return False
        
        # 检查提示词内容
        prompt = trader_config.get('prompt', '')
        
        # 检查复盘相关关键词
        review_keywords = [
            'ReviewResultQueryTool',
            '复盘结果查询',
            '绩效摘要',
            '洞察摘要',
            '复盘建议',
            '策略调整',
            '历史表现'
        ]
        
        found_keywords = []
        for keyword in review_keywords:
            if keyword in prompt:
                found_keywords.append(keyword)
        
        print(f"📊 找到复盘关键词: {len(found_keywords)}/{len(review_keywords)}")
        print(f"   关键词: {found_keywords}")
        
        if len(found_keywords) >= 5:  # 至少包含5个关键词
            print("✅ 提示词包含充分的复盘相关内容")
        else:
            print("❌ 提示词复盘相关内容不足")
            return False
        
        # 检查工具使用指南
        if 'ReviewResultQueryTool' in prompt and '复盘结果应用指南' in prompt:
            print("✅ 包含复盘工具使用指南")
        else:
            print("❌ 缺少复盘工具使用指南")
            return False
        
        # 检查输出格式
        if 'review_insights' in prompt:
            print("✅ 输出格式包含复盘洞察字段")
        else:
            print("❌ 输出格式缺少复盘洞察字段")
            return False
        
        print("✅ Agent提示词内容检查成功")
        
    except Exception as e:
        print(f"❌ Agent提示词内容检查失败: {str(e)}")
        return False
    
    return True


def main():
    """主测试函数"""
    print("🚀 交易员Agent复盘功能集成测试")
    print("="*50)
    print("测试内容：")
    print("1. 复盘工具功能测试")
    print("2. Agent配置检查")
    print("3. 复盘工作流程测试")
    print("4. 提示词内容检查")
    print("")
    
    test_results = []
    
    # 执行所有测试
    tests = [
        ("复盘工具功能", test_trader_review_tools),
        ("Agent配置检查", test_trader_agent_configuration),
        ("复盘工作流程", test_trader_review_workflow),
        ("提示词内容", test_agent_prompt_content)
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "="*50)
    print("📊 交易员Agent复盘功能集成测试总结")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 交易员Agent复盘功能完全集成！")
        print("🎯 集成效果：")
        print("   ✅ 交易员可查询最新复盘报告")
        print("   ✅ 交易员可获取绩效趋势分析")
        print("   ✅ 交易员可应用复盘洞察到决策")
        print("   ✅ 提示词包含完整的复盘指导")
        print("   ✅ 输出格式包含复盘相关字段")
        print("\n🚀 交易员现在可以基于复盘结果做出更明智的交易决策！")
        return 0
    else:
        print(f"\n⚠️ {total-passed}个测试失败，请检查相关配置")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
