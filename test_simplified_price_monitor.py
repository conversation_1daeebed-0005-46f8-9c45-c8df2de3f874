#!/usr/bin/env python3
"""
简化价格监控系统测试
验证专注于价格变化区间监控的简化逻辑
"""

import sys
import time
from pathlib import Path
from datetime import datetime, timedelta
from collections import deque

# 添加项目路径
sys.path.append(str(Path(__file__).parent / "src"))


def test_price_change_analysis():
    """测试价格变化分析"""
    print("📊 测试价格变化分析...")
    print("="*50)
    
    try:
        from price_monitor import PriceMonitor
        
        monitor = PriceMonitor()
        
        # 测试不同幅度的价格变化
        test_cases = [
            # (基准价格, 当前价格, 预期变化类型, 预期是否触发)
            (50000.0, 50000.0, 'minimal_change_flat', False),      # 无变化
            (50000.0, 50100.0, 'small_change_up', False),          # 0.2%上涨 - 小幅变化
            (50000.0, 49900.0, 'small_change_down', False),        # 0.2%下跌 - 小幅变化
            (50000.0, 50750.0, 'medium_change_up', True),          # 1.5%上涨 - 中等变化
            (50000.0, 49250.0, 'medium_change_down', True),        # 1.5%下跌 - 中等变化
            (50000.0, 51500.0, 'large_change_up', True),           # 3.0%上涨 - 大幅变化
            (50000.0, 48500.0, 'large_change_down', True),         # 3.0%下跌 - 大幅变化
            (50000.0, 52500.0, 'extreme_change_up', True),         # 5.0%上涨 - 极端变化
            (50000.0, 47500.0, 'extreme_change_down', True),       # 5.0%下跌 - 极端变化
        ]
        
        print("📋 测试各种价格变化场景:")
        
        for base_price, current_price, expected_type, expected_trigger in test_cases:
            # 创建价格历史
            price_history = deque([base_price], maxlen=100)
            
            # 分析价格变化
            analysis = monitor._analyze_price_change(price_history, current_price)
            
            # 计算实际变化百分比
            actual_change = (current_price - base_price) / base_price
            
            print(f"\n   基准价格: {base_price}, 当前价格: {current_price}")
            print(f"   实际变化: {actual_change:.2%}")
            print(f"   分析结果: {analysis['change_type']}")
            print(f"   信号强度: {analysis['signal_strength']:.1%}")
            print(f"   是否触发: {analysis['should_trigger']}")
            
            # 验证结果
            if analysis['change_type'] == expected_type:
                print(f"   ✅ 变化类型正确")
            else:
                print(f"   ❌ 变化类型错误，期望: {expected_type}, 实际: {analysis['change_type']}")
                return False
            
            if analysis['should_trigger'] == expected_trigger:
                print(f"   ✅ 触发判断正确")
            else:
                print(f"   ❌ 触发判断错误，期望: {expected_trigger}, 实际: {analysis['should_trigger']}")
                return False
        
        print("\n✅ 价格变化分析测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 价格变化分析测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_simplified_trigger_logic():
    """测试简化的触发逻辑"""
    print("\n🎯 测试简化的触发逻辑...")
    print("="*50)
    
    try:
        from price_monitor import PriceMonitor
        from unittest.mock import Mock, patch
        
        monitor = PriceMonitor()
        trading_pair = "BTC"
        
        # 初始化必要的数据结构
        monitor._price_history[trading_pair] = deque([50000.0], maxlen=100)
        
        # 模拟企业微信发送函数
        with patch('price_monitor.send_wechat_message') as mock_wechat:
            
            # 测试场景1: 小幅变化（不应触发）
            print("📋 测试场景1: 小幅变化（0.3%上涨）")
            small_change_price = 50150.0  # 0.3%上涨
            
            should_trigger_1 = monitor._should_trigger_agent(trading_pair, small_change_price)
            print(f"   小幅变化触发结果: {should_trigger_1}")
            
            if not should_trigger_1:
                print("   ✅ 小幅变化正确不触发")
            else:
                print("   ❌ 小幅变化不应该触发")
                return False
            
            # 测试场景2: 大幅变化（应该触发）
            print("\n📋 测试场景2: 大幅变化（3.5%上涨）")
            large_change_price = 51750.0  # 3.5%上涨
            
            should_trigger_2 = monitor._should_trigger_agent(trading_pair, large_change_price)
            print(f"   大幅变化触发结果: {should_trigger_2}")
            
            if should_trigger_2:
                print("   ✅ 大幅变化正确触发")
                
                # 验证触发记录
                if monitor._trigger_counts[trading_pair] > 0:
                    print("   ✅ 触发记录已更新")
                else:
                    print("   ❌ 触发记录未更新")
                    return False
                    
            else:
                print("   ❌ 大幅变化应该触发")
                return False
            
            # 测试场景3: 立即重复触发（应该被智能防重复阻止）
            print("\n📋 测试场景3: 立即重复触发")
            repeat_price = 51800.0  # 再次大幅变化
            
            should_trigger_3 = monitor._should_trigger_agent(trading_pair, repeat_price)
            print(f"   重复触发结果: {should_trigger_3}")
            
            if not should_trigger_3:
                print("   ✅ 重复触发被智能防重复正确阻止")
            else:
                print("   ❌ 重复触发应该被阻止")
                return False
        
        print("\n✅ 简化触发逻辑测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 简化触发逻辑测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_price_change_thresholds():
    """测试价格变化阈值配置"""
    print("\n⚙️ 测试价格变化阈值配置...")
    print("="*50)
    
    try:
        from price_monitor import PriceMonitor
        
        monitor = PriceMonitor()
        
        # 测试阈值边界情况
        base_price = 50000.0
        price_history = deque([base_price], maxlen=100)
        
        threshold_tests = [
            # (变化百分比, 预期变化类型, 预期是否触发)
            (0.004, 'minimal_change', False),    # 0.4% - 低于小幅变化阈值
            (0.005, 'small_change', False),      # 0.5% - 刚好达到小幅变化阈值
            (0.014, 'small_change', False),      # 1.4% - 小幅变化范围内
            (0.015, 'medium_change', True),      # 1.5% - 刚好达到中等变化阈值
            (0.029, 'medium_change', True),      # 2.9% - 中等变化范围内
            (0.030, 'large_change', True),       # 3.0% - 刚好达到大幅变化阈值
            (0.049, 'large_change', True),       # 4.9% - 大幅变化范围内
            (0.050, 'extreme_change', True),     # 5.0% - 刚好达到极端变化阈值
            (0.080, 'extreme_change', True),     # 8.0% - 极端变化范围内
        ]
        
        print("📋 测试价格变化阈值边界:")
        
        for change_pct, expected_type_prefix, expected_trigger in threshold_tests:
            current_price = base_price * (1 + change_pct)
            
            analysis = monitor._analyze_price_change(price_history, current_price)
            
            print(f"\n   变化: {change_pct:.1%}, 价格: {base_price} → {current_price}")
            print(f"   分析类型: {analysis['change_type']}")
            print(f"   信号强度: {analysis['signal_strength']:.1%}")
            print(f"   是否触发: {analysis['should_trigger']}")
            
            # 验证变化类型（忽略方向后缀）
            actual_type_prefix = analysis['change_type'].split('_')[0] + '_' + analysis['change_type'].split('_')[1]
            if actual_type_prefix == expected_type_prefix:
                print(f"   ✅ 变化类型正确")
            else:
                print(f"   ❌ 变化类型错误，期望: {expected_type_prefix}, 实际: {actual_type_prefix}")
                return False
            
            # 验证触发判断
            if analysis['should_trigger'] == expected_trigger:
                print(f"   ✅ 触发判断正确")
            else:
                print(f"   ❌ 触发判断错误，期望: {expected_trigger}, 实际: {analysis['should_trigger']}")
                return False
        
        print("\n✅ 价格变化阈值配置测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 价格变化阈值配置测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_real_kline_integration():
    """测试真实K线数据集成"""
    print("\n📊 测试真实K线数据集成...")
    print("="*50)
    
    try:
        from price_monitor import PriceMonitor
        from unittest.mock import Mock, patch
        
        monitor = PriceMonitor()
        trading_pair = "BTC"
        
        # 初始化数据结构
        monitor._price_history[trading_pair] = deque(maxlen=100)
        
        # 模拟数据库管理器
        monitor._db_manager.save_kline_data = Mock()
        
        # 模拟企业微信发送函数
        with patch('price_monitor.send_wechat_message') as mock_wechat:
            
            # 构造K线消息序列（模拟价格大幅变化）
            kline_messages = [
                # 基准K线
                {
                    'action': 'update',
                    'arg': {'instType': 'sp', 'channel': 'candle1m', 'instId': 'BTCUSDT'},
                    'data': [['1703001600000', '50000.0', '50100.0', '49900.0', '50000.0', '1000.0']],
                    'ts': 1703001620000
                },
                # 小幅变化K线（不应触发）
                {
                    'action': 'update',
                    'arg': {'instType': 'sp', 'channel': 'candle1m', 'instId': 'BTCUSDT'},
                    'data': [['1703001660000', '50000.0', '50200.0', '49950.0', '50100.0', '1100.0']],
                    'ts': 1703001680000
                },
                # 大幅变化K线（应该触发）
                {
                    'action': 'update',
                    'arg': {'instType': 'sp', 'channel': 'candle1m', 'instId': 'BTCUSDT'},
                    'data': [['1703001720000', '50100.0', '51800.0', '50000.0', '51600.0', '2000.0']],
                    'ts': 1703001740000
                }
            ]
            
            trigger_count = 0
            
            print("📋 处理K线消息序列:")
            
            for i, message in enumerate(kline_messages):
                print(f"\n   处理K线消息 {i+1}:")
                
                # 提取价格信息
                kline_data = message['data'][0]
                close_price = float(kline_data[4])
                print(f"   收盘价: {close_price}")
                
                # 处理消息
                try:
                    monitor._on_message(message)
                    print(f"   ✅ K线消息处理成功")
                    
                    # 检查是否触发了企业微信消息
                    if mock_wechat.call_count > trigger_count:
                        trigger_count = mock_wechat.call_count
                        print(f"   🚀 触发了价格变化提示")
                    else:
                        print(f"   📊 未触发（正常）")
                        
                except Exception as e:
                    print(f"   ❌ K线消息处理失败: {str(e)}")
                    return False
            
            # 验证最终结果
            print(f"\n📊 最终统计:")
            print(f"   总触发次数: {trigger_count}")
            print(f"   价格历史长度: {len(monitor._price_history[trading_pair])}")
            
            # 应该至少触发一次（大幅变化）
            if trigger_count >= 1:
                print("   ✅ 大幅价格变化正确触发了提示")
            else:
                print("   ❌ 大幅价格变化应该触发提示")
                return False
        
        print("\n✅ 真实K线数据集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 真实K线数据集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 简化价格监控系统完整测试")
    print("="*60)
    print("测试内容：")
    print("1. 价格变化分析")
    print("2. 简化触发逻辑")
    print("3. 价格变化阈值配置")
    print("4. 真实K线数据集成")
    print("")
    
    test_results = []
    
    # 执行所有测试
    tests = [
        ("价格变化分析", test_price_change_analysis),
        ("简化触发逻辑", test_simplified_trigger_logic),
        ("价格变化阈值配置", test_price_change_thresholds),
        ("真实K线数据集成", test_real_kline_integration)
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "="*60)
    print("📊 简化价格监控系统测试总结")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 简化价格监控系统测试完成！")
        print("🎯 简化特性：")
        print("   ✅ 专注于价格变化区间监控")
        print("   ✅ 移除了复杂的市场深度和成交量指标")
        print("   ✅ 简化的触发阈值：1.5%(中等)、3.0%(大幅)、5.0%(极端)")
        print("   ✅ 保留了智能防重复推送机制")
        print("   ✅ 清晰的价格变化类型分类")
        print("\n🚀 现在系统专注于您的核心需求：监控价格变化，变化太大时触发提示！")
        print("💡 触发逻辑：")
        print("   - 小幅变化(0.5%-1.5%): 不触发")
        print("   - 中等变化(1.5%-3.0%): 触发提示")
        print("   - 大幅变化(3.0%-5.0%): 触发提示")
        print("   - 极端变化(>5.0%): 触发提示")
        return 0
    else:
        print(f"\n⚠️ {total-passed}个测试失败，请检查相关组件")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
