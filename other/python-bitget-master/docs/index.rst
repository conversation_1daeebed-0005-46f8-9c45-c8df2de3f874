Welcome to Python-bitget's documentation!
================================================

bitget is a cryptocurrency derivatives exchange.

If you're new to Bitget, use the following link to `save 10% on all of
your trade fees, and can get rewards up to
$5005. <https://partner.bitget.com/bg/e55g05831674816745836>`__

This is a wrapper around the Bitget API as described on Bitget, including all features the API provides using clear and readable objects, both for the REST as the websocket API.

Contents:

.. toctree::
   :maxdepth: 3

   overview
   spot
   mix
   broker
   websockets


Index
==================

* :ref:`genindex`
