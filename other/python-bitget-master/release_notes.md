# python-bitget

[Bitget](https://www.bitget.com/en/referral/register?from=referral&clacCode=6EKP94LE) 
python wrapper with rest API, websocket API.


# Release notes
* Version 1.0.8 - 08 July 2024.
  * [x] [Get History Candle Data](https://bitgetlimited.github.io/apidoc/en/mix/#get-history-candle-data)
* Version 1.0.7 - Fix bug.
* Version 1.0.6 - Fix bug.
* Version 1.0.5 - 02 Feb 2023.
  * Fixed bug [#5](https://github.com/cuongitl/python-bitget/issues/5)
* Version 1.0.4 - 30 Jan 2023.
  * Full support for all api-endpoint: spot/mix/broker
  * Update some functions by [Bitget Update log](https://bitgetlimited.github.io/apidoc/en/mix/#update-log)
* Version 1.0.3 - 29 Jan 2023.
  * Full support for all mix-endpoints.
* Version 1.0.2 - 29 Jan 2023.
  * Full support mix-endpoints: market, account
  * [x] [Get Depth](https://bitgetlimited.github.io/apidoc/en/mix/#get-depth)
  * [x] [Get Single Symbol Ticker](https://bitgetlimited.github.io/apidoc/en/mix/#get-single-symbol-ticker)
  * [x] [Get All Symbol Ticker](https://bitgetlimited.github.io/apidoc/en/mix/#get-all-symbol-ticker)
  * [x] [Get Candle Data](https://bitgetlimited.github.io/apidoc/en/mix/#get-candle-data)
  * [x] [Get Symbol Index Price](https://bitgetlimited.github.io/apidoc/en/mix/#get-symbol-index-price)
  * [x] [Get Symbol Next Funding Time](https://bitgetlimited.github.io/apidoc/en/mix/#get-symbol-next-funding-time)
  * [x] [Get History Funding Rate](https://bitgetlimited.github.io/apidoc/en/mix/#get-history-funding-rate)
  * [x] [Get Current Funding Rate](https://bitgetlimited.github.io/apidoc/en/mix/#get-current-funding-rate)
  * [x] [Get sub Account Contract Assets](https://bitgetlimited.github.io/apidoc/en/mix/#get-sub-account-contract-assets)
  * [x] [Get Open Count](https://bitgetlimited.github.io/apidoc/en/mix/#get-open-count)
  * [x] [Change Margin](https://bitgetlimited.github.io/apidoc/en/mix/#change-margin)
  * [x] [Change Hold Mode](https://bitgetlimited.github.io/apidoc/en/mix/#change-hold-mode)
  * [x] [Get Account Bill](https://bitgetlimited.github.io/apidoc/en/mix/#get-account-bill)
  * [x] [Get Business Account Bill](https://bitgetlimited.github.io/apidoc/en/mix/#get-business-account-bill)

* Version 1.0.1 - 28 Jan 2023.
  * Add some examples
* Version 1.0.0 - 27 Jan 2023. Supported:
    * [x] [Get Symbol Mark Price](https://bitgetlimited.github.io/apidoc/en/mix/#get-symbol-mark-price)
    * [x] [Get All Symbols](https://bitgetlimited.github.io/apidoc/en/mix/#get-all-symbols)
    * [x] [Get Single Account](https://bitgetlimited.github.io/apidoc/en/mix/#get-single-account)
    * [x] [Get Account List](https://bitgetlimited.github.io/apidoc/en/mix/#get-account-list)
    * [x] [Get Symbol Leverage](https://bitgetlimited.github.io/apidoc/en/mix/#get-symbol-leverage)
    * [x] [Get Symbol Position](https://bitgetlimited.github.io/apidoc/en/mix/#get-symbol-position)
    * [x] [Get All Position](https://bitgetlimited.github.io/apidoc/en/mix/#get-all-position)
    * [x] [Get Open Order](https://bitgetlimited.github.io/apidoc/en/mix/#get-open-order)
    * [x] [Get All Open Order](https://bitgetlimited.github.io/apidoc/en/mix/#get-all-open-order)
    * [x] [Get Plan Order (TPSL) List](https://bitgetlimited.github.io/apidoc/en/mix/#get-plan-order-tpsl-list)
    * [x] [Get Fills](https://bitgetlimited.github.io/apidoc/en/mix/#get-fills)
    * [x] [Get History Orders](https://bitgetlimited.github.io/apidoc/en/mix/#get-history-orders)
    * [x] [Get ProductType History Orders](https://bitgetlimited.github.io/apidoc/en/mix/#get-producttype-history-orders)
    * [x] [Change Leverage](https://bitgetlimited.github.io/apidoc/en/mix/#change-leverage)
    * [x] [Change Margin Mode](https://bitgetlimited.github.io/apidoc/en/mix/#change-margin-mode)
    * [x] [Place Order](https://bitgetlimited.github.io/apidoc/en/mix/#place-order)
    * [x] [Cancel All Order](https://bitgetlimited.github.io/apidoc/en/mix/#cancel-all-order)
    * [x] [Cancel Order](https://bitgetlimited.github.io/apidoc/en/mix/#cancel-order)
    * [x] [Cancel Plan Order (TPSL)](https://bitgetlimited.github.io/apidoc/en/mix/#cancel-plan-order-tpsl)
    * [x] [Place Position TPSL](https://bitgetlimited.github.io/apidoc/en/mix/#place-position-tpsl)