#!/usr/bin/env python3
"""
优化后的价格监控触发逻辑测试
验证智能防重复推送机制的集成效果
"""

import sys
import json
import time
from pathlib import Path
from datetime import datetime, timedelta
from collections import deque

# 添加项目路径
sys.path.append(str(Path(__file__).parent / "src"))


def test_smart_trigger_integration():
    """测试智能触发逻辑集成"""
    print("🧠 测试智能触发逻辑集成...")
    print("="*50)
    
    try:
        from price_monitor import PriceMonitor
        
        # 创建价格监控实例
        monitor = PriceMonitor()
        print("✅ 价格监控系统创建成功")
        
        # 检查智能防重复推送组件
        required_attrs = [
            '_last_trigger_times',
            '_last_trigger_prices', 
            '_trigger_counts',
            '_success_rates',
            '_trigger_config'
        ]
        
        missing_attrs = []
        for attr in required_attrs:
            if not hasattr(monitor, attr):
                missing_attrs.append(attr)
        
        if missing_attrs:
            print(f"❌ 缺少智能防重复组件: {missing_attrs}")
            return False
        
        print("✅ 智能防重复推送组件已正确集成")
        
        # 检查配置
        config = monitor._trigger_config
        print(f"📋 智能触发配置:")
        print(f"   基础冷却时间: {config['base_cooldown_minutes']}分钟")
        print(f"   价格相似度阈值: {config['price_similarity_threshold']:.3%}")
        print(f"   自适应冷却: {config['adaptive_cooldown']}")
        print(f"   时间窗口限制: {config['time_window_limits']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 智能触发逻辑集成测试失败: {str(e)}")
        return False


def test_trigger_cooldown_logic():
    """测试触发冷却逻辑"""
    print("\n⏰ 测试触发冷却逻辑...")
    print("="*50)
    
    try:
        from price_monitor import PriceMonitor
        
        monitor = PriceMonitor()
        trading_pair = "BTC"
        
        # 测试首次触发
        print("📋 测试首次触发检查...")
        cooldown_check = monitor._check_cooldown_time(trading_pair)
        print(f"   首次触发: {cooldown_check['allow']} - {cooldown_check['reason']}")
        
        # 模拟触发记录
        monitor._last_trigger_times[trading_pair] = datetime.now()
        
        # 测试立即重复触发
        print("\n📋 测试立即重复触发...")
        cooldown_check = monitor._check_cooldown_time(trading_pair)
        print(f"   立即重复: {cooldown_check['allow']} - {cooldown_check['reason']}")
        if not cooldown_check['allow']:
            print(f"   剩余时间: {cooldown_check.get('remaining_seconds', 0)}秒")
        
        # 测试冷却时间过后
        print("\n📋 测试冷却时间过后...")
        # 模拟时间过去
        monitor._last_trigger_times[trading_pair] = datetime.now() - timedelta(minutes=16)
        cooldown_check = monitor._check_cooldown_time(trading_pair)
        print(f"   冷却后: {cooldown_check['allow']} - {cooldown_check['reason']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 触发冷却逻辑测试失败: {str(e)}")
        return False


def test_price_similarity_logic():
    """测试价格相似性逻辑"""
    print("\n💰 测试价格相似性逻辑...")
    print("="*50)
    
    try:
        from price_monitor import PriceMonitor
        
        monitor = PriceMonitor()
        trading_pair = "BTC"
        base_price = 50000.0
        
        # 测试无历史价格
        print("📋 测试无历史价格...")
        similarity_check = monitor._check_price_similarity(trading_pair, base_price)
        print(f"   无历史价格: {similarity_check['allow']} - {similarity_check['reason']}")
        
        # 设置历史价格
        monitor._last_trigger_prices[trading_pair] = base_price
        
        # 测试相似价格
        print("\n📋 测试相似价格...")
        similar_price = base_price + 100  # 0.2%差异
        similarity_check = monitor._check_price_similarity(trading_pair, similar_price)
        print(f"   相似价格({similar_price}): {similarity_check['allow']} - {similarity_check['reason']}")
        
        # 测试不同价格
        print("\n📋 测试不同价格...")
        different_price = base_price + 500  # 1%差异
        similarity_check = monitor._check_price_similarity(trading_pair, different_price)
        print(f"   不同价格({different_price}): {similarity_check['allow']} - {similarity_check['reason']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 价格相似性逻辑测试失败: {str(e)}")
        return False


def test_adaptive_cooldown_logic():
    """测试自适应冷却逻辑"""
    print("\n🔄 测试自适应冷却逻辑...")
    print("="*50)
    
    try:
        from price_monitor import PriceMonitor
        
        monitor = PriceMonitor()
        trading_pair = "BTC"
        
        # 测试不同信号强度的冷却时间
        test_cases = [
            ("强信号", 0.95, 0.03),
            ("中等信号", 0.75, 0.02),
            ("弱信号", 0.6, 0.01),
        ]
        
        for case_name, signal_strength, volatility in test_cases:
            print(f"\n📋 测试{case_name}:")
            print(f"   信号强度: {signal_strength:.2%}")
            print(f"   市场波动率: {volatility:.2%}")
            
            # 计算自适应冷却时间
            adaptive_cooldown = monitor._calculate_adaptive_cooldown(trading_pair, signal_strength, volatility)
            print(f"   自适应冷却时间: {adaptive_cooldown}分钟")
            
            # 测试自适应冷却检查
            monitor._last_trigger_times[trading_pair] = datetime.now()
            adaptive_check = monitor._check_adaptive_cooldown(trading_pair, signal_strength, volatility)
            print(f"   自适应检查: {adaptive_check['allow']} - {adaptive_check['reason']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 自适应冷却逻辑测试失败: {str(e)}")
        return False


def test_comprehensive_trigger_logic():
    """测试综合触发逻辑"""
    print("\n🎯 测试综合触发逻辑...")
    print("="*50)
    
    try:
        from price_monitor import PriceMonitor
        from unittest.mock import Mock
        
        monitor = PriceMonitor()
        trading_pair = "BTC"
        current_price = 50000.0
        
        # 初始化必要的数据结构
        monitor._price_history[trading_pair] = deque([49800, 49900, 50000], maxlen=100)
        monitor._price_levels[trading_pair] = Mock()
        monitor._price_levels[trading_pair].get_levels = Mock(return_value={
            'support_levels': [49000, 49500],
            'resistance_levels': [50500, 51000],
            'ma_values': {'ma5': 50000, 'ma10': 49800, 'ma20': 49600, 'ma50': 49400}
        })
        monitor._kline_data[trading_pair] = Mock()
        
        # 模拟市场分析器
        monitor._market_analyzer.analyze_trend = Mock(return_value={'1h': 'bullish', '4h': 'bullish'})
        monitor._market_analyzer.analyze_volume = Mock(return_value={'volume_surge': True})
        monitor._market_analyzer.analyze_technical_indicators = Mock(return_value={'rsi': 65, 'macd': 'bullish'})
        monitor._market_analyzer.analyze_market_depth = Mock(return_value={'liquidity': 'good'})
        monitor._calculate_comprehensive_score = Mock(return_value=75)  # 高于50的触发阈值
        
        print("📋 测试综合触发逻辑...")
        
        # 第一次触发（应该成功）
        print("\n   第一次触发测试:")
        should_trigger_1 = monitor._should_trigger_agent(trading_pair, current_price)
        print(f"   触发结果: {should_trigger_1}")
        
        if should_trigger_1:
            print(f"   触发次数: {monitor._trigger_counts[trading_pair]}")
            print(f"   最后触发时间: {monitor._last_trigger_times[trading_pair]}")
            print(f"   最后触发价格: {monitor._last_trigger_prices[trading_pair]}")
        
        # 立即重复触发（应该被阻止）
        print("\n   立即重复触发测试:")
        should_trigger_2 = monitor._should_trigger_agent(trading_pair, current_price + 50)
        print(f"   触发结果: {should_trigger_2}")
        
        # 获取统计信息
        print("\n📊 触发统计信息:")
        stats = monitor.get_trigger_statistics(trading_pair)
        for key, value in stats.items():
            if key != 'trading_pair':
                print(f"   {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 综合触发逻辑测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_trigger_statistics():
    """测试触发统计功能"""
    print("\n📊 测试触发统计功能...")
    print("="*50)
    
    try:
        from price_monitor import PriceMonitor
        
        monitor = PriceMonitor()
        
        # 模拟一些触发记录
        test_pairs = ["BTC", "ETH", "SOL"]
        for i, pair in enumerate(test_pairs):
            monitor._trigger_counts[pair] = i + 1
            monitor._last_trigger_times[pair] = datetime.now() - timedelta(minutes=i*5)
            monitor._last_trigger_prices[pair] = 50000 + i * 1000
            monitor._success_rates[pair] = [True, False, True] if i == 0 else [True, True]
        
        # 测试特定交易对统计
        print("📋 测试特定交易对统计:")
        for pair in test_pairs:
            stats = monitor.get_trigger_statistics(pair)
            print(f"   {pair}: 触发{stats['trigger_count']}次, 成功率{stats['recent_success_rate']:.2%}")
        
        # 测试全局统计
        print("\n📋 测试全局统计:")
        global_stats = monitor.get_trigger_statistics()
        print(f"   总触发次数: {global_stats['total_triggers']}")
        print(f"   活跃交易对: {global_stats['active_pairs']}")
        print(f"   平均成功率: {global_stats['average_success_rate']:.2%}")
        
        # 测试成功率更新
        print("\n📋 测试成功率更新:")
        monitor.update_trigger_success("BTC", True)
        updated_stats = monitor.get_trigger_statistics("BTC")
        print(f"   BTC更新后成功率: {updated_stats['recent_success_rate']:.2%}")
        
        return True
        
    except Exception as e:
        print(f"❌ 触发统计功能测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("🚀 优化后的价格监控触发逻辑完整测试")
    print("="*60)
    print("测试内容：")
    print("1. 智能触发逻辑集成")
    print("2. 触发冷却逻辑")
    print("3. 价格相似性逻辑")
    print("4. 自适应冷却逻辑")
    print("5. 综合触发逻辑")
    print("6. 触发统计功能")
    print("")
    
    test_results = []
    
    # 执行所有测试
    tests = [
        ("智能触发逻辑集成", test_smart_trigger_integration),
        ("触发冷却逻辑", test_trigger_cooldown_logic),
        ("价格相似性逻辑", test_price_similarity_logic),
        ("自适应冷却逻辑", test_adaptive_cooldown_logic),
        ("综合触发逻辑", test_comprehensive_trigger_logic),
        ("触发统计功能", test_trigger_statistics)
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "="*60)
    print("📊 优化后的价格监控触发逻辑测试总结")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 价格监控触发逻辑优化完成！")
        print("🎯 优化特性：")
        print("   ✅ 保持原有WebSocket数据处理")
        print("   ✅ 保持原有技术分析逻辑")
        print("   ✅ 集成智能防重复推送机制")
        print("   ✅ 自适应冷却时间调整")
        print("   ✅ 价格相似性过滤")
        print("   ✅ 多维度时间窗口限制")
        print("   ✅ 完整的统计跟踪")
        print("\n🚀 您的价格监控系统现在具备专业级的智能触发能力！")
        print("💡 核心优势：")
        print("   - WebSocket实时数据 + 智能防重复")
        print("   - 基于信号强度和成功率的自适应优化")
        print("   - 完整的触发历史和统计分析")
        return 0
    else:
        print(f"\n⚠️ {total-passed}个测试失败，请检查相关组件")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
