#!/usr/bin/env python3
"""
全面优化功能测试脚本
验证复盘功能、数据保存、工具分配和交易准则的完整性
"""

import sys
import json
import time
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.append(str(Path(__file__).parent / "src"))

from gold_agents.tools.trading_review_tool import TradingReviewTool
from gold_agents.tools.trading_data_manager import TradingDataManager
from gold_agents.tools.trading_rules_engine import TradingRulesEngine
from gold_agents.crew import GoldAgents


def test_trading_review_functionality():
    """测试复盘功能"""
    print("🔄 测试复盘功能...")
    
    try:
        review_tool = TradingReviewTool()
        
        # 模拟保存一些Agent决策记录
        session_id = "test_session_review_001"
        
        # 保存Agent决策
        review_tool.save_agent_decision(
            session_id=session_id,
            agent_name="trader",
            task_name="execute_trade",
            input_data={
                "trading_pair": "BTCUSDT",
                "strategy_signal": "long",
                "signal_strength": 0.8
            },
            output_data={
                "action": "open_long",
                "size": 0.1,
                "price": 50000,
                "stop_loss": 49000,
                "take_profit": 51000
            },
            execution_time=1.2,
            success=True
        )
        
        # 保存交易记录
        review_tool.save_trading_record(
            session_id=session_id,
            strategy_id=1,
            trading_pair="BTCUSDT",
            action="open_long",
            order_type="limit",
            entry_price=50000,
            size=0.1,
            stop_loss=49000,
            take_profit=51000,
            pnl=100.0,
            commission=5.0,
            entry_time=datetime.now(),
            status="closed"
        )
        
        # 执行日度复盘
        review_result = review_tool.run(
            action="daily_review",
            start_date=datetime.now().strftime('%Y-%m-%d'),
            trading_pair="BTCUSDT"
        )
        
        print("✅ 复盘功能测试成功")
        print(f"   - 复盘状态: {review_result['status']}")
        if review_result['status'] == 'success':
            report = review_result['report']
            print(f"   - 交易统计: {report['trading_stats']['total_trades']}笔交易")
            print(f"   - 胜率: {report['trading_stats']['win_rate']:.2%}")
            print(f"   - 关键洞察: {len(report['key_insights'])}条")
            print(f"   - 改进建议: {len(report['improvement_suggestions'])}条")
        
        return True
        
    except Exception as e:
        print(f"❌ 复盘功能测试失败: {str(e)}")
        return False


def test_trading_data_management():
    """测试交易数据保存功能"""
    print("\n💾 测试交易数据保存功能...")
    
    try:
        data_manager = TradingDataManager()
        
        # 创建新的交易会话
        session_result = data_manager.run(
            action="start_session",
            data={
                "trading_pair": "BTCUSDT",
                "initial_balance": 10000.0,
                "metadata": {"test": "comprehensive_optimization"}
            }
        )
        
        if session_result["status"] != "success":
            raise Exception("创建交易会话失败")
        
        session_id = session_result["session_id"]
        print(f"✅ 交易会话创建成功: {session_id}")
        
        # 保存Agent输出
        agent_result = data_manager.run(
            action="save_agent_output",
            session_id=session_id,
            data={
                "agent_name": "trader",
                "task_name": "analyze_market",
                "input_data": {"market_condition": "bullish"},
                "output_data": {"recommendation": "open_long", "confidence": 0.8},
                "execution_time": 2.1,
                "success": True
            }
        )
        
        # 保存交易记录
        trade_result = data_manager.run(
            action="save_trade",
            session_id=session_id,
            data={
                "strategy_id": 1,
                "order_id": "test_order_001",
                "trading_pair": "BTCUSDT",
                "action": "open_long",
                "order_type": "limit",
                "entry_price": 50000,
                "size": 0.1,
                "leverage": 3,
                "stop_loss": 49000,
                "take_profit": 51000,
                "status": "filled",
                "agent_decisions": {"trader": "bullish_signal", "risk_controller": "approved"}
            }
        )
        
        # 保存风险事件
        risk_result = data_manager.run(
            action="save_risk_event",
            session_id=session_id,
            data={
                "event_type": "position_limit_check",
                "severity": "low",
                "description": "仓位检查通过",
                "triggered_by": "risk_controller",
                "action_taken": "继续交易",
                "impact": 0.0
            }
        )
        
        # 获取会话数据
        session_data = data_manager.run(
            action="get_session_data",
            session_id=session_id
        )
        
        print("✅ 交易数据保存功能测试成功")
        if session_data["status"] == "success":
            data = session_data["session_data"]
            print(f"   - Agent输出记录: {len(data['agent_outputs'])}条")
            print(f"   - 交易记录: {len(data['trades'])}条")
            print(f"   - 风险事件: {len(data['risk_events'])}条")
        
        # 结束会话
        end_result = data_manager.run(
            action="end_session",
            session_id=session_id,
            data={"final_balance": 10100.0, "status": "completed"}
        )
        
        return True
        
    except Exception as e:
        print(f"❌ 交易数据保存功能测试失败: {str(e)}")
        return False


def test_trading_rules_engine():
    """测试交易准则引擎"""
    print("\n📏 测试交易准则引擎...")
    
    try:
        rules_engine = TradingRulesEngine()
        
        # 测试交易验证
        trade_data = {
            "action": "open_long",
            "size": 0.1,
            "price": 50000,
            "stop_loss": 49000,
            "take_profit": 51000,
            "signal_strength": 0.8,
            "confidence": 0.75,
            "leverage": 3,
            "strategy_type": "momentum"
        }
        
        account_data = {
            "balance": 10000,
            "position_value": 1000,
            "margin_ratio": 0.6,
            "daily_pnl": -200,
            "consecutive_losses": 1,
            "win_rate": 0.65,
            "profit_factor": 2.1,
            "max_drawdown": 0.03
        }
        
        market_data = {
            "depth": 150000,
            "volatility": 0.03,
            "price_change_24h": -0.02
        }
        
        # 验证交易决策
        validation_result = rules_engine.run(
            action="validate_trade",
            trade_data=trade_data,
            account_data=account_data,
            market_data=market_data
        )
        
        print("✅ 交易验证测试成功")
        if validation_result["status"] == "success":
            print(f"   - 交易允许: {validation_result['is_allowed']}")
            print(f"   - 违规项目: {len(validation_result['violations'])}个")
            print(f"   - 警告项目: {len(validation_result['warnings'])}个")
            print(f"   - 风险评分: {validation_result['risk_score']:.1f}/100")
            print(f"   - 改进建议: {len(validation_result['recommendations'])}条")
        
        # 检查风险合规性
        risk_check = rules_engine.run(
            action="check_risk",
            account_data=account_data,
            market_data=market_data
        )
        
        print("✅ 风险合规检查成功")
        if risk_check["status"] == "success":
            print(f"   - 风险等级: {risk_check['risk_level']}")
            print(f"   - 合规状态: {risk_check['compliance_status']}")
            print(f"   - 风险问题: {len(risk_check['risk_issues'])}个")
        
        # 评估绩效表现
        performance_eval = rules_engine.run(
            action="evaluate_performance",
            account_data=account_data
        )
        
        print("✅ 绩效评估测试成功")
        if performance_eval["status"] == "success":
            print(f"   - 绩效等级: {performance_eval['performance_grade']}")
            print(f"   - 绩效问题: {len(performance_eval['performance_issues'])}个")
            print(f"   - 改进建议: {len(performance_eval['improvement_suggestions'])}条")
        
        # 应急程序检查
        emergency_check = rules_engine.run(
            action="emergency_check",
            account_data=account_data,
            market_data=market_data
        )
        
        print("✅ 应急程序检查成功")
        if emergency_check["status"] == "success":
            print(f"   - 应急触发: {emergency_check['emergency_triggered']}")
            print(f"   - 严重程度: {emergency_check['severity']}")
            print(f"   - 应急措施: {len(emergency_check['emergency_actions'])}项")
        
        return True
        
    except Exception as e:
        print(f"❌ 交易准则引擎测试失败: {str(e)}")
        return False


def test_optimized_tool_allocation():
    """测试优化后的工具分配"""
    print("\n⚖️ 测试优化后的工具分配...")
    
    try:
        # 创建Gold Agents实例
        gold_agents = GoldAgents()
        
        # 检查每个Agent的工具分配
        agents_info = {
            "trader": {
                "expected_tools": ["StrategyTool", "BitgetMarketTool", "BitgetAccountTool", 
                                 "TradingRulesEngine", "TradingDataManager", "TradingReviewTool"],
                "agent": gold_agents.trader()
            },
            "position_manager": {
                "expected_tools": ["BitgetAccountTool", "BitgetRiskTool", "StrategyTool",
                                 "TradingDataManager", "TradingReviewTool"],
                "agent": gold_agents.position_manager()
            },
            "risk_controller": {
                "expected_tools": ["TradingRulesEngine", "BitgetRiskTool", "BitgetMarketTool",
                                 "BitgetAccountTool", "TradingDataManager", "TradingReviewTool"],
                "agent": gold_agents.risk_controller()
            },
            "order_executor": {
                "expected_tools": ["BitgetTradeTool", "BitgetOrderTool", "BitgetMarketTool",
                                 "TradingDataManager", "BitgetAccountTool"],
                "agent": gold_agents.order_executor()
            }
        }
        
        all_tools_correct = True
        
        for agent_name, info in agents_info.items():
            agent = info["agent"]
            expected_tools = info["expected_tools"]
            actual_tools = [tool.__class__.__name__ for tool in agent.tools]
            
            print(f"   📊 {agent_name}:")
            print(f"      - 预期工具数: {len(expected_tools)}")
            print(f"      - 实际工具数: {len(actual_tools)}")
            
            # 检查工具是否匹配
            missing_tools = []
            for expected_tool in expected_tools:
                if not any(expected_tool in actual_tool for actual_tool in actual_tools):
                    missing_tools.append(expected_tool)
            
            if missing_tools:
                print(f"      ❌ 缺少工具: {missing_tools}")
                all_tools_correct = False
            else:
                print(f"      ✅ 工具分配正确")
        
        # 测试系统健康检查
        health_status = gold_agents.health_check()
        print(f"\n   🏥 系统健康检查: {health_status['overall_status']}")
        
        # 测试系统指标
        metrics = gold_agents.get_crew_metrics()
        print(f"   📊 系统指标: {len(metrics.get('tools_metrics', {}))}个工具")
        
        # 清理资源
        gold_agents.close()
        
        if all_tools_correct:
            print("✅ 工具分配优化测试成功")
            return True
        else:
            print("❌ 工具分配存在问题")
            return False
        
    except Exception as e:
        print(f"❌ 工具分配测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始全面优化功能测试...\n")
    print("="*60)
    
    test_results = []
    
    # 执行所有测试
    tests = [
        ("复盘功能", test_trading_review_functionality),
        ("数据保存功能", test_trading_data_management),
        ("交易准则引擎", test_trading_rules_engine),
        ("工具分配优化", test_optimized_tool_allocation)
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "="*60)
    print("📊 全面优化功能测试总结")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有功能测试通过！")
        print("🎯 系统已完成全面优化，具备以下能力：")
        print("   ✅ 完整的复盘分析功能")
        print("   ✅ 自动化交易数据保存")
        print("   ✅ 优化的Agent工具分配")
        print("   ✅ 严格的交易准则执行")
        print("   ✅ 智能风险控制机制")
        print("   ✅ 持续的绩效监控评估")
        print("\n🚀 系统已准备好进行实盘交易！")
        return 0
    else:
        print(f"\n⚠️ {total-passed}个功能测试失败，请检查相关组件")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
