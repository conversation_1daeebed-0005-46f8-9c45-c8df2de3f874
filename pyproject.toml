[project]
name = "gold_agents"
version = "0.1.0"
description = "gold-agents using crewAI"
authors = [{ name = "Your Name", email = "<EMAIL>" }]
requires-python = ">=3.10,<3.13"
dependencies = [
    "crewai[tools]>=0.102.0,<1.0.0",
    "akshare>=1.10.30,<2.0.0", # For Jin10 data and potentially K-line # For potential API calls / WeCom notifications
    "requests>=2.31.0,<3.0.0",
    "pymysql>=1.1.1",
    "pandas-ta>=0.3.14b0",
    "python-bitget>=1.0.8",
]

[project.scripts]
gold_agents = "gold_agents.main:run"
run_crew = "gold_agents.main:run"
train = "gold_agents.main:train"
replay = "gold_agents.main:replay"
test = "gold_agents.main:test"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.crewai]
type = "crew"
