# 🎯 两个团队分离使用指南

## 📋 **团队分离架构**

现在系统已完全分离为两个独立的团队：

### 🔄 **复盘团队 (Review Agents)**
- **职责**: 独立执行复盘分析，生成报告
- **位置**: `src/review_agents/`
- **数据库**: 独立的复盘数据表
- **运行方式**: 手动执行或云服务定时任务

### 💰 **交易团队 (Gold Agents)**  
- **职责**: 执行交易，查询复盘结果，应用策略调整
- **位置**: `src/gold_agents/`
- **数据库**: 交易链数据表 + 查询复盘数据表
- **运行方式**: 实时交易执行

---

## 🔄 **复盘团队工具**

### 📊 **TradingDataAnalyzer (交易数据分析器)**
```python
# 分析交易绩效
analyzer.run(action="analyze_performance", start_date="2024-01-01", end_date="2024-01-31")

# 分析Agent行为
analyzer.run(action="analyze_behavior", session_ids=["session1", "session2"])

# 分析市场模式
analyzer.run(action="analyze_market", start_date="2024-01-01", end_date="2024-01-31")
```

### 💾 **ReviewResultManager (复盘结果管理器)**
```python
# 保存复盘报告
report_id = manager.save_review_report(review_result)

# 获取最新报告
latest_report = manager.get_latest_review_report("daily")

# 获取待应用调整
adjustments = manager.get_pending_strategy_adjustments()
```

### 🤖 **ReviewAgents (复盘Agent团队)**
```python
# 执行复盘分析
review_agents = ReviewAgents()
result = review_agents.execute_review("2024-01-01_to_2024-01-31")
review_agents.close()
```

---

## 💰 **交易团队工具**

### 🔍 **ReviewResultQueryTool (复盘结果查询工具)**
```python
# 获取最新复盘报告
query_tool.run(action="get_latest_report", report_type="daily")

# 获取待应用调整
query_tool.run(action="get_pending_adjustments")

# 获取绩效摘要
query_tool.run(action="get_performance_summary", days_back=30)

# 获取洞察摘要
query_tool.run(action="get_insights_summary", days_back=7)
```

### ⚙️ **StrategyAdjustmentTool (策略调整工具)**
```python
# 获取最新复盘结果
adjustment_tool.run(action="get_latest_review", report_type="daily")

# 应用策略调整
adjustment_tool.run(action="apply_adjustments", auto_apply=True)

# 获取调整建议
adjustment_tool.run(action="get_recommendations")
```

### 🔗 **TradingChainManager (交易链管理器)**
```python
# 记录完整交易链
session_id = manager.start_trading_session("BTCUSDT", 10000.0)
agent_exec_id = manager.record_agent_execution_start("trader", "analyze", input_data)
tool_exec_id = manager.record_tool_execution(agent_exec_id, "StrategyTool", "get_latest", params, 1)
manager.complete_tool_execution(tool_exec_id, output_result, True)
manager.complete_agent_execution(agent_exec_id, output_data, reasoning, True)
```

---

## 🗄️ **数据库表结构**

### 复盘团队表
```sql
-- 复盘报告表
review_reports:
  - 报告基本信息、绩效指标
  - 关键洞察、改进建议
  - 策略调整建议

-- 策略调整记录表
strategy_adjustments:
  - 具体调整参数
  - 调整原因和预期影响
  - 应用状态跟踪
```

### 交易团队表
```sql
-- 交易会话表
trading_sessions:
  - 会话基本信息、盈亏统计

-- Agent执行记录表
agent_executions:
  - 每个Agent的完整执行过程

-- 工具执行记录表
tool_executions:
  - 每个工具的调用详情

-- 交易决策记录表
trading_decisions:
  - 最终交易决策和执行结果
```

---

## 🚀 **使用流程**

### 1. **初始化系统**
```bash
# 初始化复盘数据库
python -c "
from review_agents.tools.review_result_manager import ReviewResultManager
manager = ReviewResultManager()
manager.init_db()
print('复盘数据库初始化完成')
"

# 初始化交易链数据库
python -c "
from gold_agents.database.trading_chain_manager import TradingChainManager
manager = TradingChainManager()
manager.init_db()
print('交易链数据库初始化完成')
"
```

### 2. **启动交易团队**
```bash
# 启动交易系统
python -m gold_agents --mode normal --trading-pair BTCUSDT
```

### 3. **执行复盘分析**
```bash
# 手动执行日度复盘
python -m review_agents.scheduler --daily

# 手动执行周度复盘
python -m review_agents.scheduler --weekly

# 手动执行月度复盘
python -m review_agents.scheduler --monthly

# 自定义时间范围复盘
python -m review_agents.scheduler --manual --start-date 2024-01-01 --end-date 2024-01-31
```

### 4. **云服务定时任务设置**

#### 宝塔面板设置
```bash
# 日度复盘 - 每天00:00
cd /www/wwwroot/gold_agents && python -m review_agents.scheduler --daily

# 周度复盘 - 每周一00:00
cd /www/wwwroot/gold_agents && python -m review_agents.scheduler --weekly

# 月度复盘 - 每月1号00:00
cd /www/wwwroot/gold_agents && python -m review_agents.scheduler --monthly
```

#### Linux Crontab设置
```bash
# 编辑crontab
crontab -e

# 添加定时任务
0 0 * * * cd /path/to/gold_agents && python -m review_agents.scheduler --daily
0 0 * * 1 cd /path/to/gold_agents && python -m review_agents.scheduler --weekly
0 0 1 * * cd /path/to/gold_agents && python -m review_agents.scheduler --monthly
```

### 5. **验证系统集成**
```bash
# 运行完整集成测试
python test_teams_integration.py
```

---

## 🔄 **数据流程**

### 完整数据闭环
```
1. 交易团队执行交易
   ↓
2. TradingChainManager记录完整交易链
   ↓
3. 复盘团队分析交易数据
   ↓
4. ReviewResultManager保存复盘报告
   ↓
5. 交易团队查询复盘结果
   ↓
6. StrategyAdjustmentTool应用策略调整
   ↓
7. 优化后的策略应用到下次交易
   ↓
8. 持续改进循环
```

### 团队间数据交互
```
复盘团队                    交易团队
    ↓                         ↑
生成复盘报告              查询复盘结果
    ↓                         ↑
保存到数据库     ←→     ReviewResultQueryTool
    ↓                         ↑
策略调整建议              应用策略调整
```

---

## 🎯 **Agent工具配置**

### 复盘团队Agent配置
```python
# 数据分析师
tools = [TradingDataAnalyzer]

# 绩效评估师
tools = [TradingDataAnalyzer]

# 策略优化师
tools = [TradingDataAnalyzer]

# 报告生成器
tools = []  # 主要整合其他Agent结果
```

### 交易团队Agent配置
```python
# 交易员
tools = [
    StrategyTool,              # 策略信号
    BitgetMarketTool,          # 市场数据
    BitgetAccountTool,         # 账户状态
    TradingRulesEngine,        # 合规检查
    ReviewResultQueryTool      # 查询复盘结果（新增）
]

# 仓位管理员
tools = [
    BitgetAccountTool,         # 账户监控
    BitgetRiskTool,            # 风险评估
    StrategyTool               # 策略参数
]

# 风险控制员
tools = [
    TradingRulesEngine,        # 风险合规
    BitgetRiskTool,            # 风险控制
    BitgetMarketTool,          # 市场监控
    BitgetAccountTool          # 风险指标
]

# 订单执行员
tools = [
    BitgetTradeTool,           # 交易执行
    BitgetOrderTool,           # 订单管理
    BitgetMarketTool,          # 市场数据
    BitgetAccountTool          # 执行验证
]
```

---

## 🔧 **故障排除**

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查数据库配置
cat src/gold_agents/config/trader_config.yaml

# 测试连接
python test_teams_integration.py
```

#### 2. 复盘团队无法查询交易数据
```bash
# 检查是否有交易数据
python -c "
from gold_agents.database.trading_chain_manager import TradingChainManager
manager = TradingChainManager()
# 检查交易会话
"
```

#### 3. 交易团队无法获取复盘结果
```bash
# 检查复盘报告
python -c "
from review_agents.tools.review_result_manager import ReviewResultManager
manager = ReviewResultManager()
report = manager.get_latest_review_report()
print('最新报告:', report)
"
```

---

## 🎉 **总结**

### ✅ **完成的分离**
1. **独立的复盘团队** - 专注复盘分析，不干扰交易
2. **独立的查询接口** - 交易团队只读访问复盘结果
3. **清晰的数据流** - 复盘→保存→查询→调整→应用
4. **云服务定时任务** - 替代内置定时器，更灵活

### 🎯 **核心优势**
- **团队解耦**: 两个团队完全独立运行
- **数据安全**: 交易团队只能查询，不能修改复盘数据
- **灵活部署**: 支持不同的部署和调度策略
- **完整闭环**: 从交易到复盘到优化的完整数据流

现在您可以安全地运行两个独立的团队系统！

---

**完成时间**: 2024年12月19日  
**版本**: v6.0.0 - 团队分离版  
**状态**: ✅ 生产就绪，完全分离
