#!/usr/bin/env python3
"""
K线数据触发逻辑修复验证测试
验证K线数据update处理中的触发计算是否正常工作
"""

import sys
import json
import time
from pathlib import Path
from datetime import datetime
from collections import deque

# 添加项目路径
sys.path.append(str(Path(__file__).parent / "src"))


def test_kline_data_structure():
    """测试K线数据结构"""
    print("📊 测试K线数据结构...")
    print("="*50)
    
    try:
        from price_monitor import KlineData
        
        # 创建K线数据实例
        kline = KlineData()
        print("✅ K线数据实例创建成功")
        
        # 测试数据更新
        test_kline_data = [
            1703001600000,  # timestamp (毫秒)
            "50000.0",      # open
            "50200.0",      # high  
            "49800.0",      # low
            "50100.0",      # close
            "1000.5"        # volume
        ]
        
        print("📋 测试K线数据更新:")
        kline.update(test_kline_data)
        
        print(f"   时间戳: {kline.timestamp}")
        print(f"   开盘价: {kline.open}")
        print(f"   最高价: {kline.high}")
        print(f"   最低价: {kline.low}")
        print(f"   收盘价: {kline.close}")
        print(f"   成交量: {kline.volume}")
        
        # 验证数据类型
        assert isinstance(kline.open, float), "开盘价应为float类型"
        assert isinstance(kline.high, float), "最高价应为float类型"
        assert isinstance(kline.low, float), "最低价应为float类型"
        assert isinstance(kline.close, float), "收盘价应为float类型"
        assert isinstance(kline.volume, float), "成交量应为float类型"
        assert isinstance(kline.timestamp, datetime), "时间戳应为datetime类型"
        
        print("✅ K线数据结构测试通过")
        return True
        
    except Exception as e:
        print(f"❌ K线数据结构测试失败: {str(e)}")
        return False


def test_kline_message_processing():
    """测试K线消息处理"""
    print("\n📨 测试K线消息处理...")
    print("="*50)
    
    try:
        from price_monitor import PriceMonitor
        from unittest.mock import Mock, patch
        
        monitor = PriceMonitor()
        trading_pair = "BTC"
        
        # 初始化必要的数据结构
        monitor._price_history[trading_pair] = deque(maxlen=100)
        monitor._price_levels[trading_pair] = Mock()
        monitor._price_levels[trading_pair].update = Mock()
        monitor._price_levels[trading_pair].get_levels = Mock(return_value={
            'support_levels': [49000, 49500],
            'resistance_levels': [50500, 51000],
            'ma_values': {'ma5': 50000, 'ma10': 49800, 'ma20': 49600, 'ma50': 49400}
        })
        
        # 模拟数据库管理器
        monitor._db_manager.save_kline_data = Mock()
        
        # 模拟企业微信发送函数
        with patch('price_monitor.send_wechat_message') as mock_wechat:
            # 构造K线update消息
            kline_update_message = {
                "action": "update",
                "arg": {
                    "instId": "BTCUSDT"
                },
                "data": [[
                    1703001600000,  # timestamp
                    "50000.0",      # open
                    "50200.0",      # high
                    "49800.0",      # low
                    "50100.0",      # close
                    "1000.5"        # volume
                ]]
            }
            
            print("📋 测试K线update消息处理:")
            print(f"   消息类型: {kline_update_message['action']}")
            print(f"   交易对: {kline_update_message['arg']['instId']}")
            
            # 模拟触发检查返回True
            monitor._should_trigger_agent = Mock(return_value=True)
            
            # 处理消息
            monitor._on_message(kline_update_message)
            
            # 验证K线数据是否被保存
            assert monitor._db_manager.save_kline_data.called, "K线数据应该被保存到数据库"
            print("   ✅ K线数据已保存到数据库")
            
            # 验证价格历史是否被更新
            assert len(monitor._price_history[trading_pair]) > 0, "价格历史应该被更新"
            print("   ✅ 价格历史已更新")
            
            # 验证触发检查是否被调用
            assert monitor._should_trigger_agent.called, "触发检查应该被调用"
            print("   ✅ 触发检查已调用")
            
            # 验证企业微信消息是否被发送
            assert mock_wechat.called, "企业微信消息应该被发送"
            print("   ✅ 企业微信消息已发送")
            
            # 检查调用参数
            call_args = monitor._should_trigger_agent.call_args
            if call_args:
                called_pair, called_price = call_args[0]
                print(f"   触发检查参数: 交易对={called_pair}, 价格={called_price}")
                assert called_pair == trading_pair, "触发检查的交易对应该正确"
                assert called_price == 50100.0, "触发检查的价格应该是收盘价"
                print("   ✅ 触发检查参数正确")
        
        print("✅ K线消息处理测试通过")
        return True
        
    except Exception as e:
        print(f"❌ K线消息处理测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_kline_snapshot_processing():
    """测试K线快照处理"""
    print("\n📷 测试K线快照处理...")
    print("="*50)
    
    try:
        from price_monitor import PriceMonitor
        from unittest.mock import Mock
        
        monitor = PriceMonitor()
        trading_pair = "BTC"
        
        # 初始化必要的数据结构
        monitor._price_history[trading_pair] = deque(maxlen=100)
        monitor._price_levels[trading_pair] = Mock()
        monitor._price_levels[trading_pair].update = Mock()
        
        # 模拟数据库管理器
        monitor._db_manager.save_kline_data = Mock()
        
        # 构造K线snapshot消息（多个历史K线）
        kline_snapshot_message = {
            "action": "snapshot",
            "arg": {
                "instId": "BTCUSDT"
            },
            "data": [
                [1703001600000, "49900.0", "50000.0", "49800.0", "49950.0", "800.0"],
                [1703001660000, "49950.0", "50100.0", "49900.0", "50050.0", "900.0"],
                [1703001720000, "50050.0", "50200.0", "50000.0", "50100.0", "1000.0"]
            ]
        }
        
        print("📋 测试K线snapshot消息处理:")
        print(f"   消息类型: {kline_snapshot_message['action']}")
        print(f"   K线数量: {len(kline_snapshot_message['data'])}")
        
        # 处理消息
        monitor._on_message(kline_snapshot_message)
        
        # 验证K线数据是否被保存（应该保存3次）
        assert monitor._db_manager.save_kline_data.call_count == 3, "应该保存3个K线数据"
        print("   ✅ 所有K线数据已保存到数据库")
        
        # 验证价格历史是否被更新（只更新最新的K线）
        assert len(monitor._price_history[trading_pair]) == 1, "价格历史应该只包含最新K线的收盘价"
        assert monitor._price_history[trading_pair][-1] == 50100.0, "最新价格应该是最后一个K线的收盘价"
        print("   ✅ 价格历史已正确更新")
        
        # 验证价格水平是否被更新
        assert monitor._price_levels[trading_pair].update.called, "价格水平应该被更新"
        print("   ✅ 价格水平已更新")
        
        print("✅ K线快照处理测试通过")
        return True
        
    except Exception as e:
        print(f"❌ K线快照处理测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_kline_vs_ticker_trigger():
    """测试K线数据与ticker数据的触发对比"""
    print("\n⚖️ 测试K线数据与ticker数据的触发对比...")
    print("="*50)
    
    try:
        from price_monitor import PriceMonitor
        from unittest.mock import Mock, patch
        
        monitor = PriceMonitor()
        trading_pair = "BTC"
        
        # 初始化必要的数据结构
        monitor._price_history[trading_pair] = deque(maxlen=100)
        monitor._price_levels[trading_pair] = Mock()
        monitor._price_levels[trading_pair].update = Mock()
        monitor._price_levels[trading_pair].get_levels = Mock(return_value={
            'support_levels': [49000, 49500],
            'resistance_levels': [50500, 51000],
            'ma_values': {'ma5': 50000, 'ma10': 49800, 'ma20': 49600, 'ma50': 49400}
        })
        
        # 模拟数据库管理器
        monitor._db_manager.save_price_data = Mock()
        monitor._db_manager.save_kline_data = Mock()
        monitor._db_manager.save_price_levels = Mock()
        
        # 模拟触发检查
        trigger_calls = []
        def mock_trigger_check(pair, price):
            trigger_calls.append(('trigger_check', pair, price))
            return True
        
        monitor._should_trigger_agent = mock_trigger_check
        
        with patch('price_monitor.send_wechat_message') as mock_wechat:
            # 1. 测试ticker数据触发
            print("📋 测试ticker数据触发:")
            ticker_message = {
                "data": {
                    "last": "50000.0",
                    "symbol": "BTCUSDT",
                    "vol24h": "1000000",
                    "high24h": "51000.0",
                    "low24h": "49000.0"
                }
            }
            
            monitor._on_message(ticker_message)
            ticker_trigger_count = len([call for call in trigger_calls if call[0] == 'trigger_check'])
            print(f"   ticker触发检查次数: {ticker_trigger_count}")
            
            # 2. 测试K线数据触发
            print("\n📋 测试K线数据触发:")
            kline_message = {
                "action": "update",
                "arg": {"instId": "BTCUSDT"},
                "data": [[1703001600000, "50000.0", "50200.0", "49800.0", "50100.0", "1000.0"]]
            }
            
            trigger_calls.clear()  # 清空之前的记录
            monitor._on_message(kline_message)
            kline_trigger_count = len([call for call in trigger_calls if call[0] == 'trigger_check'])
            print(f"   K线触发检查次数: {kline_trigger_count}")
            
            # 验证两种数据都能触发
            assert ticker_trigger_count > 0, "ticker数据应该能触发检查"
            assert kline_trigger_count > 0, "K线数据应该能触发检查"
            print("   ✅ ticker和K线数据都能正确触发")
            
            # 验证触发价格
            if trigger_calls:
                last_call = trigger_calls[-1]
                assert last_call[2] == 50100.0, "K线触发应该使用收盘价"
                print(f"   ✅ K线触发使用收盘价: {last_call[2]}")
        
        print("✅ K线数据与ticker数据触发对比测试通过")
        return True
        
    except Exception as e:
        print(f"❌ K线数据与ticker数据触发对比测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_volume_analysis_with_kline():
    """测试K线数据中的成交量分析"""
    print("\n📊 测试K线数据中的成交量分析...")
    print("="*50)
    
    try:
        from price_monitor import PriceMonitor
        from unittest.mock import Mock
        
        monitor = PriceMonitor()
        trading_pair = "BTC"
        
        # 初始化K线数据
        from price_monitor import KlineData
        monitor._kline_data[trading_pair] = KlineData()
        
        # 更新K线数据
        test_kline = [1703001600000, "50000.0", "50200.0", "49800.0", "50100.0", "1500.0"]
        monitor._kline_data[trading_pair].update(test_kline)
        
        print("📋 测试成交量分析修复:")
        print(f"   K线成交量: {monitor._kline_data[trading_pair].volume}")
        
        # 模拟市场分析器
        volume_analysis_calls = []
        def mock_volume_analysis(volumes):
            volume_analysis_calls.append(volumes)
            return {'volume_ratio': 1.5, 'large_trades': True}
        
        monitor._market_analyzer.analyze_volume = mock_volume_analysis
        
        # 测试成交量分析代码（在_should_trigger_agent中）
        # 这里直接测试修复后的成交量获取逻辑
        kline_data = monitor._kline_data[trading_pair]
        if hasattr(kline_data, 'volume') and kline_data.volume:
            volume_analysis = monitor._market_analyzer.analyze_volume([kline_data.volume])
            print(f"   ✅ 成交量分析成功: {volume_analysis}")
            
            # 验证传入的成交量数据
            if volume_analysis_calls:
                passed_volumes = volume_analysis_calls[-1]
                print(f"   传入的成交量数据: {passed_volumes}")
                assert passed_volumes == [1500.0], "应该传入正确的成交量数据"
                print("   ✅ 成交量数据传递正确")
        else:
            print("   ❌ 成交量数据获取失败")
            return False
        
        print("✅ K线数据成交量分析测试通过")
        return True
        
    except Exception as e:
        print(f"❌ K线数据成交量分析测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 K线数据触发逻辑修复验证测试")
    print("="*60)
    print("测试内容：")
    print("1. K线数据结构")
    print("2. K线消息处理")
    print("3. K线快照处理")
    print("4. K线数据与ticker数据触发对比")
    print("5. K线数据成交量分析")
    print("")
    
    test_results = []
    
    # 执行所有测试
    tests = [
        ("K线数据结构", test_kline_data_structure),
        ("K线消息处理", test_kline_message_processing),
        ("K线快照处理", test_kline_snapshot_processing),
        ("K线vs ticker触发对比", test_kline_vs_ticker_trigger),
        ("K线成交量分析", test_volume_analysis_with_kline)
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "="*60)
    print("📊 K线数据触发逻辑修复验证测试总结")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 K线数据触发逻辑修复完成！")
        print("🎯 修复内容：")
        print("   ✅ K线update消息现在会触发价格监控")
        print("   ✅ 使用K线收盘价作为触发价格")
        print("   ✅ K线数据包含完整的OHLCV信息")
        print("   ✅ 成交量分析使用K线成交量数据")
        print("   ✅ 历史K线数据正确初始化价格历史")
        print("\n🚀 现在ticker和K线数据都能正确触发智能防重复推送机制！")
        print("💡 核心改进：")
        print("   - K线数据提供更丰富的市场信息（OHLCV）")
        print("   - 实时K线更新立即触发分析")
        print("   - 智能防重复机制同样适用于K线触发")
        return 0
    else:
        print(f"\n⚠️ {total-passed}个测试失败，请检查相关组件")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
