#!/usr/bin/env python3
"""
改进后的价格监控系统测试
验证智能触发管理器与原有价格监控的集成
"""

import sys
import json
import time
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.append(str(Path(__file__).parent / "src"))


def test_smart_trigger_manager_import():
    """测试智能触发管理器导入"""
    print("🔧 测试智能触发管理器导入...")
    print("="*50)
    
    try:
        # 测试直接导入
        sys.path.append(str(Path(__file__).parent / "src" / "gold_agents" / "tools"))
        from smart_trigger_manager import SmartTriggerManager
        
        manager = SmartTriggerManager()
        print("✅ 智能触发管理器导入成功")
        
        # 测试基本功能
        result = manager.should_allow_trigger("BTC", 50000.0, 0.8, 0.02)
        print(f"   触发测试: {result['allow']} - {result['reason']}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 智能触发管理器导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 智能触发管理器测试失败: {str(e)}")
        return False


def test_price_monitor_integration():
    """测试价格监控系统集成"""
    print("\n📈 测试价格监控系统集成...")
    print("="*50)
    
    try:
        # 导入价格监控系统
        from price_monitor import PriceMonitor
        
        # 创建价格监控实例
        monitor = PriceMonitor()
        print("✅ 价格监控系统创建成功")
        
        # 检查智能触发管理器是否已集成
        if hasattr(monitor, '_smart_trigger_manager') and monitor._smart_trigger_manager:
            print("✅ 智能触发管理器已集成")
            
            # 测试统计信息获取
            stats = monitor.get_trigger_statistics()
            print(f"   统计信息获取: {stats.get('status', 'success')}")
            
            # 测试配置更新
            test_config = {
                'min_cooldown_minutes': 10,
                'adaptive_cooldown': True
            }
            monitor.configure_smart_trigger(test_config)
            print("✅ 配置更新测试成功")
            
        else:
            print("⚠️ 智能触发管理器未集成或不可用")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 价格监控系统集成测试失败: {str(e)}")
        return False


def test_volatility_calculation():
    """测试波动率计算"""
    print("\n📊 测试波动率计算...")
    print("="*50)
    
    try:
        from price_monitor import PriceMonitor
        from collections import deque
        
        monitor = PriceMonitor()
        
        # 创建测试价格历史
        test_prices = deque([50000, 50100, 49950, 50200, 50050, 50300, 49900], maxlen=100)
        
        # 计算波动率
        volatility = monitor._calculate_volatility(test_prices)
        print(f"✅ 波动率计算成功: {volatility:.4f} ({volatility:.2%})")
        
        # 测试边界情况
        empty_prices = deque(maxlen=100)
        volatility_empty = monitor._calculate_volatility(empty_prices)
        print(f"   空数据波动率: {volatility_empty:.4f}")
        
        single_price = deque([50000], maxlen=100)
        volatility_single = monitor._calculate_volatility(single_price)
        print(f"   单价格波动率: {volatility_single:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 波动率计算测试失败: {str(e)}")
        return False


def test_trigger_logic_enhancement():
    """测试触发逻辑增强"""
    print("\n🎯 测试触发逻辑增强...")
    print("="*50)
    
    try:
        from price_monitor import PriceMonitor, MarketAnalyzer
        from collections import deque
        
        monitor = PriceMonitor()
        
        # 模拟价格历史和价格水平
        trading_pair = "BTC"
        
        # 初始化必要的数据结构
        if trading_pair not in monitor._price_history:
            monitor._price_history[trading_pair] = deque(maxlen=100)
            monitor._price_levels[trading_pair] = type('PriceLevel', (), {
                'update': lambda self, price, timestamp: None,
                'get_levels': lambda self: {
                    'support_levels': [49000, 49500],
                    'resistance_levels': [50500, 51000],
                    'ma_values': {'ma5': 50000, 'ma10': 49800, 'ma20': 49600, 'ma50': 49400}
                }
            })()
            monitor._kline_data[trading_pair] = type('KlineData', (), {
                'volume': 1000000
            })()
        
        # 添加价格历史
        test_prices = [49800, 49900, 50000, 50100, 50200]
        for price in test_prices:
            monitor._price_history[trading_pair].append(price)
        
        # 测试触发判断
        current_price = 50300.0
        should_trigger = monitor._should_trigger_agent(trading_pair, current_price)
        
        print(f"✅ 触发逻辑测试完成")
        print(f"   交易对: {trading_pair}")
        print(f"   当前价格: {current_price}")
        print(f"   触发决策: {should_trigger}")
        
        # 如果有智能触发管理器，显示其状态
        if monitor._smart_trigger_manager:
            stats = monitor.get_trigger_statistics(trading_pair)
            print(f"   触发统计: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ 触发逻辑增强测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_command_line_interface():
    """测试命令行接口"""
    print("\n💻 测试命令行接口...")
    print("="*50)
    
    try:
        import subprocess
        import os
        
        # 测试统计信息显示
        print("📊 测试统计信息显示...")
        result = subprocess.run([
            sys.executable, "src/price_monitor.py", "--stats"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ 统计信息显示测试成功")
        else:
            print(f"⚠️ 统计信息显示测试警告: {result.stderr}")
        
        # 测试配置更新
        print("⚙️ 测试配置更新...")
        test_config = '{"min_cooldown_minutes": 10, "adaptive_cooldown": true}'
        result = subprocess.run([
            sys.executable, "src/price_monitor.py", "--config-trigger", test_config
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ 配置更新测试成功")
        else:
            print(f"⚠️ 配置更新测试警告: {result.stderr}")
        
        return True
        
    except subprocess.TimeoutExpired:
        print("⚠️ 命令行测试超时（正常情况，因为主程序会持续运行）")
        return True
    except Exception as e:
        print(f"❌ 命令行接口测试失败: {str(e)}")
        return False


def test_real_data_compatibility():
    """测试真实数据兼容性"""
    print("\n🔗 测试真实数据兼容性...")
    print("="*50)
    
    try:
        from price_monitor import PriceMonitor, DatabaseManager, MarketAnalyzer
        
        # 测试数据库管理器
        db_manager = DatabaseManager()
        print("✅ 数据库管理器创建成功")
        
        # 测试市场分析器
        market_analyzer = MarketAnalyzer()
        print("✅ 市场分析器创建成功")
        
        # 测试价格监控系统的各个组件
        monitor = PriceMonitor()
        
        # 检查所有必要的属性
        required_attrs = [
            '_price_history', '_price_levels', '_kline_data',
            '_db_manager', '_market_analyzer'
        ]
        
        missing_attrs = []
        for attr in required_attrs:
            if not hasattr(monitor, attr):
                missing_attrs.append(attr)
        
        if missing_attrs:
            print(f"❌ 缺少必要属性: {missing_attrs}")
            return False
        
        print("✅ 所有必要组件都已正确初始化")
        
        # 测试智能触发管理器集成
        if hasattr(monitor, '_smart_trigger_manager'):
            if monitor._smart_trigger_manager:
                print("✅ 智能触发管理器已正确集成")
            else:
                print("⚠️ 智能触发管理器未启用（可能是导入问题）")
        
        return True
        
    except Exception as e:
        print(f"❌ 真实数据兼容性测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("🚀 改进后的价格监控系统完整测试")
    print("="*60)
    print("测试内容：")
    print("1. 智能触发管理器导入")
    print("2. 价格监控系统集成")
    print("3. 波动率计算功能")
    print("4. 触发逻辑增强")
    print("5. 命令行接口")
    print("6. 真实数据兼容性")
    print("")
    
    test_results = []
    
    # 执行所有测试
    tests = [
        ("智能触发管理器导入", test_smart_trigger_manager_import),
        ("价格监控系统集成", test_price_monitor_integration),
        ("波动率计算功能", test_volatility_calculation),
        ("触发逻辑增强", test_trigger_logic_enhancement),
        ("命令行接口", test_command_line_interface),
        ("真实数据兼容性", test_real_data_compatibility)
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "="*60)
    print("📊 改进后的价格监控系统测试总结")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 改进后的价格监控系统完全就绪！")
        print("🎯 改进特性：")
        print("   ✅ 保持原有真实数据处理能力")
        print("   ✅ 集成智能触发管理器")
        print("   ✅ 增强防重复推送机制")
        print("   ✅ 自适应冷却时间调整")
        print("   ✅ 多维度触发条件检查")
        print("   ✅ 完整的统计信息跟踪")
        print("   ✅ 灵活的命令行接口")
        print("\n🚀 您的价格监控系统现在具备专业级的防重复推送能力！")
        return 0
    else:
        print(f"\n⚠️ {total-passed}个测试失败，请检查相关组件")
        print("💡 使用建议：")
        print("   1. 确保 smart_trigger_manager.py 在正确路径")
        print("   2. 检查依赖包是否完整安装")
        print("   3. 验证数据库连接配置")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
