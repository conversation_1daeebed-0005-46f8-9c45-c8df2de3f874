#!/usr/bin/env python3
"""
专业交易流程测试 - 验证从价格监控到交易执行的完整专业流程
"""

import sys
import json
import time
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.append(str(Path(__file__).parent / "src"))


def test_professional_market_analyzer():
    """测试专业市场分析工具"""
    print("🔍 测试专业市场分析工具...")
    print("="*50)
    
    try:
        from gold_agents.tools.professional_market_analyzer import ProfessionalMarketAnalyzer
        
        analyzer = ProfessionalMarketAnalyzer()
        
        # 1. 测试交易机会分析
        print("📊 测试交易机会分析...")
        opportunity_result = analyzer.run(action="analyze_opportunity", trading_pair="BTC")
        
        if opportunity_result['status'] == 'success':
            print("✅ 交易机会分析成功")
            
            opportunity = opportunity_result.get('opportunity')
            if opportunity:
                print(f"   信号类型: {opportunity['signal_type']}")
                print(f"   置信度: {opportunity['confidence']:.2%}")
                print(f"   风险收益比: {opportunity['risk_reward_ratio']:.2f}")
                print(f"   时间框架一致性: {opportunity['timeframe_confluence']}")
                print(f"   成交量确认: {opportunity['volume_confirmation']}")
            else:
                print("   当前无交易机会")
            
            # 时间框架分析
            timeframe_analysis = opportunity_result.get('timeframe_analysis', {})
            confluence_score = timeframe_analysis.get('confluence_score', 0)
            print(f"   时间框架一致性评分: {confluence_score:.2%}")
        else:
            print(f"❌ 交易机会分析失败: {opportunity_result.get('message', 'unknown')}")
            return False
        
        # 2. 测试风险评估
        print("\n⚠️ 测试风险评估...")
        risk_result = analyzer.run(action="assess_risk", trading_pair="BTC")
        
        if risk_result['status'] == 'success':
            print("✅ 风险评估成功")
            
            risk_assessment = risk_result.get('risk_assessment', {})
            overall_risk = risk_assessment.get('overall_risk', {})
            
            print(f"   风险评分: {overall_risk.get('risk_score', 0):.2%}")
            print(f"   风险等级: {overall_risk.get('risk_level', 'unknown')}")
            print(f"   建议仓位: {overall_risk.get('max_position_size', 0):.2%}")
            print(f"   建议杠杆: {overall_risk.get('recommended_leverage', 1)}x")
            
            # 风险警报
            risk_alerts = risk_result.get('risk_alerts', [])
            if risk_alerts:
                print("   风险警报:")
                for alert in risk_alerts:
                    print(f"     - {alert}")
        else:
            print(f"❌ 风险评估失败: {risk_result.get('message', 'unknown')}")
            return False
        
        # 3. 测试市场制度检测
        print("\n🌊 测试市场制度检测...")
        regime_result = analyzer.run(action="detect_regime", trading_pair="BTC")
        
        if regime_result['status'] == 'success':
            print("✅ 市场制度检测成功")
            
            market_state = regime_result.get('market_state', {})
            print(f"   趋势方向: {market_state.get('trend_direction', 'unknown')}")
            print(f"   趋势强度: {market_state.get('trend_strength', 0):.2%}")
            print(f"   波动率制度: {market_state.get('volatility_regime', 'unknown')}")
            print(f"   市场阶段: {market_state.get('market_phase', 'unknown')}")
            
            # 策略建议
            strategy_recommendations = regime_result.get('strategy_recommendations', {})
            print(f"   推荐策略: {strategy_recommendations.get('primary_strategy', 'unknown')}")
            print(f"   信号阈值: {strategy_recommendations.get('signal_threshold', 0):.2f}")
        else:
            print(f"❌ 市场制度检测失败: {regime_result.get('message', 'unknown')}")
            return False
        
        print("🎉 专业市场分析工具测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 专业市场分析工具测试失败: {str(e)}")
        return False


def test_enhanced_price_monitor():
    """测试增强价格监控系统"""
    print("\n📈 测试增强价格监控系统...")
    print("="*50)
    
    try:
        from enhanced_price_monitor import EnhancedPriceMonitor
        
        monitor = EnhancedPriceMonitor()
        
        # 测试触发逻辑
        print("🎯 测试触发逻辑...")
        
        # 模拟不同价格情况
        test_prices = [50000.0, 50100.0, 49900.0, 50200.0]
        
        for i, price in enumerate(test_prices, 1):
            print(f"\n   测试场景 {i}: 价格 {price}")
            
            trigger_result = monitor.should_trigger_trading_team("BTC", price)
            
            print(f"   触发决策: {trigger_result['should_trigger']}")
            print(f"   决策原因: {trigger_result['reason']}")
            
            if trigger_result['should_trigger']:
                print(f"   置信度: {trigger_result.get('confidence', 0):.2%}")
                print(f"   风险收益比: {trigger_result.get('risk_reward_ratio', 0):.2f}")
                print(f"   市场风险: {trigger_result.get('market_risk_score', 0):.2%}")
                
                # 检查具体条件
                checks = trigger_result.get('checks', {})
                print("   条件检查:")
                for check_name, result in checks.items():
                    status = "✅" if result else "❌"
                    print(f"     {check_name}: {status}")
        
        # 测试监控状态
        print("\n📊 测试监控状态...")
        status = monitor.get_monitor_status()
        
        print(f"   监控活跃: {status['monitor_active']}")
        print(f"   触发配置: {status['trigger_config']}")
        print(f"   冷却时间: {status['cooldown_remaining']}秒")
        
        print("✅ 增强价格监控系统测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 增强价格监控系统测试失败: {str(e)}")
        return False


def test_trader_agent_integration():
    """测试交易员Agent集成"""
    print("\n🤖 测试交易员Agent集成...")
    print("="*50)
    
    try:
        from gold_agents.crew import GoldAgents
        
        gold_agents = GoldAgents()
        
        # 检查交易员Agent工具配置
        trader_agent = gold_agents.trader()
        tool_names = [tool.__class__.__name__ for tool in trader_agent.tools]
        
        print(f"📋 交易员Agent工具列表:")
        for i, tool_name in enumerate(tool_names, 1):
            print(f"   {i}. {tool_name}")
        
        # 检查专业分析工具
        if 'ProfessionalMarketAnalyzer' in tool_names:
            print("✅ 专业市场分析工具已集成")
        else:
            print("❌ 专业市场分析工具未集成")
            return False
        
        # 检查复盘查询工具
        if 'ReviewResultQueryTool' in tool_names:
            print("✅ 复盘结果查询工具已集成")
        else:
            print("❌ 复盘结果查询工具未集成")
            return False
        
        # 检查其他必要工具
        required_tools = ['StrategyTool', 'BitgetMarketTool', 'BitgetAccountTool', 'TradingRulesEngine']
        missing_tools = []
        
        for required_tool in required_tools:
            if not any(required_tool in tool_name for tool_name in tool_names):
                missing_tools.append(required_tool)
        
        if missing_tools:
            print(f"❌ 缺少必要工具: {missing_tools}")
            return False
        else:
            print("✅ 所有必要工具已配置")
        
        # 检查Agent配置
        agent_config = trader_agent.config
        prompt = agent_config.get('prompt', '')
        
        # 检查专业分析相关内容
        professional_keywords = ['ProfessionalMarketAnalyzer', '专业市场分析', '多时间框架', '市场制度']
        found_keywords = [kw for kw in professional_keywords if kw in prompt]
        
        if found_keywords:
            print(f"✅ 提示词包含专业分析内容: {found_keywords}")
        else:
            print("❌ 提示词缺少专业分析内容")
            return False
        
        gold_agents.close()
        print("✅ 交易员Agent集成测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 交易员Agent集成测试失败: {str(e)}")
        return False


def test_complete_professional_flow():
    """测试完整专业交易流程"""
    print("\n🔄 测试完整专业交易流程...")
    print("="*50)
    
    try:
        # 模拟完整流程
        print("📋 模拟专业交易员完整决策流程:")
        
        # 1. 专业市场分析
        print("\n   1. 执行专业市场分析...")
        from gold_agents.tools.professional_market_analyzer import ProfessionalMarketAnalyzer
        
        analyzer = ProfessionalMarketAnalyzer()
        
        # 分析交易机会
        opportunity_result = analyzer.run(action="analyze_opportunity", trading_pair="BTC")
        if opportunity_result['status'] == 'success':
            print("      ✅ 交易机会分析完成")
            opportunity = opportunity_result.get('opportunity')
            if opportunity:
                print(f"         发现{opportunity['signal_type']}机会，置信度{opportunity['confidence']:.2%}")
        
        # 评估风险
        risk_result = analyzer.run(action="assess_risk", trading_pair="BTC")
        if risk_result['status'] == 'success':
            print("      ✅ 风险评估完成")
            overall_risk = risk_result.get('risk_assessment', {}).get('overall_risk', {})
            print(f"         市场风险等级: {overall_risk.get('risk_level', 'unknown')}")
        
        # 检测市场制度
        regime_result = analyzer.run(action="detect_regime", trading_pair="BTC")
        if regime_result['status'] == 'success':
            print("      ✅ 市场制度检测完成")
            market_state = regime_result.get('market_state', {})
            print(f"         市场阶段: {market_state.get('market_phase', 'unknown')}")
        
        # 2. 复盘结果查询
        print("\n   2. 查询复盘结果...")
        from gold_agents.tools.review_result_query_tool import ReviewResultQueryTool
        
        query_tool = ReviewResultQueryTool()
        latest_review = query_tool.run(action="get_latest_report")
        
        if latest_review['status'] == 'success':
            print("      ✅ 复盘结果查询完成")
            report = latest_review.get('report', {})
            if report.get('win_rate'):
                print(f"         历史胜率: {report['win_rate']:.2%}")
        else:
            print("      ⚠️ 无复盘数据（正常情况）")
        
        # 3. 综合决策
        print("\n   3. 综合决策制定...")
        
        # 模拟交易员的决策逻辑
        decision_factors = {
            'market_opportunity': opportunity is not None if 'opportunity' in locals() else False,
            'risk_acceptable': overall_risk.get('risk_level') in ['low', 'moderate'] if 'overall_risk' in locals() else True,
            'market_suitable': market_state.get('market_phase') == 'trending' if 'market_state' in locals() else True,
            'historical_performance': True  # 假设历史表现良好
        }
        
        print("      📊 决策因素评估:")
        for factor, result in decision_factors.items():
            status = "✅" if result else "❌"
            print(f"         {factor}: {status}")
        
        # 最终决策
        should_trade = all(decision_factors.values())
        
        if should_trade:
            print("\n      🎯 决策结果: 执行交易")
            print("         理由: 所有专业分析条件满足")
            
            # 模拟交易参数
            if 'opportunity' in locals() and opportunity:
                print(f"         入场价: {opportunity.get('optimal_entry', 50000):.2f}")
                print(f"         止损价: {opportunity.get('stop_loss', 49000):.2f}")
                print(f"         止盈价: {opportunity.get('take_profit', 51000):.2f}")
                print(f"         风险收益比: {opportunity.get('risk_reward_ratio', 2.0):.2f}")
        else:
            print("\n      ⏸️ 决策结果: 暂不交易")
            print("         理由: 专业分析条件不满足")
        
        print("\n🎉 完整专业交易流程测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 完整专业交易流程测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("🚀 专业交易流程完整测试")
    print("="*60)
    print("测试内容：")
    print("1. 专业市场分析工具")
    print("2. 增强价格监控系统")
    print("3. 交易员Agent集成")
    print("4. 完整专业交易流程")
    print("")
    
    test_results = []
    
    # 执行所有测试
    tests = [
        ("专业市场分析工具", test_professional_market_analyzer),
        ("增强价格监控系统", test_enhanced_price_monitor),
        ("交易员Agent集成", test_trader_agent_integration),
        ("完整专业交易流程", test_complete_professional_flow)
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "="*60)
    print("📊 专业交易流程测试总结")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 专业交易流程完全就绪！")
        print("🎯 专业特性：")
        print("   ✅ 多时间框架分析确认")
        print("   ✅ 关键位突破验证")
        print("   ✅ 成交量配合确认")
        print("   ✅ 市场制度自适应")
        print("   ✅ 动态风险评估")
        print("   ✅ 专业级触发条件")
        print("   ✅ 复盘结果集成")
        print("\n🚀 系统已达到专业交易员水准！")
        return 0
    else:
        print(f"\n⚠️ {total-passed}个测试失败，请检查相关组件")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
