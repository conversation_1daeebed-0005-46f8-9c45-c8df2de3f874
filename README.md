项目背景：
本项目是通过价格监控的方式 ，监控合约BTCUSDT的价格变化，然后触发唤醒Agent交易员团队进行实时交易、仓库管理、风险应对,同时Agent团队主要是替代交易员人为盯盘，这样可以更有效的发现交易机会 ，更快、更精准的进行交易
注意：通过[price_monitor.py](src/price_monitor.py) 进行监控，此代码目前测试阶段，暂时未完成，里面的触发是推送企业微信消息，后续会进行完善进行唤醒交易团队
Agent交易员团队成员：
交易员
仓位管理员
风险控制
订单执行员
交易团队责任
1、被唤醒的交易员团队需要进行多周期K线技术面分析、消息面分析，寻找交易机会进行交易（每笔交易需要保存到数据库中）、仓位管理、风险应对等等，来保证账户资金的安全
2、复盘，对近期的交易成果进行复盘，形成复盘策略报告，对后续交易进行关键性指导 ，复盘通过查询近期的交易记录，有一个问题：是否需要将每一笔交易的过程全部保存下来，比如每一个Agent的输出输出 
3、Agent交易团队在模拟仓进行交易
4、每笔交易按照1-10U金额进行交易，根据情况选择交易的金额大小 限制10U是保证资金安全




