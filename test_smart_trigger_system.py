#!/usr/bin/env python3
"""
智能触发系统测试 - 验证增强的防重复推送机制
"""

import sys
import time
import json
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(str(Path(__file__).parent / "src"))


def test_smart_trigger_manager():
    """测试智能触发管理器"""
    print("🧠 测试智能触发管理器...")
    print("="*50)
    
    try:
        from gold_agents.tools.smart_trigger_manager import SmartTriggerManager
        
        manager = SmartTriggerManager(base_cooldown_minutes=5)  # 使用较短的冷却时间便于测试
        
        # 测试场景1: 首次触发
        print("📋 测试场景1: 首次触发")
        result1 = manager.should_allow_trigger("BTC", 50000.0, 0.8, 0.03)
        print(f"   结果: {result1['allow']} - {result1['reason']}")
        
        if result1['allow']:
            trigger_id1 = manager.record_trigger("BTC", 50000.0, 0.8, "首次触发测试")
            print(f"   触发ID: {trigger_id1}")
            manager.update_trigger_result(trigger_id1, True)
        
        # 测试场景2: 立即重复触发（应该被阻止）
        print("\n📋 测试场景2: 立即重复触发")
        result2 = manager.should_allow_trigger("BTC", 50010.0, 0.8, 0.03)
        print(f"   结果: {result2['allow']} - {result2['reason']}")
        
        # 测试场景3: 价格相似性检查
        print("\n📋 测试场景3: 价格相似性检查")
        # 等待一段时间后，用相似价格触发
        time.sleep(1)
        result3 = manager.should_allow_trigger("BTC", 50025.0, 0.8, 0.03)  # 0.05%差异
        print(f"   结果: {result3['allow']} - {result3['reason']}")
        
        # 测试场景4: 不同交易对（应该允许）
        print("\n📋 测试场景4: 不同交易对")
        result4 = manager.should_allow_trigger("ETH", 3000.0, 0.8, 0.03)
        print(f"   结果: {result4['allow']} - {result4['reason']}")
        
        if result4['allow']:
            trigger_id4 = manager.record_trigger("ETH", 3000.0, 0.8, "不同交易对测试")
            print(f"   触发ID: {trigger_id4}")
        
        # 测试场景5: 高频触发检查
        print("\n📋 测试场景5: 高频触发检查")
        for i in range(3):
            result = manager.should_allow_trigger("TEST", 1000.0 + i*10, 0.8, 0.03)
            print(f"   第{i+1}次: {result['allow']} - {result['reason']}")
            if result['allow']:
                trigger_id = manager.record_trigger("TEST", 1000.0 + i*10, 0.8, f"高频测试{i+1}")
                manager.update_trigger_result(trigger_id, True)
            time.sleep(0.1)
        
        # 获取统计信息
        print("\n📊 统计信息:")
        stats = manager.get_trigger_statistics()
        print(f"   总触发次数: {stats['total_triggers']}")
        print(f"   活跃交易对: {stats['active_pairs']}")
        print(f"   平均成功率: {stats['average_success_rate']:.2%}")
        
        # 获取BTC的详细统计
        btc_stats = manager.get_trigger_statistics("BTC")
        print(f"   BTC触发次数: {btc_stats['trigger_count']}")
        print(f"   BTC成功率: {btc_stats['recent_success_rate']:.2%}")
        
        print("✅ 智能触发管理器测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 智能触发管理器测试失败: {str(e)}")
        return False


def test_enhanced_price_monitor_integration():
    """测试增强价格监控系统集成"""
    print("\n📈 测试增强价格监控系统集成...")
    print("="*50)
    
    try:
        from enhanced_price_monitor import EnhancedPriceMonitor
        
        monitor = EnhancedPriceMonitor()
        
        # 测试场景1: 正常触发
        print("📋 测试场景1: 正常触发判断")
        result1 = monitor.should_trigger_trading_team("BTC", 50000.0)
        print(f"   触发决策: {result1['should_trigger']}")
        print(f"   决策原因: {result1['reason']}")
        
        if 'signal_strength' in result1:
            print(f"   信号强度: {result1['signal_strength']:.2%}")
        
        # 测试场景2: 立即重复触发
        print("\n📋 测试场景2: 立即重复触发")
        result2 = monitor.should_trigger_trading_team("BTC", 50050.0)
        print(f"   触发决策: {result2['should_trigger']}")
        print(f"   决策原因: {result2['reason']}")
        
        if 'cooldown_info' in result2:
            cooldown_info = result2['cooldown_info']
            print(f"   冷却信息: {cooldown_info.get('cooldown_type', 'unknown')}")
            if 'remaining_seconds' in cooldown_info:
                print(f"   剩余时间: {cooldown_info['remaining_seconds']}秒")
        
        # 测试场景3: 不同交易对
        print("\n📋 测试场景3: 不同交易对")
        result3 = monitor.should_trigger_trading_team("ETH", 3000.0)
        print(f"   触发决策: {result3['should_trigger']}")
        print(f"   决策原因: {result3['reason']}")
        
        # 获取监控状态
        print("\n📊 监控状态:")
        status = monitor.get_monitor_status()
        print(f"   监控活跃: {status['monitor_active']}")
        print(f"   交易会话活跃: {status['trading_session_active']}")
        
        smart_stats = status.get('smart_trigger_stats', {})
        print(f"   总触发次数: {smart_stats.get('total_triggers', 0)}")
        print(f"   活跃交易对: {smart_stats.get('active_pairs', 0)}")
        print(f"   平均成功率: {smart_stats.get('average_success_rate', 0):.2%}")
        
        print("✅ 增强价格监控系统集成测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 增强价格监控系统集成测试失败: {str(e)}")
        return False


def test_adaptive_cooldown():
    """测试自适应冷却时间"""
    print("\n🔄 测试自适应冷却时间...")
    print("="*50)
    
    try:
        from gold_agents.tools.smart_trigger_manager import SmartTriggerManager
        
        manager = SmartTriggerManager(base_cooldown_minutes=10)
        
        # 启用自适应冷却
        manager.update_config({'adaptive_cooldown': True})
        
        # 测试不同信号强度的冷却时间
        test_cases = [
            ("强信号", 0.95, 0.03),
            ("中等信号", 0.75, 0.02),
            ("弱信号", 0.6, 0.01),
        ]
        
        for case_name, signal_strength, volatility in test_cases:
            print(f"\n📋 测试{case_name}:")
            print(f"   信号强度: {signal_strength:.2%}")
            print(f"   市场波动率: {volatility:.2%}")
            
            # 计算自适应冷却时间
            adaptive_cooldown = manager._calculate_adaptive_cooldown("TEST", signal_strength, volatility)
            print(f"   自适应冷却时间: {adaptive_cooldown}分钟")
            
            # 测试触发
            result = manager.should_allow_trigger("TEST", 1000.0, signal_strength, volatility)
            print(f"   触发允许: {result['allow']}")
            
            if result['allow']:
                trigger_id = manager.record_trigger("TEST", 1000.0, signal_strength, case_name)
                print(f"   触发ID: {trigger_id}")
                
                # 模拟不同的成功率
                success = signal_strength > 0.7  # 强信号更容易成功
                manager.update_trigger_result(trigger_id, success)
                print(f"   模拟结果: {'成功' if success else '失败'}")
        
        print("✅ 自适应冷却时间测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 自适应冷却时间测试失败: {str(e)}")
        return False


def test_time_window_limits():
    """测试时间窗口限制"""
    print("\n⏰ 测试时间窗口限制...")
    print("="*50)
    
    try:
        from gold_agents.tools.smart_trigger_manager import SmartTriggerManager
        
        manager = SmartTriggerManager(base_cooldown_minutes=1)  # 很短的基础冷却时间
        
        # 修改时间窗口限制为更小的值便于测试
        manager.time_window_limits = {
            'minute': 1,
            'hour': 3,
            'day': 10
        }
        
        print("📋 测试1分钟内多次触发:")
        
        # 在1分钟内尝试多次触发
        for i in range(3):
            result = manager.should_allow_trigger("LIMIT_TEST", 1000.0 + i, 0.8, 0.02)
            print(f"   第{i+1}次触发: {result['allow']} - {result['reason']}")
            
            if result['allow']:
                trigger_id = manager.record_trigger("LIMIT_TEST", 1000.0 + i, 0.8, f"限制测试{i+1}")
                manager.update_trigger_result(trigger_id, True)
            
            time.sleep(0.1)  # 短暂等待
        
        print("✅ 时间窗口限制测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 时间窗口限制测试失败: {str(e)}")
        return False


def test_price_similarity_filter():
    """测试价格相似性过滤"""
    print("\n💰 测试价格相似性过滤...")
    print("="*50)
    
    try:
        from gold_agents.tools.smart_trigger_manager import SmartTriggerManager
        
        manager = SmartTriggerManager(base_cooldown_minutes=1)
        
        # 设置较小的价格相似度阈值
        manager.update_config({'price_similarity_threshold': 0.002})  # 0.2%
        
        base_price = 50000.0
        
        print("📋 测试价格相似性检查:")
        
        # 第一次触发
        result1 = manager.should_allow_trigger("PRICE_TEST", base_price, 0.8, 0.02)
        print(f"   基准价格 {base_price}: {result1['allow']} - {result1['reason']}")
        
        if result1['allow']:
            trigger_id1 = manager.record_trigger("PRICE_TEST", base_price, 0.8, "基准价格")
            manager.update_trigger_result(trigger_id1, True)
        
        time.sleep(1.1)  # 等待基础冷却时间过去
        
        # 测试相似价格
        similar_prices = [
            (base_price + 50, "相似价格1 (+0.1%)"),
            (base_price + 200, "不同价格1 (+0.4%)"),
            (base_price - 80, "相似价格2 (-0.16%)"),
            (base_price + 500, "不同价格2 (+1.0%)")
        ]
        
        for price, description in similar_prices:
            result = manager.should_allow_trigger("PRICE_TEST", price, 0.8, 0.02)
            price_diff = abs(price - base_price) / base_price
            print(f"   {description} (差异{price_diff:.3%}): {result['allow']} - {result['reason']}")
            
            if result['allow']:
                trigger_id = manager.record_trigger("PRICE_TEST", price, 0.8, description)
                manager.update_trigger_result(trigger_id, True)
            
            time.sleep(0.1)
        
        print("✅ 价格相似性过滤测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 价格相似性过滤测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("🚀 智能触发系统完整测试")
    print("="*60)
    print("测试内容：")
    print("1. 智能触发管理器基础功能")
    print("2. 增强价格监控系统集成")
    print("3. 自适应冷却时间机制")
    print("4. 时间窗口限制功能")
    print("5. 价格相似性过滤功能")
    print("")
    
    test_results = []
    
    # 执行所有测试
    tests = [
        ("智能触发管理器", test_smart_trigger_manager),
        ("价格监控集成", test_enhanced_price_monitor_integration),
        ("自适应冷却时间", test_adaptive_cooldown),
        ("时间窗口限制", test_time_window_limits),
        ("价格相似性过滤", test_price_similarity_filter)
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "="*60)
    print("📊 智能触发系统测试总结")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 智能触发系统完全就绪！")
        print("🎯 防重复推送特性：")
        print("   ✅ 基础冷却时间保护")
        print("   ✅ 自适应冷却时间调整")
        print("   ✅ 多维度时间窗口限制")
        print("   ✅ 价格相似性过滤")
        print("   ✅ 交易对独立管理")
        print("   ✅ 成功率跟踪优化")
        print("   ✅ 高频触发保护")
        print("\n🚀 您的防重复推送设计已达到专业级水准！")
        return 0
    else:
        print(f"\n⚠️ {total-passed}个测试失败，请检查相关组件")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
