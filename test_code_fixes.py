#!/usr/bin/env python3
"""
代码修复验证测试
验证修复后的价格监控触发逻辑是否正常工作
"""

import sys
import json
import time
from pathlib import Path
from datetime import datetime, timedelta
from collections import deque

# 添加项目路径
sys.path.append(str(Path(__file__).parent / "src"))


def test_code_syntax():
    """测试代码语法正确性"""
    print("🔍 测试代码语法正确性...")
    print("="*50)
    
    try:
        # 尝试导入价格监控模块
        from price_monitor import PriceMonitor
        print("✅ 代码语法检查通过")
        return True
        
    except SyntaxError as e:
        print(f"❌ 语法错误: {str(e)}")
        return False
    except ImportError as e:
        print(f"❌ 导入错误: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {str(e)}")
        return False


def test_volume_analysis_fix():
    """测试成交量分析修复"""
    print("\n📊 测试成交量分析修复...")
    print("="*50)
    
    try:
        from price_monitor import PriceMonitor
        from unittest.mock import Mock
        
        monitor = PriceMonitor()
        trading_pair = "BTC"
        
        # 初始化必要的数据结构
        monitor._price_history[trading_pair] = deque([49800, 49900, 50000], maxlen=100)
        monitor._price_levels[trading_pair] = Mock()
        monitor._price_levels[trading_pair].get_levels = Mock(return_value={
            'support_levels': [49000, 49500],
            'resistance_levels': [50500, 51000],
            'ma_values': {'ma5': 50000, 'ma10': 49800, 'ma20': 49600, 'ma50': 49400}
        })
        
        # 测试不同的kline_data情况
        test_cases = [
            ("有volume属性", Mock(volume=1000000)),
            ("无volume属性", Mock()),
            ("volume为None", Mock(volume=None)),
            ("volume为0", Mock(volume=0))
        ]
        
        for case_name, kline_data in test_cases:
            print(f"   测试{case_name}:")
            monitor._kline_data[trading_pair] = kline_data
            
            # 模拟市场分析器
            monitor._market_analyzer.analyze_volume = Mock(return_value={'volume_ratio': 1.2})
            
            try:
                # 这应该不会出错
                if hasattr(kline_data, 'volume') and kline_data.volume:
                    volume_analysis = monitor._market_analyzer.analyze_volume([kline_data.volume])
                else:
                    volume_analysis = monitor._market_analyzer.analyze_volume([])
                
                print(f"     ✅ 成交量分析成功")
                
            except Exception as e:
                print(f"     ❌ 成交量分析失败: {str(e)}")
                return False
        
        print("✅ 成交量分析修复验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 成交量分析修复验证失败: {str(e)}")
        return False


def test_volatility_calculation_fix():
    """测试波动率计算修复"""
    print("\n📈 测试波动率计算修复...")
    print("="*50)
    
    try:
        from price_monitor import PriceMonitor
        
        monitor = PriceMonitor()
        
        # 检查是否只有一个_calculate_volatility方法
        volatility_methods = [method for method in dir(monitor) if method == '_calculate_volatility']
        print(f"   _calculate_volatility方法数量: {len(volatility_methods)}")
        
        if len(volatility_methods) != 1:
            print(f"❌ 发现{len(volatility_methods)}个_calculate_volatility方法，应该只有1个")
            return False
        
        # 测试波动率计算
        test_prices = deque([50000, 50100, 49950, 50200, 50050], maxlen=100)
        
        volatility = monitor._calculate_volatility(test_prices)
        print(f"   计算的波动率: {volatility:.4f}")
        
        if 0.001 <= volatility <= 0.1:
            print("✅ 波动率计算在合理范围内")
        else:
            print(f"❌ 波动率{volatility}超出合理范围[0.001, 0.1]")
            return False
        
        # 测试边界情况
        empty_prices = deque(maxlen=100)
        volatility_empty = monitor._calculate_volatility(empty_prices)
        print(f"   空数据波动率: {volatility_empty:.4f}")
        
        if volatility_empty == 0.02:
            print("✅ 空数据默认波动率正确")
        else:
            print(f"❌ 空数据默认波动率应为0.02，实际为{volatility_empty}")
            return False
        
        print("✅ 波动率计算修复验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 波动率计算修复验证失败: {str(e)}")
        return False


def test_time_window_improvement():
    """测试时间窗口限制改进"""
    print("\n⏰ 测试时间窗口限制改进...")
    print("="*50)
    
    try:
        from price_monitor import PriceMonitor
        
        monitor = PriceMonitor()
        trading_pair = "BTC"
        
        # 测试无触发历史
        print("   测试无触发历史:")
        count = monitor._estimate_recent_triggers(trading_pair, 'minute')
        print(f"     无历史触发次数: {count}")
        if count == 0:
            print("     ✅ 无历史情况正确")
        else:
            print(f"     ❌ 无历史应返回0，实际返回{count}")
            return False
        
        # 设置最近触发时间
        monitor._last_trigger_times[trading_pair] = datetime.now() - timedelta(seconds=30)
        monitor._trigger_counts[trading_pair] = 5
        
        # 测试1分钟内触发
        print("   测试1分钟内触发:")
        count = monitor._estimate_recent_triggers(trading_pair, 'minute')
        print(f"     1分钟内触发次数: {count}")
        if count == 1:
            print("     ✅ 1分钟内触发次数正确")
        else:
            print(f"     ❌ 1分钟内应返回1，实际返回{count}")
            return False
        
        # 测试1小时内触发
        print("   测试1小时内触发:")
        count = monitor._estimate_recent_triggers(trading_pair, 'hour')
        print(f"     1小时内触发次数: {count}")
        if count == 3:  # 更严格的限制
            print("     ✅ 1小时内触发次数正确（更严格限制）")
        else:
            print(f"     ❌ 1小时内应返回3，实际返回{count}")
            return False
        
        # 测试超过时间窗口
        monitor._last_trigger_times[trading_pair] = datetime.now() - timedelta(minutes=2)
        count = monitor._estimate_recent_triggers(trading_pair, 'minute')
        print(f"   超过1分钟的触发次数: {count}")
        if count == 0:
            print("     ✅ 超过时间窗口正确返回0")
        else:
            print(f"     ❌ 超过时间窗口应返回0，实际返回{count}")
            return False
        
        print("✅ 时间窗口限制改进验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 时间窗口限制改进验证失败: {str(e)}")
        return False


def test_config_adjustment():
    """测试配置调整"""
    print("\n⚙️ 测试配置调整...")
    print("="*50)
    
    try:
        from price_monitor import PriceMonitor
        
        monitor = PriceMonitor()
        config = monitor._trigger_config
        
        # 检查时间窗口限制配置
        time_limits = config['time_window_limits']
        
        expected_limits = {
            'minute': 1,
            'hour': 3,    # 更严格
            'day': 12     # 更合理
        }
        
        print("   检查时间窗口限制配置:")
        for window, expected in expected_limits.items():
            actual = time_limits[window]
            print(f"     {window}: {actual} (期望: {expected})")
            if actual == expected:
                print(f"     ✅ {window}配置正确")
            else:
                print(f"     ❌ {window}配置错误，期望{expected}，实际{actual}")
                return False
        
        # 检查其他配置
        print("   检查其他配置:")
        expected_config = {
            'base_cooldown_minutes': 15,
            'min_cooldown_minutes': 5,
            'max_cooldown_minutes': 60,
            'price_similarity_threshold': 0.005,
            'adaptive_cooldown': True
        }
        
        for key, expected in expected_config.items():
            actual = config[key]
            print(f"     {key}: {actual} (期望: {expected})")
            if actual == expected:
                print(f"     ✅ {key}配置正确")
            else:
                print(f"     ❌ {key}配置错误，期望{expected}，实际{actual}")
                return False
        
        print("✅ 配置调整验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置调整验证失败: {str(e)}")
        return False


def test_comprehensive_trigger_logic():
    """测试综合触发逻辑"""
    print("\n🎯 测试综合触发逻辑...")
    print("="*50)
    
    try:
        from price_monitor import PriceMonitor
        from unittest.mock import Mock
        
        monitor = PriceMonitor()
        trading_pair = "BTC"
        current_price = 50000.0
        
        # 初始化必要的数据结构
        monitor._price_history[trading_pair] = deque([49800, 49900, 50000], maxlen=100)
        monitor._price_levels[trading_pair] = Mock()
        monitor._price_levels[trading_pair].get_levels = Mock(return_value={
            'support_levels': [49000, 49500],
            'resistance_levels': [50500, 51000],
            'ma_values': {'ma5': 50000, 'ma10': 49800, 'ma20': 49600, 'ma50': 49400}
        })
        monitor._kline_data[trading_pair] = Mock(volume=1000000)
        
        # 模拟市场分析器
        monitor._market_analyzer.analyze_trend = Mock(return_value={'1h': {'trend': 'bullish', 'strength': 0.8}})
        monitor._market_analyzer.analyze_volume = Mock(return_value={'volume_ratio': 1.5, 'large_trades': True})
        monitor._market_analyzer.analyze_technical_indicators = Mock(return_value={'rsi': 65, 'macd': {'histogram': 0.002}})
        monitor._market_analyzer.analyze_market_depth = Mock(return_value={'imbalance': 0.3, 'liquidity_score': 0.7})
        
        print("   测试首次触发（应该成功）:")
        should_trigger_1 = monitor._should_trigger_agent(trading_pair, current_price)
        print(f"     首次触发结果: {should_trigger_1}")
        
        if should_trigger_1:
            print("     ✅ 首次触发成功")
            print(f"     触发次数: {monitor._trigger_counts[trading_pair]}")
            print(f"     最后触发时间: {monitor._last_trigger_times[trading_pair]}")
        else:
            print("     ❌ 首次触发失败")
            return False
        
        print("\n   测试立即重复触发（应该被阻止）:")
        should_trigger_2 = monitor._should_trigger_agent(trading_pair, current_price + 50)
        print(f"     重复触发结果: {should_trigger_2}")
        
        if not should_trigger_2:
            print("     ✅ 重复触发被正确阻止")
        else:
            print("     ❌ 重复触发应该被阻止")
            return False
        
        print("✅ 综合触发逻辑验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 综合触发逻辑验证失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🔧 代码修复验证测试")
    print("="*60)
    print("验证内容：")
    print("1. 代码语法正确性")
    print("2. 成交量分析修复")
    print("3. 波动率计算修复")
    print("4. 时间窗口限制改进")
    print("5. 配置调整")
    print("6. 综合触发逻辑")
    print("")
    
    test_results = []
    
    # 执行所有测试
    tests = [
        ("代码语法正确性", test_code_syntax),
        ("成交量分析修复", test_volume_analysis_fix),
        ("波动率计算修复", test_volatility_calculation_fix),
        ("时间窗口限制改进", test_time_window_improvement),
        ("配置调整", test_config_adjustment),
        ("综合触发逻辑", test_comprehensive_trigger_logic)
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "="*60)
    print("📊 代码修复验证测试总结")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有代码修复验证通过！")
        print("🎯 修复内容：")
        print("   ✅ 修复了成交量分析代码错误")
        print("   ✅ 删除了重复的波动率计算方法")
        print("   ✅ 改进了时间窗口限制实现")
        print("   ✅ 调整了配置参数为更合理的值")
        print("   ✅ 验证了综合触发逻辑正常工作")
        print("\n🚀 价格监控触发逻辑现在已达到生产级别质量！")
        return 0
    else:
        print(f"\n⚠️ {total-passed}个测试失败，需要进一步检查")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
