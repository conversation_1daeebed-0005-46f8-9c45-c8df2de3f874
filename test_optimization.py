#!/usr/bin/env python3
"""
优化效果测试脚本
用于验证系统优化后的性能和功能
"""

import sys
import time
import json
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent / "src"))

from gold_agents.config.config_manager import config_manager
from gold_agents.tools.strategy_tool import StrategyTool
from gold_agents.tools.base_tool import OptimizedBaseTool, tool_registry
from gold_agents.crew import GoldAgents


def test_config_manager():
    """测试配置管理器"""
    print("🔧 测试配置管理器...")
    
    try:
        # 测试API配置
        api_config = config_manager.get_api_config()
        print(f"✅ API配置加载成功 - 环境: {api_config.environment}")
        
        # 测试风险配置
        risk_config = config_manager.get_risk_config()
        print(f"✅ 风险配置加载成功 - 最大杠杆: {risk_config.max_leverage}")
        
        # 测试数据库配置
        db_config = config_manager.get_database_config()
        print(f"✅ 数据库配置加载成功 - 主机: {db_config.host}")
        
        # 测试配置路径访问
        env = config_manager.get_config('trader.environment', 'test')
        print(f"✅ 配置路径访问成功 - 环境: {env}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {str(e)}")
        return False


def test_strategy_tool():
    """测试策略工具"""
    print("\n📈 测试策略工具...")
    
    try:
        tool = StrategyTool()
        
        # 测试获取最新策略
        result = tool.run(action="get_latest", trading_pair="BTC", timeframe="1h")
        print(f"✅ 获取最新策略成功 - 状态: {result['status']}")
        
        if result['status'] == 'success' and result['strategy']:
            strategy_id = result['strategy']['id']
            
            # 测试策略验证
            validation_result = tool.run(action="validate", strategy_id=strategy_id)
            print(f"✅ 策略验证成功 - 有效性: {validation_result['is_valid']}")
        
        # 测试性能指标
        metrics = tool.get_metrics()
        print(f"✅ 性能指标获取成功 - 调用次数: {metrics['metrics']['call_count']}")
        
        # 测试健康检查
        health = tool.health_check()
        print(f"✅ 健康检查成功 - 状态: {health['status']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 策略工具测试失败: {str(e)}")
        return False


def test_optimized_base_tool():
    """测试优化基类"""
    print("\n🛠️ 测试优化基类...")
    
    try:
        # 创建测试工具
        class TestTool(OptimizedBaseTool):
            name = "TestTool"
            description = "测试工具"
            
            def _run(self, **kwargs):
                # 模拟一些处理时间
                time.sleep(0.1)
                return self._format_response("success", {"test": "data"}, "测试成功")
        
        tool = TestTool()
        
        # 测试工具执行
        result = tool.run(test_param="test_value")
        print(f"✅ 工具执行成功 - 状态: {result['status']}")
        
        # 测试性能监控
        metrics = tool.get_metrics()
        print(f"✅ 性能监控成功 - 平均响应时间: {metrics['metrics']['avg_response_time']:.3f}s")
        
        # 测试健康检查
        health = tool.health_check()
        print(f"✅ 健康检查成功 - 状态: {health['status']}")
        
        # 测试工具注册表
        tool_registry.register_tool(tool)
        registered_tool = tool_registry.get_tool("TestTool")
        print(f"✅ 工具注册成功 - 注册状态: {registered_tool is not None}")
        
        return True
        
    except Exception as e:
        print(f"❌ 优化基类测试失败: {str(e)}")
        return False


def test_gold_agents():
    """测试Gold Agents系统"""
    print("\n🤖 测试Gold Agents系统...")
    
    try:
        # 创建系统实例
        gold_agents = GoldAgents()
        print("✅ Gold Agents实例创建成功")
        
        # 测试健康检查
        health_status = gold_agents.health_check()
        print(f"✅ 系统健康检查完成 - 整体状态: {health_status['overall_status']}")
        
        # 测试性能指标
        metrics = gold_agents.get_crew_metrics()
        print(f"✅ 系统指标获取成功 - 工具数量: {len(metrics.get('tools_metrics', {}))}")
        
        # 测试配置状态
        config_status = metrics.get('config_status', {})
        print(f"✅ 配置状态检查 - API配置: {config_status.get('api_config', False)}")
        
        # 关闭资源
        gold_agents.close()
        print("✅ 资源清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ Gold Agents系统测试失败: {str(e)}")
        return False


def performance_benchmark():
    """性能基准测试"""
    print("\n⚡ 执行性能基准测试...")
    
    try:
        # 测试策略工具性能
        tool = StrategyTool()
        
        # 执行多次调用测试
        start_time = time.time()
        for i in range(10):
            result = tool.run(action="get_latest", trading_pair="BTC", timeframe="1h")
        
        end_time = time.time()
        avg_time = (end_time - start_time) / 10
        
        print(f"✅ 策略工具平均响应时间: {avg_time:.3f}s")
        
        # 获取详细性能指标
        metrics = tool.get_metrics()
        stats = metrics['metrics']
        
        print(f"✅ 总调用次数: {stats['call_count']}")
        print(f"✅ 错误率: {stats['error_rate']:.2%}")
        print(f"✅ 平均响应时间: {stats['avg_response_time']:.3f}s")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能基准测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始优化效果测试...\n")
    
    test_results = []
    
    # 执行各项测试
    tests = [
        ("配置管理器", test_config_manager),
        ("策略工具", test_strategy_tool),
        ("优化基类", test_optimized_base_tool),
        ("Gold Agents系统", test_gold_agents),
        ("性能基准", performance_benchmark)
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "="*50)
    print("📊 测试结果总结")
    print("="*50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！优化效果良好！")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查相关组件")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
