#!/usr/bin/env python3
"""
deque index out of range 错误修复验证测试
验证PriceLevels类中的_update_volatility方法修复
"""

import sys
import time
from pathlib import Path
from datetime import datetime, timedelta
from collections import deque

# 添加项目路径
sys.path.append(str(Path(__file__).parent / "src"))


def test_price_levels_volatility_fix():
    """测试PriceLevels波动率更新修复"""
    print("🔧 测试PriceLevels波动率更新修复...")
    print("="*50)
    
    try:
        from price_monitor import PriceLevels
        
        # 创建PriceLevels实例
        price_levels = PriceLevels()
        print("✅ PriceLevels实例创建成功")
        
        # 测试场景1: 空deque情况（这是导致错误的原因）
        print("\n📋 测试场景1: 空deque情况")
        
        # 添加第一个价格
        price1 = 50000.0
        timestamp1 = datetime.now()
        price_levels.update(price1, timestamp1)
        print(f"   添加第一个价格: {price1}")
        
        # 添加第二个价格（这时会触发波动率计算）
        price2 = 50100.0
        timestamp2 = datetime.now() + timedelta(seconds=30)
        
        print("   添加第二个价格（触发波动率计算）...")
        try:
            price_levels.update(price2, timestamp2)
            print(f"   ✅ 成功添加第二个价格: {price2}")
            print("   ✅ 波动率计算没有出现IndexError")
        except IndexError as e:
            print(f"   ❌ 仍然出现IndexError: {str(e)}")
            return False
        except Exception as e:
            print(f"   ⚠️ 其他错误: {str(e)}")
            # 其他错误可能是正常的，只要不是IndexError
        
        # 测试场景2: 连续更新多个价格
        print("\n📋 测试场景2: 连续更新多个价格")
        
        test_prices = [50200.0, 50150.0, 50300.0, 50250.0, 50400.0]
        base_time = datetime.now()
        
        for i, price in enumerate(test_prices):
            timestamp = base_time + timedelta(seconds=i*60)  # 每分钟一个价格
            try:
                price_levels.update(price, timestamp)
                print(f"   ✅ 价格 {price} 更新成功")
            except IndexError as e:
                print(f"   ❌ 价格 {price} 更新失败 - IndexError: {str(e)}")
                return False
            except Exception as e:
                print(f"   ⚠️ 价格 {price} 更新时出现其他错误: {str(e)}")
                # 继续测试，其他错误可能是正常的
        
        # 测试场景3: 跨时间周期更新
        print("\n📋 测试场景3: 跨时间周期更新")
        
        # 跨分钟更新
        future_time = datetime.now() + timedelta(minutes=2)
        try:
            price_levels.update(50500.0, future_time)
            print("   ✅ 跨分钟更新成功")
        except IndexError as e:
            print(f"   ❌ 跨分钟更新失败 - IndexError: {str(e)}")
            return False
        except Exception as e:
            print(f"   ⚠️ 跨分钟更新时出现其他错误: {str(e)}")
        
        # 跨小时更新
        future_hour = datetime.now() + timedelta(hours=1)
        try:
            price_levels.update(50600.0, future_hour)
            print("   ✅ 跨小时更新成功")
        except IndexError as e:
            print(f"   ❌ 跨小时更新失败 - IndexError: {str(e)}")
            return False
        except Exception as e:
            print(f"   ⚠️ 跨小时更新时出现其他错误: {str(e)}")
        
        # 检查波动率数据结构
        print("\n📊 检查波动率数据结构:")
        levels = price_levels.get_levels()
        volatility_data = levels.get('volatility', {})
        
        for period, volatility in volatility_data.items():
            print(f"   {period}: {volatility:.6f}")
        
        print("✅ PriceLevels波动率更新修复测试通过")
        return True
        
    except Exception as e:
        print(f"❌ PriceLevels波动率更新修复测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_kline_message_with_fix():
    """测试K线消息处理（使用修复后的代码）"""
    print("\n📊 测试K线消息处理（使用修复后的代码）...")
    print("="*50)
    
    try:
        from price_monitor import PriceMonitor
        from unittest.mock import Mock, patch
        
        monitor = PriceMonitor()
        trading_pair = "BTC"
        
        # 初始化必要的数据结构
        monitor._price_history[trading_pair] = deque(maxlen=100)
        
        # 模拟数据库管理器
        monitor._db_manager.save_kline_data = Mock()
        monitor._db_manager.save_price_levels = Mock()
        
        # 模拟企业微信发送函数
        with patch('price_monitor.send_wechat_message') as mock_wechat:
            # 构造真实的K线update消息（类似错误日志中的消息）
            kline_message = {
                'action': 'update',
                'arg': {
                    'instType': 'sp',
                    'channel': 'candle1m',
                    'instId': 'BTCUSDT'
                },
                'data': [['1749457680000', '105523.2', '105523.2', '105523.19', '105523.2', '0.450077']],
                'ts': 1749457700298
            }
            
            print("📋 测试真实K线消息处理:")
            print(f"   消息: {kline_message}")
            
            # 模拟触发检查
            monitor._should_trigger_agent = Mock(return_value=False)  # 不触发，只测试数据处理
            
            try:
                # 处理消息 - 这里之前会出现IndexError
                monitor._on_message(kline_message)
                print("   ✅ K线消息处理成功，没有IndexError")
                
                # 验证K线数据是否被保存
                assert monitor._db_manager.save_kline_data.called, "K线数据应该被保存"
                print("   ✅ K线数据已保存到数据库")
                
                # 验证价格历史是否被更新
                assert len(monitor._price_history[trading_pair]) > 0, "价格历史应该被更新"
                print("   ✅ 价格历史已更新")
                
                # 检查价格水平是否正确初始化
                if trading_pair in monitor._price_levels:
                    levels = monitor._price_levels[trading_pair].get_levels()
                    print(f"   ✅ 价格水平已初始化: {len(levels)} 个字段")
                
            except IndexError as e:
                print(f"   ❌ 仍然出现IndexError: {str(e)}")
                return False
            except Exception as e:
                print(f"   ⚠️ 其他错误（可能正常）: {str(e)}")
                # 其他错误可能是正常的，只要不是IndexError
        
        # 测试连续多个K线消息
        print("\n📋 测试连续多个K线消息:")
        
        for i in range(3):
            timestamp = 1749457680000 + i * 60000  # 每分钟一个K线
            price = 105523.2 + i * 0.1
            
            kline_message = {
                'action': 'update',
                'arg': {'instType': 'sp', 'channel': 'candle1m', 'instId': 'BTCUSDT'},
                'data': [[str(timestamp), str(price), str(price+0.1), str(price-0.1), str(price), '0.5']],
                'ts': timestamp + 20000
            }
            
            try:
                monitor._on_message(kline_message)
                print(f"   ✅ K线消息 {i+1} 处理成功")
            except IndexError as e:
                print(f"   ❌ K线消息 {i+1} 出现IndexError: {str(e)}")
                return False
            except Exception as e:
                print(f"   ⚠️ K线消息 {i+1} 其他错误: {str(e)}")
        
        print("✅ K线消息处理测试通过")
        return True
        
    except Exception as e:
        print(f"❌ K线消息处理测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_edge_cases():
    """测试边界情况"""
    print("\n🔍 测试边界情况...")
    print("="*50)
    
    try:
        from price_monitor import PriceLevels
        
        # 测试场景1: 快速连续更新
        print("📋 测试快速连续更新:")
        price_levels = PriceLevels()
        
        base_time = datetime.now()
        for i in range(10):
            price = 50000.0 + i
            timestamp = base_time + timedelta(seconds=i)
            
            try:
                price_levels.update(price, timestamp)
                print(f"   ✅ 快速更新 {i+1}/10 成功")
            except IndexError as e:
                print(f"   ❌ 快速更新 {i+1}/10 IndexError: {str(e)}")
                return False
            except Exception as e:
                print(f"   ⚠️ 快速更新 {i+1}/10 其他错误: {str(e)}")
        
        # 测试场景2: 相同时间戳更新
        print("\n📋 测试相同时间戳更新:")
        same_time = datetime.now()
        
        for i in range(3):
            price = 51000.0 + i * 10
            try:
                price_levels.update(price, same_time)
                print(f"   ✅ 相同时间戳更新 {i+1}/3 成功")
            except IndexError as e:
                print(f"   ❌ 相同时间戳更新 {i+1}/3 IndexError: {str(e)}")
                return False
            except Exception as e:
                print(f"   ⚠️ 相同时间戳更新 {i+1}/3 其他错误: {str(e)}")
        
        # 测试场景3: 极端价格变化
        print("\n📋 测试极端价格变化:")
        extreme_prices = [50000.0, 100000.0, 25000.0, 75000.0]
        
        for i, price in enumerate(extreme_prices):
            timestamp = datetime.now() + timedelta(minutes=i)
            try:
                price_levels.update(price, timestamp)
                print(f"   ✅ 极端价格 {price} 更新成功")
            except IndexError as e:
                print(f"   ❌ 极端价格 {price} IndexError: {str(e)}")
                return False
            except Exception as e:
                print(f"   ⚠️ 极端价格 {price} 其他错误: {str(e)}")
        
        print("✅ 边界情况测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 边界情况测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🔧 deque index out of range 错误修复验证测试")
    print("="*60)
    print("测试内容：")
    print("1. PriceLevels波动率更新修复")
    print("2. K线消息处理（使用修复后的代码）")
    print("3. 边界情况测试")
    print("")
    
    test_results = []
    
    # 执行所有测试
    tests = [
        ("PriceLevels波动率更新修复", test_price_levels_volatility_fix),
        ("K线消息处理修复", test_kline_message_with_fix),
        ("边界情况测试", test_edge_cases)
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "="*60)
    print("📊 deque index out of range 错误修复验证测试总结")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 deque index out of range 错误修复成功！")
        print("🔧 修复内容：")
        print("   ✅ 修复了PriceLevels._update_volatility中的IndexError")
        print("   ✅ 添加了deque空检查，避免访问空deque的[-1]索引")
        print("   ✅ 确保K线消息处理不会因为波动率计算而崩溃")
        print("   ✅ 处理了各种边界情况和极端场景")
        print("\n🚀 现在WebSocket K线消息可以正常处理，不会出现IndexError！")
        print("💡 修复原理：")
        print("   - 在访问deque[-1]之前检查deque长度")
        print("   - 如果deque为空，直接append新值而不是更新最后一个值")
        print("   - 保持了原有的时间周期逻辑不变")
        return 0
    else:
        print(f"\n⚠️ {total-passed}个测试失败，需要进一步检查")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
