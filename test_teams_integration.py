#!/usr/bin/env python3
"""
两个团队集成测试 - 检查复盘团队和交易团队的工具代码完整性
"""

import sys
import json
import traceback
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.append(str(Path(__file__).parent / "src"))


def test_review_team_tools():
    """测试复盘团队工具"""
    print("🔄 测试复盘团队工具...")
    print("="*50)
    
    try:
        # 测试复盘结果管理器
        print("📊 测试复盘结果管理器...")
        from review_agents.tools.review_result_manager import ReviewResultManager
        
        manager = ReviewResultManager()
        print("✅ ReviewResultManager 导入成功")
        
        # 测试数据库初始化
        manager.init_db()
        print("✅ 数据库初始化成功")
        
        # 测试保存复盘报告
        test_review_result = {
            'status': 'success',
            'review_period': '2024-01-01_to_2024-01-31_test',
            'data_analysis': {
                'performance_metrics': {
                    'win_rate': 0.65,
                    'profit_factor': 2.1
                }
            },
            'performance_evaluation': {
                'absolute_performance': {
                    'total_return': 0.15,
                    'max_drawdown': 0.08,
                    'sharpe_ratio': 1.5
                }
            },
            'review_result': {
                'key_insights': ['测试洞察1', '测试洞察2'],
                'improvement_suggestions': ['测试建议1', '测试建议2'],
                'strategy_adjustments': [
                    {
                        'type': 'parameter',
                        'component': 'trader',
                        'parameter_name': 'signal_threshold',
                        'old_value': '0.7',
                        'new_value': '0.75',
                        'reason': '测试调整',
                        'expected_impact': '测试影响'
                    }
                ]
            }
        }
        
        report_id = manager.save_review_report(test_review_result)
        print(f"✅ 复盘报告保存成功: {report_id}")
        
        # 测试获取最新报告
        latest_report = manager.get_latest_review_report()
        if latest_report:
            print(f"✅ 获取最新报告成功: {latest_report['report_type']}")
        else:
            print("⚠️ 没有找到复盘报告")
        
        # 测试获取待应用调整
        pending_adjustments = manager.get_pending_strategy_adjustments()
        print(f"✅ 获取待应用调整成功: {len(pending_adjustments)}项")
        
    except Exception as e:
        print(f"❌ 复盘结果管理器测试失败: {str(e)}")
        traceback.print_exc()
        return False
    
    try:
        # 测试交易数据分析器
        print("\n📈 测试交易数据分析器...")
        from review_agents.tools.trading_data_analyzer import TradingDataAnalyzer
        
        analyzer = TradingDataAnalyzer()
        print("✅ TradingDataAnalyzer 导入成功")
        
        # 测试绩效分析
        result = analyzer.run(
            action="analyze_performance",
            start_date="2024-01-01",
            end_date="2024-01-31"
        )
        print(f"✅ 绩效分析测试: {result['status']}")
        
    except Exception as e:
        print(f"❌ 交易数据分析器测试失败: {str(e)}")
        traceback.print_exc()
        return False
    
    try:
        # 测试复盘Agent团队
        print("\n🤖 测试复盘Agent团队...")
        from review_agents.crew import ReviewAgents
        
        review_agents = ReviewAgents()
        print("✅ ReviewAgents 导入成功")
        
        # 测试工具初始化
        if hasattr(review_agents, 'data_analyzer') and hasattr(review_agents, 'result_manager'):
            print("✅ 复盘团队工具初始化成功")
        else:
            print("❌ 复盘团队工具初始化失败")
            return False
        
        review_agents.close()
        
    except Exception as e:
        print(f"❌ 复盘Agent团队测试失败: {str(e)}")
        traceback.print_exc()
        return False
    
    print("🎉 复盘团队工具测试完成！")
    return True


def test_trading_team_tools():
    """测试交易团队工具"""
    print("\n💰 测试交易团队工具...")
    print("="*50)
    
    try:
        # 测试复盘结果查询工具
        print("🔍 测试复盘结果查询工具...")
        from gold_agents.tools.review_result_query_tool import ReviewResultQueryTool
        
        query_tool = ReviewResultQueryTool()
        print("✅ ReviewResultQueryTool 导入成功")
        
        # 测试获取最新报告
        result = query_tool.run(action="get_latest_report", report_type="daily")
        print(f"✅ 获取最新报告测试: {result['status']}")
        
        # 测试获取待应用调整
        result = query_tool.run(action="get_pending_adjustments")
        print(f"✅ 获取待应用调整测试: {result['status']}")
        
        # 测试获取绩效摘要
        result = query_tool.run(action="get_performance_summary", days_back=30)
        print(f"✅ 获取绩效摘要测试: {result['status']}")
        
    except Exception as e:
        print(f"❌ 复盘结果查询工具测试失败: {str(e)}")
        traceback.print_exc()
        return False
    
    try:
        # 测试策略调整工具
        print("\n⚙️ 测试策略调整工具...")
        from gold_agents.tools.strategy_adjustment_tool import StrategyAdjustmentTool
        
        adjustment_tool = StrategyAdjustmentTool()
        print("✅ StrategyAdjustmentTool 导入成功")
        
        # 测试获取最新复盘结果
        result = adjustment_tool.run(action="get_latest_review", report_type="daily")
        print(f"✅ 获取最新复盘结果测试: {result['status']}")
        
        # 测试获取调整建议
        result = adjustment_tool.run(action="get_recommendations")
        print(f"✅ 获取调整建议测试: {result['status']}")
        
    except Exception as e:
        print(f"❌ 策略调整工具测试失败: {str(e)}")
        traceback.print_exc()
        return False
    
    try:
        # 测试交易链管理器
        print("\n🔗 测试交易链管理器...")
        from gold_agents.database.trading_chain_manager import TradingChainManager
        
        chain_manager = TradingChainManager()
        print("✅ TradingChainManager 导入成功")
        
        # 测试数据库初始化
        chain_manager.init_db()
        print("✅ 交易链数据库初始化成功")
        
    except Exception as e:
        print(f"❌ 交易链管理器测试失败: {str(e)}")
        traceback.print_exc()
        return False
    
    try:
        # 测试交易Agent团队
        print("\n🎯 测试交易Agent团队...")
        from gold_agents.crew import GoldAgents
        
        gold_agents = GoldAgents()
        print("✅ GoldAgents 导入成功")
        
        # 检查交易员Agent的工具配置
        trader_agent = gold_agents.trader()
        tool_names = [tool.__class__.__name__ for tool in trader_agent.tools]
        
        print(f"📋 交易员Agent工具列表: {tool_names}")
        
        # 检查是否包含复盘查询工具
        if 'ReviewResultQueryTool' in tool_names:
            print("✅ 交易员已集成复盘查询工具")
        else:
            print("❌ 交易员未集成复盘查询工具")
            return False
        
        # 检查其他必要工具
        required_tools = ['StrategyTool', 'BitgetMarketTool', 'BitgetAccountTool', 'TradingRulesEngine']
        missing_tools = []
        
        for required_tool in required_tools:
            if not any(required_tool in tool_name for tool_name in tool_names):
                missing_tools.append(required_tool)
        
        if missing_tools:
            print(f"❌ 交易员缺少必要工具: {missing_tools}")
            return False
        else:
            print("✅ 交易员工具配置完整")
        
        gold_agents.close()
        
    except Exception as e:
        print(f"❌ 交易Agent团队测试失败: {str(e)}")
        traceback.print_exc()
        return False
    
    print("🎉 交易团队工具测试完成！")
    return True


def test_teams_integration():
    """测试两个团队的集成"""
    print("\n🔄 测试两个团队的集成...")
    print("="*50)
    
    try:
        # 模拟完整的数据流
        print("📊 模拟完整数据流...")
        
        # 1. 复盘团队生成报告
        from review_agents.tools.review_result_manager import ReviewResultManager
        
        manager = ReviewResultManager()
        
        # 保存一个测试报告
        test_report = {
            'status': 'success',
            'review_period': '2024-01-01_to_2024-01-31_integration_test',
            'review_result': {
                'key_insights': ['集成测试洞察'],
                'improvement_suggestions': ['集成测试建议'],
                'strategy_adjustments': [
                    {
                        'type': 'parameter',
                        'component': 'trader',
                        'parameter_name': 'test_param',
                        'old_value': '0.5',
                        'new_value': '0.6',
                        'reason': '集成测试',
                        'expected_impact': '测试影响'
                    }
                ]
            }
        }
        
        report_id = manager.save_review_report(test_report)
        print(f"✅ 复盘团队生成报告: {report_id}")
        
        # 2. 交易团队查询报告
        from gold_agents.tools.review_result_query_tool import ReviewResultQueryTool
        
        query_tool = ReviewResultQueryTool()
        
        # 查询最新报告
        latest_report = query_tool.run(action="get_latest_report")
        
        if latest_report['status'] == 'success':
            print("✅ 交易团队成功查询复盘报告")
            print(f"   报告ID: {latest_report['report']['id']}")
            print(f"   报告类型: {latest_report['report']['report_type']}")
        else:
            print(f"❌ 交易团队查询报告失败: {latest_report.get('message', 'unknown')}")
            return False
        
        # 查询待应用调整
        pending_adjustments = query_tool.run(action="get_pending_adjustments")
        
        if pending_adjustments['status'] == 'success':
            print(f"✅ 交易团队成功查询待应用调整: {pending_adjustments['total_adjustments']}项")
        else:
            print(f"❌ 交易团队查询调整失败: {pending_adjustments.get('message', 'unknown')}")
            return False
        
        # 3. 测试策略调整应用
        from gold_agents.tools.strategy_adjustment_tool import StrategyAdjustmentTool
        
        adjustment_tool = StrategyAdjustmentTool()
        
        # 获取调整建议
        recommendations = adjustment_tool.run(action="get_recommendations")
        
        if recommendations['status'] == 'success':
            print("✅ 交易团队成功获取调整建议")
            rec_count = len(recommendations.get('recommendations', []))
            print(f"   建议数量: {rec_count}")
        else:
            print(f"❌ 获取调整建议失败: {recommendations.get('message', 'unknown')}")
        
        print("🎉 两个团队集成测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 两个团队集成测试失败: {str(e)}")
        traceback.print_exc()
        return False


def test_database_connectivity():
    """测试数据库连接"""
    print("\n🗄️ 测试数据库连接...")
    print("="*30)
    
    try:
        # 测试复盘数据库
        from review_agents.tools.review_result_manager import ReviewResultManager
        
        manager = ReviewResultManager()
        manager.init_db()
        print("✅ 复盘数据库连接成功")
        
        # 测试交易链数据库
        from gold_agents.database.trading_chain_manager import TradingChainManager
        
        chain_manager = TradingChainManager()
        chain_manager.init_db()
        print("✅ 交易链数据库连接成功")
        
        # 测试查询工具数据库连接
        from gold_agents.tools.review_result_query_tool import ReviewResultQueryTool
        
        query_tool = ReviewResultQueryTool()
        result = query_tool.run(action="get_latest_report")
        print(f"✅ 查询工具数据库连接成功: {result['status']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {str(e)}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 两个团队集成测试开始")
    print("="*60)
    print("测试内容：")
    print("1. 复盘团队工具完整性")
    print("2. 交易团队工具完整性") 
    print("3. 两个团队数据交互")
    print("4. 数据库连接测试")
    print("")
    
    test_results = []
    
    # 执行所有测试
    tests = [
        ("数据库连接", test_database_connectivity),
        ("复盘团队工具", test_review_team_tools),
        ("交易团队工具", test_trading_team_tools),
        ("团队集成", test_teams_integration)
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "="*60)
    print("📊 两个团队集成测试总结")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有测试通过！两个团队已完全集成")
        print("🎯 系统状态：")
        print("   ✅ 复盘团队：独立运行，生成复盘报告")
        print("   ✅ 交易团队：可查询复盘结果，应用策略调整")
        print("   ✅ 数据流：复盘→保存→查询→调整→应用")
        print("   ✅ 数据库：MySQL完整存储和查询")
        print("\n🚀 系统已准备好运行！")
        return 0
    else:
        print(f"\n⚠️ {total-passed}个测试失败，请检查相关组件")
        print("🔧 常见问题排查：")
        print("   1. 检查数据库连接配置")
        print("   2. 确认所有依赖包已安装")
        print("   3. 验证文件路径和导入")
        print("   4. 检查数据库表是否正确创建")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
