以下是结合你的需求以及参考论文《TradingAgents: Multi-Agents LLM Financial Trading Framework》的优化版本，系统专注于**策略分析和信号生成**，不涉及实际交易执行，所有Agent职责聚焦于不同角度的信息分析与策略输出。

---

# ✅ 现货黄金多智能体策略分析系统优化方案

本系统采用 **CrewAI** 框架与 **Grok3** 模型搭建，基于以下三类数据源：
1. **行情数据**（K线、指标）
2. **情绪/新闻数据**（来自金十网）
3. **宏观政策信息**（央行动态、国际地缘政治）

最终目的是通过多角色Agent合作生成**黄金现货策略建议**，并通过**企业微信**推送到终端用户。

---

## 🧠 Agent角色结构（基于论文结构优化）

### 1. 技术面分析师 (Technical Analyst Agent)
- **职责**：
  - 分析多周期K线（15m, 1h, 4h, 1d）
  - 基于 TA-Lib 指标生成趋势判断
  - 识别关键信号如突破、背离、支撑阻力
- **交付格式**：
  ```json
  {
    "role": "Technical Analyst",
    "trend_15m": "bullish",
    "trend_1h": "bullish",
    "trend_4h": "neutral",
    "signal_strength": 0.82,
    "support": 2335.0,
    "resistance": 2360.0
  }
  ```
- **提示词**：
  ```
  你是一个技术分析师，请根据以下技术指标与K线数据判断多个周期的趋势方向（bullish/bearish/neutral），并输出支撑阻力区间与信号强度（0~1）。
  ```

---

### 2. 基本面分析师 (Fundamental Analyst Agent)
- **职责**：
  - 解读国际宏观经济数据、CPI、就业、利率等
  - 判断数据与预期偏差对黄金的可能影响
- **交付格式**：
  ```json
  {
    "role": "Fundamental Analyst",
    "macro_impact": "bullish",
    "drivers": ["美联储延迟加息", "美国CPI低于预期"],
    "impact_score": 0.74
  }
  ```
- **提示词**：
  ```
  你是一个基本面分析专家，请分析以下宏观数据与市场预期之间的关系，判断其对现货黄金的中短期影响方向（bullish/bearish/neutral）及强度（0~1）。
  ```

---

### 3. 情绪分析师 (Sentiment Analyst Agent)
- **职责**：
  - 抽取金十快讯情绪信号
  - 使用BERT/VADER或打分模型进行情感倾向判断
- **交付格式**：
  ```json
  {
    "role": "Sentiment Analyst",
    "sentiment": "positive",
    "sources": ["金十快讯：避险情绪升温"],
    "score": 0.68
  }
  ```
- **提示词**：
  ```
  你是一个金融情绪分析师，请基于以下新闻文本，提取市场情绪，并给出情绪倾向（positive/negative/neutral）与情绪强度评分。
  ```

---

### 4. 政策分析师 (Policy Analyst Agent)
- **职责**：
  - 分析央行/地缘新闻
  - 判断其对黄金价格的长期潜在影响
- **交付格式**：
  ```json
  {
    "role": "Policy Analyst",
    "policy_trend": "bullish",
    "summary": "中国央行持续增持黄金，美元长期压力增加",
    "impact_level": 0.61
  }
  ```
- **提示词**：
  ```
  你是政策分析专家，请提取以下政策新闻对黄金市场的可能影响，并输出其方向与影响强度。
  ```

---

### 5. 风险评估师 (Risk Evaluator Agent)
- **职责**：
  - 评估当前波动率、突发事件概率、整体策略风险敞口
  - 给出策略适用性或建议观望
- **交付格式**：
  ```json
  {
    "role": "Risk Evaluator",
    "volatility": "high",
    "risk_flag": true,
    "recommendation": "wait",
    "reason": "地缘政治不确定性剧烈波动"
  }
  ```
- **提示词**：
  ```
  你是一个金融风险评估专家，请根据波动率、新闻影响等判断当前是否适合采取交易策略，并输出建议。
  ```

---

### 6. 策略整合师 (Strategy Synthesizer Agent)
- **职责**：
  - 汇总以上所有Agent的结果
  - 判断是否形成一致信号，并输出买卖策略与信心
- **交付格式**：
  ```json
  {
    "role": "Strategy Synthesizer",
    "final_decision": "buy",
    "entry": 2352.0,
    "take_profit": 2370.0,
    "stop_loss": 2332.0,
    "confidence": 0.81,
    "reasoning": "技术与基本面一致看多，情绪积极"
  }
  ```
- **提示词**：
  ```
  请整合以下Agent输出，判断是否给出买/卖/观望建议，并输出进场、止盈、止损点与信心值。请列出形成策略的主要依据。
  ```

---

### 7. 推送机器人 (WeCom Bot Agent)
- **职责**：将策略推送到企业微信
- **格式**：
  ```
  🟡 黄金策略提醒  
  ⏰ 时间：2025-04-23 14:00  
  📊 趋势：上涨（1h, 4h）  
  💬 情绪：积极  
  🏛️ 政策：中国增持黄金  
  ✅ 策略建议：Buy @ 2352  
  🎯 止盈：2370，止损：2332  
  ```

---

## 🔄 数据流程图（简化）

```mermaid
flowchart TD
  A[行情数据] --> TA[技术分析师]
  B[新闻快讯] --> SA[情绪分析师]
  C[宏观数据] --> FA[基本面分析师]
  D[政策新闻] --> PA[政策分析师]
  E[波动率指标] --> RA[风险评估师]

  TA --> SS[策略整合师]
  SA --> SS
  FA --> SS
  PA --> SS
  RA --> SS
  SS --> WB[企业微信推送]
```

---

如需我帮你输出 CrewAI 的角色注册代码或 JSON 模板部署这套 Agent 系统，也可以继续说！我们可以一步步搭建起来。