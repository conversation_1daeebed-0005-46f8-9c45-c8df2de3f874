import re  # 导入 re 模块
import requests
import json
from typing import List, Dict
from urllib.parse import quote


class JinseAPIClient:
    def __init__(self):
        self.base_url = "https://api.jinse.cn/noah/v2/lives"
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Referer": "https://www.jinse.cn/",
            "X-Requested-With": "XMLHttpRequest"
        }
        self.id = 0
        self.bottom_id = 0
        self.has_more = True

    @classmethod
    def get_news(cls):
        # 创建实例进行操作
        instance = cls()
        hot_news = instance.hot_news()
        today_events = instance.get_today_events()
        return {
            "hot_news": hot_news,
            "today_events": today_events
        }


    def hot_news(self, max_pages=1) -> List[Dict]:
        """获取全部分页数据"""
        all_news = []
        page = 1

        while self.has_more and page <= max_pages:
            try:
                response = self._fetch_page()
                self._update_cursor(response)
                all_news.extend(self._parse_items(response))
                page += 1
            except Exception as e:
                print(f"获取第{page}页失败: {str(e)}")
                break

        return all_news

    def _fetch_page(self) -> requests.Response:
        """发送API请求"""
        params = {
            "limit": 20,
            "reading": "false",
            "source": "web",
            "flag": "down",
            "id": self.id,
            "category": 0
        }
        response = requests.get(
            self.base_url,
            headers=self.headers,
            params=params,
            timeout=10
        )
        # try:
        #     # 使用 json.dumps 进行格式化打印
        #     print(json.dumps(response.json(), indent=4))
        # except json.JSONDecodeError as e:
        #     print(f"解析 JSON 失败: {str(e)}")
        return response

    def _update_cursor(self, response: requests.Response):
        """更新分页游标"""
        if response is None:
            self.has_more = False
            return
        try:
            data = response.json()  # 使用 response.json() 解析 JSON 数据
            self.has_more = len(data["list"]) > 0  # 修正对 data 的引用
            if self.has_more:
                self.bottom_id = data["list"][-1]["lives"][-1]["id"]  # 修正对 data 的引用
        except (KeyError, json.JSONDecodeError) as e:
            print(f"解析数据失败: {str(e)}")
            self.has_more = False

    def _parse_items(self, response: requests.Response) -> List[Dict]:
        """解析数据项"""
        if response is None:
            return []
        try:
            data = response.json()  # 使用 response.json() 解析 JSON 数据
        except json.JSONDecodeError as e:
            print(f"解析 JSON 失败: {str(e)}")
            return []
        return [{
            # "id": live["id"],
            "content": live["content"],
            "timestamp": live["created_at_zh"],
            "upvotes": live["up_counts"],
            # "images": [img["url"] for img in live.get("images", [])],
            # "related_coins": self._extract_coins(live["content"] )
        } for day in data["list"] for live in day["lives"]]  # 修正对 data 的引用

    def get_today_events(self, page=1, limit=10) -> List[Dict]:
        """获取今日大事件"""
        try:
            response = self._fetch_today_events(page, limit)
            data = response.json()
            return self._parse_today_events(data)
        except Exception as e:
            print(f"获取今日大事件失败: {str(e)}")
            return []

    def _fetch_today_events(self, page: int, limit: int) -> requests.Response:
        """发送获取今日大事件的API请求"""
        url = "https://api.jinse.cn/noah/v2/chief/editor/recommends"
        params = {
            "page": page,
            "limit": limit
        }
        return requests.get(
            url,
            headers=self.headers,
            params=params,
            timeout=10
        )

    def _parse_today_events(self, data: dict) -> List[Dict]:
        """解析今日大事件的数据项"""
        return [{
            # "id": event["id"],
            "title": event["title"],
            "content_type": event["content_type"],
            # "jump_url": event["jump_url"],
            # "jump_type": event["jump_type"],
            # "author": event["author"],
            "is_hot": event["is_hot"],
            # "cover": event["cover"],
            # "refreshed_at": event["refreshed_at"],
            # "published_at": event["published_at"],
            "short_title": event["short_title"]
        } for event in data["data"]["list"]]

    def _extract_coins(self, content: str) -> List[str]:
        """提取关联币种"""
        return list(set(re.findall(r"\$([A-Z]{3,5})", content)))

if __name__ == '__main__':
    client = JinseAPIClient()
    today = client.get_news()
    print(today)
