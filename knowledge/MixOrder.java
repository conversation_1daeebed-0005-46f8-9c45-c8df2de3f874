package com.auto.bitget.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.auto.bitget.bean.MonitoringAlert;
import com.auto.bitget.bean.Ticker;
import com.bitget.openapi.common.client.BitgetRestClient;
import com.bitget.openapi.dto.response.ResponseResult;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/14 0:06
 */
@Slf4j
@Configuration
public class MixOrder extends BaseClient{

    public BitgetRestClient getRestClient(String env ) {
        return  BitgetRestClient.builder().configuration("test".equals(env) ? parameterTest : parameter).build();
    }


    public Object fundingTime(String symbol,String env )  {
        ResponseResult result = null;
        try {
            Map<String, String> paramMap = Maps.newHashMap();
            paramMap.put("symbol", symbol);
            result = getRestClient(env).bitget().v2().request().get("/api/mix/v1/market/funding-time", paramMap);
            log.info("fundingTime,回复：{}",JSON.toJSONString(result));
        } catch (Exception e) {
            log.error(e.getMessage());
            result.setMsg(e.getMessage());
        }
        return JSON.toJSONString(result) ;
    }

    /**
     * 计划单
     * @param paramMap
     * @return
     * @
     */
    public ResponseResult placePlan(Map<String,String> paramMap){
        ResponseResult responseResult = new ResponseResult();
        try {
            responseResult = getRestClient(paramMap.get("env")).bitget().v1().mixOrder().placePlan(paramMap);
            log.info("计划单,回复：{}",JSON.toJSONString(responseResult));
        } catch (Exception e) {
            log.error(e.getMessage());
            responseResult.setMsg(e.getMessage());
        }
        return responseResult;
    }

    public ResponseResult cancelPlan(Map<String,String> paramMap){
        ResponseResult responseResult = new ResponseResult();
        try {
            responseResult = getRestClient(paramMap.get("env")).bitget().v1().mixOrder().cancelPlan(paramMap);
            System.out.println(JSON.toJSONString(responseResult));
            log.info("撤销计划单,回复：{}",JSON.toJSONString(responseResult));
        } catch (Exception e) {
            log.error(e.getMessage());
            responseResult.setMsg(e.getMessage());
            
        }
        return responseResult;
    }

    public ResponseResult placeOrder(Map<String,String> paramMap){
        ResponseResult responseResult = new ResponseResult();
        try {
            responseResult = getRestClient(paramMap.get("env")).bitget().v1().mixOrder().placeOrder(paramMap);
            System.out.println();
            String result = JSON.toJSONString(responseResult);
            log.info("下单,回复：{}",result);
            CustomRobotGroupMessage.sendMessage(result);
        } catch (Exception e) {
            log.error(e.getMessage());
            responseResult.setMsg(e.getMessage());
        }
        return responseResult;
    }

    public ResponseResult cancelOrder(Map<String,String> paramMap){
        ResponseResult responseResult = new ResponseResult();
        try {
            responseResult = getRestClient(paramMap.get("env")).bitget().v1().mixOrder().cancelOrder(paramMap);
            log.info("取消订单,回复：{}",JSON.toJSONString(responseResult));
        } catch (Exception e) {
            log.error("取消订单异常：{} ",e.getMessage());
            responseResult.setMsg(e.getMessage());
        }
        return responseResult;
    }

    /**
     * 查询订单详情
     * @param symbol
     * @param env
     * @param orderId
     * @param clientOid
     * @return
     * @
     */
    public ResponseResult orderDetail(String symbol,String env,String orderId, String clientOid)  {
        ResponseResult result = new ResponseResult();
        try {
            Map<String, String> paramMap = Maps.newHashMap();
            paramMap.put("symbol", symbol);
            paramMap.put("orderId", orderId);
            paramMap.put("clientOid", clientOid);
            log.info("");
            result = getRestClient(env).bitget().v1().request().get("/api/mix/v1/order/detail", paramMap);
            System.out.println(JSON.toJSONString(result));
            log.info("查询订单详情,回复：{}",JSON.toJSONString(result));
        } catch (Exception e) {
            log.error(e.getMessage());
            result.setMsg(e.getMessage());
        }
        return result;
    }

    /**
     * 设置杠杆
     * @param paramMap
     * @return
     * @
     */
    public ResponseResult setLeverage(Map<String,String> paramMap) {
        ResponseResult responseResult = new ResponseResult();
        try {
            responseResult = getRestClient(paramMap.get("env")).bitget().v1().mixAccount().setLeverage(paramMap);
            log.info("设置杠杆,回复：{}",JSON.toJSONString(responseResult));
        } catch (Exception e) {
            log.error(e.getMessage());
            responseResult.setMsg(e.getMessage());
        }
        return responseResult;
    }


    public Ticker getTicker(String symbol,String env )  {
        Ticker data = null ;
        try {
            log.info("币种：{}",symbol);
            Map<String, String> paramMap = Maps.newHashMap();
            paramMap.put("symbol", symbol);
            ResponseResult  result = getRestClient(env).bitget().v1().mixMarket().ticker(paramMap);
            log.info("ticker,回复：{}",JSON.toJSONString(result));
            Object datas = result.getData();
            Ticker javaObject = JSON.toJavaObject(JSON.parseObject(JSON.toJSONString(datas)), Ticker.class);

//            ResponseResult<Ticker> ticker = getRestClient(env).bitget().v1().mixMarket().ticker(paramMap);
//            data = ticker.getData();
            return javaObject;
        } catch (Exception e) {
            log.error(e.getMessage());

        }
        return data;
    }

    public JSON marketCandles(String symbol,String env ){
        try {
            Map<String, String> paramMap = Maps.newHashMap();
            paramMap.put("symbol", symbol);
            paramMap.put("granularity", "15m");
            paramMap.put("limit", "1000");
            paramMap.put("productType", "usdt-futures");
            paramMap.put("startTime", "1733632162000");
            paramMap.put("endTime", "1733664614959");
            ResponseResult result = getRestClient(env).bitget().v1().mixMarket().candles(paramMap);
            log.info("ticker,回复：{}",JSON.toJSONString(result));
            Object datas = result.getData();
            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(datas));
            return jsonObject;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


}
