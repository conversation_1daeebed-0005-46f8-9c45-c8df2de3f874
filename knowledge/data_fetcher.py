import sys
import os
import pandas as pd
import ccxt
import requests
import time
from datetime import datetime, timedelta
from typing import List, Dict
import json  # 添加json导入，用于处理缓存文件

BINANCE_API_KEY = "68DX8UkNJEFAusY9nVE9O99G8khVV8roE1wEgXHMgxonlfgX5pd5Cw9cqYsGAHjG"
BINANCE_SECRET = "gNNWQdZ7wrRQc7OqbSU2r7HiKuf5SUN1nMQsnjSqaYuwogzSgoVpXJvJUuzwlTUQ"
BASE_URL = "https://data-api.binance.vision"
symbol = ['BTCUSDT']
periods = ['15m','1h', '4h','8h','12h','1d']
# periods = ['15m']
limit = 60
 

class BinanceAPI:
    BASE_URL = "https://data-api.binance.vision"
    CACHE_DIR = "cache"  # 新增缓存目录常量

    @classmethod
    def get_cached_klines(cls, symbol: str, periods: List[str]) -> Dict[str, List[Dict]]:
        """带缓存的多周期K线数据获取"""
        os.makedirs(cls.CACHE_DIR, exist_ok=True)
        results = {}
        
        for period in periods:
            cache_file = os.path.join(cls.CACHE_DIR, f"{symbol}_{period}.cache")
            data = None
            
            # 检查缓存是否存在且有效
            if os.path.exists(cache_file):
                with open(cache_file, 'r') as f:
                    cached = json.load(f)
                    if cls._is_cache_valid(cached['timestamp'], period):
                        data = cached['data']
            
            # 需要更新数据的情况
            if not data:
                interval_seconds = cls._period_to_seconds(period)
                raw_data = cls.fetch_klines(symbol, period, limit)
                data = cls._process_raw_data(raw_data)
                
                # 获取K线的最新时间作为缓存时间戳
                latest_kline_time = data[-1]['timestamp'] / 1000  # 转换为秒
                
                # 保存缓存（包含时间戳）
                with open(cache_file, 'w') as f:
                    json.dump({
                        'timestamp': latest_kline_time,  # 使用K线的最新时间
                        'data': data
                    }, f)
            
            results[period] = data
        return results

    @classmethod
    def _is_cache_valid(cls, cache_timestamp: float, period: str) -> bool:
        """检查缓存是否在有效期内"""
        cache_time = datetime.fromtimestamp(cache_timestamp)
        current_time = datetime.now()
        interval = cls._period_to_seconds(period)
        
        # 计算缓存过期时间，基于K线的最新时间加上周期时间
        cache_expire_time = cache_time + timedelta(seconds=interval)
        print(f"缓存过期时间：{cache_expire_time}")
        # 判断当前时间是否大于缓存过期时间
        return current_time <= cache_expire_time

    @staticmethod
    def should_update_cache(latest_kline_time, interval):
        """判断缓存是否需要更新"""
        current_time = datetime.now()
        cache_expire_time = latest_kline_time + timedelta(seconds=interval)
        return current_time > cache_expire_time

    @classmethod
    def fetch_klines(cls, symbol: str, interval: str, limit: int):
        """实际调用API获取数据的方法"""
        url = f"{cls.BASE_URL}/api/v3/klines"
        print(url)
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }
        response = requests.get(url, params=params)
        response.raise_for_status()
        print("-------------")
        print(response.json())
        print("------------")
        return response.json()

    @staticmethod
    def _period_to_seconds(period: str) -> int:
        """转换周期到秒数"""
        period_map = {
            '15m': 900,
            '1h': 3600,
            '2h': 7200,
            '4h': 14400,
            '8h': 28800,
            '12h': 43200,
            '1d': 86400
        }
        return period_map.get(period, 3600)

    @staticmethod
    def _process_raw_data(raw_data: list) -> List[Dict]:
        """处理原始数据为标准化格式"""
        return [{
            'timestamp': item[0],
            'open': float(item[1]),
            'high': float(item[2]),
            'low': float(item[3]),
            'close': float(item[4]),
            'volume': float(item[5])
        } for item in raw_data]



# 修复3：修正缩进错误
if __name__ == '__main__':
    data = BinanceAPI.get_cached_klines("BTCUSDT", periods)
    print(data)
